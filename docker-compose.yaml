---
version: '3.8'
networks:
  # Taken from localstack docs:
  # - https://docs.localstack.cloud/references/network-troubleshooting/endpoint-url/
  ls:
    ipam:
      config:
        - subnet: ********/24
services:
  base: &base
    image: nexus-test:latest
    volumes:
      - ./pyproject.toml:/var/task/pyproject.toml
      - ./src/nexus:/var/task/nexus
      - ./tests:/var/task/tests
    env_file: [.env.test]
  dynamodb:
    command: -jar DynamoDBLocal.jar -sharedDb -port 8001 -dbPath ./data
    image: amazon/dynamodb-local:latest
    container_name: dynamodb
    ports: [8001:8001]
    volumes: [./docker/dynamodb:/home/<USER>/data]
    working_dir: /home/<USER>
    dns: [*********]
    networks: [ls]
  dynamodb-setup:
    # script to create table in dynamodb local
    <<: *base
    depends_on: [dynamodb]
    links: [dynamodb]
    entrypoint: python tests/nexus/utils/ddb_setup.py
    dns: [*********]
    networks: [ls]
  localstack:
    image: localstack/localstack:3.6
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # initialisation hook to create infra
      #  - https://docs.localstack.cloud/references/init-hooks/#usage-example
      - ./bin/local_dev/localstack_infra_setup.sh:/etc/localstack/init/ready.d/init-aws.sh
    ports: [4566:4566]
    environment:
      - AWS_DEFAULT_REGION=us-west-2
      - DEBUG=${DEBUG:-0}
      - SERVICES=sts, sns, sqs
    networks:
      ls:
        ipv4_address: *********
  mock-api:
    <<: *base
    ports: [8002:8002]
    entrypoint: python tests/nexus/utils/mock_api.py
    dns: [*********]
    networks: [ls]
  nexus:
    <<: *base
    ports: [8000:8000]
    depends_on: [dynamodb-setup, localstack, mock-api]
    tty: true
    entrypoint: uvicorn nexus.main:app
    command: --host 0.0.0.0 --port 8000 --workers 1 --reload
    dns: [*********]
    networks: [ls]
  nexus-debug:
    <<: *base
    ports: [8000:8000, 5678:5678]
    depends_on: [dynamodb-setup, localstack, mock-api]
    tty: true
    entrypoint: python
    command: >
      -m debugpy
      --listen 0.0.0.0:5678
      --wait-for-client
      -m uvicorn nexus.main:app
      --host 0.0.0.0 --port 8000 --reload --workers 1
    dns: [*********]
    networks:
      - ls

  #-----------------------------------------------
  # TESTING
  #-----------------------------------------------
  nexus-test:
    <<: *base
    tty: true
    entrypoint: python -m pytest
