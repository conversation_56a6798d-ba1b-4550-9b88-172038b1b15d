<!-- Text below this line was autogenerated by VDG v1.3 on 2025-04-02 11:12:15.018800, manual edits may be lost in the future, please make all manual edits above this comment -->

## Instantiation Example

Below is an example instantiation of this module including all required input variables which do not have default values.
Additional non-required variables may also be available.

```
module "persistence" {
    source       = "citizen.devops.vectra-svc.ai/<REPO>/<MODULE>/published"
    version      = "<VERSION>"
    service_name = local.service_name
}
```

## Variables

|Variable |Description |Type |Required? |Default |Sensitive? |Nullable? |
|--- |--- |--- |--- |--- |--- |--- |
|`service_name` |Name of the stack being deployed with the prefix appended e.g main-nexus |`${string}` |True |n/a |False |True |
|`point_in_time_recovery` |Enable/disable DynamoDB point in time recovery feature |`${bool}` |False |`False` |False |True |
|`deletion_protection_enabled` |Enable/disable DynamoDB deletion protection feature |`${bool}` |False |`False` |False |True |

## Outputs

<table>
<tr>
<th> Output </th> <th> Description </th><th> Value </th><th> Sensitive? </th>
</tr>
<tr><td>

`unified` </td><td></td><td>

```json
{
  "dynamodb_id": "${aws_dynamodb_table.dynamodb.id}"
}
```

</td><td>False</td></tr>
</table>
