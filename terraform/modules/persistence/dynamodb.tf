resource "aws_dynamodb_table" "dynamodb" {
  name                        = var.service_name
  billing_mode                = "PAY_PER_REQUEST"
  deletion_protection_enabled = var.deletion_protection_enabled
  hash_key                    = "pk"
  range_key                   = "sk"

  point_in_time_recovery {
    enabled = var.point_in_time_recovery
  }

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "sk"
    type = "S"
  }

  attribute {
    name = "internal_brain_id"
    type = "S"
  }

  attribute {
    # active, inactive
    name = "operating_state"
    type = "S"
  }

  attribute {
    # typically connector_type#connector_state
    # aws,azure-cp,365,etc. # setup,connected,error,deleted
    name = "data"
    type = "S"
  }

  global_secondary_index {
    name            = "operating_state_gsi"
    hash_key        = "operating_state"
    range_key       = "data"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "internal_brain_id_gsi"
    hash_key        = "internal_brain_id"
    range_key       = "operating_state"
    projection_type = "ALL"
  }
}
