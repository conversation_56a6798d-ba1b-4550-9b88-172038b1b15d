variable "service_name" {
  type        = string
  description = "Name of the stack being deployed with the prefix appended e.g main-nexus"
}

variable "point_in_time_recovery" {
  description = "Enable/disable DynamoDB point in time recovery feature"
  type        = bool
  default     = false
}

variable "deletion_protection_enabled" {
  description = "Enable/disable DynamoDB deletion protection feature"
  type        = bool
  default     = false
}