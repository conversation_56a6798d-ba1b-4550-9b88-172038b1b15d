# created by build-tools Terraform.setup.py
variable "basename" {
  type        = string
  description = "the stack basename"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "custom aws tags"
}

variable "dynamodb_id" {
  type        = string
  description = "DDB id for policy assignment"
}

variable "application_namespace" {
  type        = string
  description = "The kubernetes application namespace in the cluster"
}

variable "role_name_prefix" {
  type        = string
  description = "The aws role name created for the service account"
}

#----------------------------------
#  CONSENT SERIVCE
#----------------------------------
variable "consent_service" {
  type = object({
    internal_delete_role_arn = string
  })
}

#----------------------------------
#  LOGFLOW
#----------------------------------
variable "logflow_subscriber_queue_name" {
  type        = string
  description = "The name of the sqs queue that subscribes to nexus topic"
}