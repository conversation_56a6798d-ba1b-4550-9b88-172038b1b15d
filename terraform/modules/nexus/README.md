
# nexus

Todo: fill in contents here

<!-- Text below this line was autogenerated by VDG v1.3 on 2025-04-02 11:12:13.824146, manual edits may be lost in the future, please make all manual edits above this comment -->

## Instantiation Example

Below is an example instantiation of this module including all required input variables which do not have default values.
Additional non-required variables may also be available.

```
module "nexus" {
    source                        = "citizen.devops.vectra-svc.ai/<REPO>/<MODULE>/published"
    version                       = "<VERSION>"
    basename                      = local.basename
    dynamodb_id                   = local.dynamodb_id
    application_namespace         = local.application_namespace
    role_name_prefix              = local.role_name_prefix
    consent_service               = local.consent_service
    logflow_subscriber_queue_name = local.logflow_subscriber_queue_name
}
```

## Variables

|Variable |Description |Type |Required? |Default |Sensitive? |Nullable? |
|--- |--- |--- |--- |--- |--- |--- |
|`basename` |the stack basename |`${string}` |True |n/a |False |True |
|`tags` |custom aws tags |`${map(string)}` |False |`{}` |False |True |
|`dynamodb_id` |DDB id for policy assignment |`${string}` |True |n/a |False |True |
|`application_namespace` |The kubernetes application namespace in the cluster |`${string}` |True |n/a |False |True |
|`role_name_prefix` |The aws role name created for the service account |`${string}` |True |n/a |False |True |
|`consent_service` | |`{'internal_delete_role_arn': ${string}}` |True |n/a |False |True |
|`logflow_subscriber_queue_name` |The name of the sqs queue that subscribes to nexus topic |`${string}` |True |n/a |False |True |

## Outputs

<table>
<tr>
<th> Output </th> <th> Description </th><th> Value </th><th> Sensitive? </th>
</tr>
<tr><td>

`unified` </td><td></td><td>

```json
{
  "iam_role_arn": "${module.k8s-webidentity.unified.iam_role_arn}"
}
```

</td><td>False</td></tr>
</table>

## Submodules

|Module |Source |Version|
|--- |--- |--- |
|`k8s-webidentity` |`citizen.devops.vectra-svc.ai/eksawstools/eks-webidentity/published` |`202403.1.0` |
