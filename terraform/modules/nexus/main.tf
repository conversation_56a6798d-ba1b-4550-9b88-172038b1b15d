module "k8s-webidentity" {
  # https://sourcecode.vectra.io/projects/sp/repos/eks-aws-tools/browse/terraform/modules/eks-webidentity?at=refs%2Ftags%2F202403.1.0
  source                = "citizen.devops.vectra-svc.ai/eksawstools/eks-webidentity/published"
  version               = "202403.1.0"
  application_namespace = var.application_namespace
  basename              = var.basename
  tags                  = var.tags
  role_name_prefix      = var.role_name_prefix
  permissions = [
    {
      sid       = "assumeRole"
      actions   = ["sts:AssumeRole", ]
      resources = ["arn:aws:iam::*:role/ServiceDiscoveryLookup"]
    },
    {
      sid = "NexusDynamoDBWrite"
      actions = [
        "dynamodb:GetItem",
        "dynamodb:Query",
        "dynamodb:UpdateItem",
        "dynamodb:PutItem",
        "dynamodb:Scan"
      ]
      resources = ["${data.aws_dynamodb_table.dynamodb.arn}*"]
    },
    {
      sid       = "AllowAssumeConsentWrite"
      actions   = ["sts:AssumeRole", ]
      resources = [var.consent_service.internal_delete_role_arn]
    },
    {
      sid       = "NexusSnsPublish"
      actions   = ["sns:Publish"]
      resources = ["${aws_sns_topic.nexus.arn}"]
    }
  ]
}