terraform {
  required_version = ">= 1.6.2"
  backend "s3" {
    bucket   = "ingestion-state-files"
    region   = "eu-west-1"                                 # always in this region
    role_arn = "arn:aws:iam::************:role/developers" # saas-ingestion-dev
  }
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
  }
}

provider "aws" {
  region = "us-west-2"
  assume_role {
    role_arn     = "arn:aws:iam::${module.vectra_constants.aws_account_saas-ingestion-dev}:role/developers" # saas-ingestion-dev
    session_name = "terraform"
  }
}

locals {
  service_name = "${var.basename}-nexus"
}


module "persistence" {
  source       = "../../modules/persistence"
  service_name = local.service_name
}

module "nexus" {
  source                        = "../../modules/nexus"
  application_namespace         = "nexus"
  dynamodb_id                   = module.persistence.unified.dynamodb_id
  role_name_prefix              = "nexus-app"
  logflow_subscriber_queue_name = "main-logflow-sensible_listener"
  consent_service = {
    internal_delete_role_arn = module.vectra_constants.unified["consent_service"]["dev"]["us-west-2"].internal_delete_role_arn
  }
  basename = var.basename
}

output "nexus" {
  value = module.nexus.unified
}
