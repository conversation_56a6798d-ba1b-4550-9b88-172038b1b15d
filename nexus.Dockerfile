FROM 375412324511.dkr.ecr.us-west-2.amazonaws.com/cd/python-312-builder:202504.8.0 AS builder
#build tools env
ARG TARGETPLATFORM
#------------------
# Dependencies
#------------------
FROM builder AS dependencies
COPY ./requirements.txt .
RUN --mount=type=ssh --mount=type=secret,id=PIP_CONFIG_SECRET,dst=/root/.config/pip/pip.conf pip3 install --no-cache-dir \
  -r requirements.txt --target $PYTHONLIBS

#------------------
# Dev dependencies
#------------------
FROM builder AS dev-dependencies
COPY ./requirements-dev.txt .
RUN python -m pip install --no-cache-dir -r requirements-dev.txt --target $PYTHONLIBS

#------------------
# Runtime image
#------------------
FROM 375412324511.dkr.ecr.us-west-2.amazonaws.com/cd/python-312-base:202504.8.0 AS runtime
COPY --from=dependencies $PYTHONLIBS $PYTHONLIBS

#------------------
# Test image
#------------------
FROM runtime AS unit-test
COPY --from=dev-dependencies $PYTHONLIBS $PYTHONLIBS
COPY ./pyproject.toml .
COPY .env.test .env
COPY ./src/ .
COPY ./tests/ ./tests
ENTRYPOINT ["python", "-m", "pytest"]
CMD ["tests/nexus/unit", "src/vectra/nexus_client/test"]

#------------------
# Prod image
#------------------
FROM runtime AS prod
# build tools envs
ARG GIT_VERSION
ENV GIT_VERSION=$GIT_VERSION

COPY ./src/ .
CMD ["uvicorn", "nexus.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
