from uuid import UUID

from nexus.models import Connector, ConnectorSize, ConnectorState, OperatingState


def test_serialisation(connector):
    model = connector.model_dump(by_alias=True)

    expected_result = {
        "pk": connector.connector_id,
        "sk": "connector",
        "data": f"{connector.connector_type}#{connector.connector_state}",
        "internal_brain_id": connector.internal_brain_id,
        "operating_state": connector.operating_state,
        "name": connector.name,
        "connector_type": connector.connector_type,
        "connector_state": connector.connector_state,
        "setup_records": connector.setup_records,
        "created_at": connector.created_at,
        "updated_at": connector.updated_at,
        "properties": connector.properties,
        "first_log_received": connector.first_log_received,
        "last_log_received": connector.last_log_received,
        "size": connector.size,
        "last_sequence_id": connector.last_sequence_id,
        "error": connector.error,
    }

    assert model == expected_result


def is_valid_uuid(val: str) -> bool:
    try:
        UUID(str(val))
        return True
    except ValueError:
        return False


def test_default_values():
    connector = Connector(
        name="mock_name",
        connector_type="azure-cp",
        internal_brain_id="mock_brain_id",
        connector_state=ConnectorState.SETUP,
        operating_state=OperatingState.INACTIVE,
        setup_records={"azure-cp": "completed"},
        properties={"foo": "arn:aws:iam::123456789012:role/foo"},
    )

    assert connector.created_at
    assert connector.updated_at
    assert connector.error is None
    assert connector.first_log_received is None
    assert connector.last_log_received is None
    assert connector.size is ConnectorSize.MEDIUM
    test_sid = is_valid_uuid(connector.connector_id)
    assert test_sid is True
    assert connector.last_sequence_id is None


def test_custom_values():
    connector = Connector(
        name="mock_name",
        connector_type="azure-cp",
        internal_brain_id="mock_brain_id",
        connector_state=ConnectorState.SETUP,
        operating_state=OperatingState.INACTIVE,
        first_log_received="2024-11-11T09:36:15.680Z",
        last_log_received="2024-12-01T09:36:15.680Z",
        last_sequence_id="test_sequence_id",
        setup_records={"azure-cp": "completed"},
        properties={"foo": "arn:aws:iam::123456789012:role/foo"},
    )

    assert connector.created_at
    assert connector.updated_at
    assert connector.error is None
    assert connector.first_log_received == "2024-11-11T09:36:15.680Z"
    assert connector.last_log_received == "2024-12-01T09:36:15.680Z"
    assert connector.size is ConnectorSize.MEDIUM
    assert connector.last_sequence_id == "test_sequence_id"
    test_sid = is_valid_uuid(connector.connector_id)
    assert test_sid is True
