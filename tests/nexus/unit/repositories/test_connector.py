import pytest
from pydantic import ValidationError

from nexus.api.exceptions import ConflictException, ForbiddenException, NotFoundException
from nexus.models import Connector, ConnectorState, ConnectorStateUpdate, ConnectorType
from nexus.schemas import Connector<PERSON>ueryPara<PERSON>, ConnectorUpdate
from nexus.utils.config import config


@pytest.mark.asyncio
async def test_persist_connector(dynamodb_async_client, connector_repository, connector):
    """Tests the 'Create a connector' access pattern using the ConnectorRepository."""

    # Given: A connector is persisted
    await connector_repository.persist_connector(connector)

    # When: Retrieve the item and verify it was inserted with the expected attributes.
    response = await dynamodb_async_client.get_item(
        TableName=config.NEXUS_TABLE_NAME, Key={"pk": {"S": connector.connector_id}, "sk": {"S": "connector"}}
    )

    # Then: We can get the connector.
    item = response.get("Item")
    assert item["pk"]["S"] == "mock_id"
    assert item["sk"]["S"] == "connector"
    assert item["data"]["S"] == f"{connector.connector_type}#{connector.connector_state}"


@pytest.mark.asyncio
async def test_get_connector(connector_repository, create_persisted_connector):
    """Get a single connector"""
    await create_persisted_connector()
    connector = await connector_repository.get_connector("mock_id")

    assert isinstance(connector, Connector)
    assert connector.connector_id == "mock_id"


@pytest.mark.asyncio
async def test_get_connector_item_doesnt_exist(connector_repository):
    """Get a single connector that doesn't exist"""

    with pytest.raises(NotFoundException) as err:
        await connector_repository.get_connector("non_existent_connector")

    assert err.typename == "NotFoundException"
    assert err.value.detail == "Connector non_existent_connector cannot be found"


@pytest.mark.asyncio
async def test_get_connectors_for_vui(connector_repository, create_persisted_connector):
    """Tests the 'Get Connectors' access pattern when connectors exist for a given internal_brain_id"""
    await create_persisted_connector(pk="76d6d891-7e5d-47c8-a078-666044c0bbdb", internal_brain_id="saas_1")
    await create_persisted_connector(pk="6b4ba593-5ad3-41d3-a5df-9ce18f40190b", internal_brain_id="saas_1")
    await create_persisted_connector(pk="2c8f1e89-6146-4a1b-b769-682eaaf87e38", internal_brain_id="saas_2")

    response = await connector_repository.get_connectors_for_vui("saas_1")
    assert len(response.connectors) == 2
    assert response.model_fields_set == {"connectors"}

    # pagination check, reduce page limit to 1 to force pagination
    connector_repository.page_limit = 1
    response = await connector_repository.get_connectors_for_vui("saas_1")
    assert len(response.connectors) == 1
    assert response.model_fields_set == {"connectors", "last_evaluated_key"}


@pytest.mark.asyncio
async def test_get_connectors_by_params(connector_repository, create_persisted_connector):
    """Tests the 'Get Connectors' access pattern for getting connectors by given state/type/properties"""
    await create_persisted_connector(
        pk="76d6d891-7e5d-47c8-a078-666044c0bbdb", connector_type="azure-cp", connector_state="deleted"
    )
    await create_persisted_connector(
        pk="6b4ba593-5ad3-41d3-a5df-9ce18f40190b",
        connector_type="azure-cp",
        operating_state="inactive",
        connector_state="setup",
    )
    await create_persisted_connector(
        pk="2c8f1e89-6146-4a1b-b769-682eaaf87e38",
        connector_type="azure-cp",
        operating_state="active",
        connector_state="connected",
    )
    await create_persisted_connector(
        pk="e47e99fc-d6ea-4194-92f8-e757e27eef4f",
        connector_type="defender",
        operating_state="active",
        connector_state="connected",
    )
    await create_persisted_connector(
        pk="f3d6415b-f051-4371-b086-289944025e03",
        connector_type="defender",
        operating_state="active",
        connector_state="connected",
        properties={"prop_1": "val_1"},
    )

    defender_param = ConnectorQueryParams(connector_type=ConnectorType.DEFENDER)
    test_connected_param = ConnectorQueryParams(
        connector_type=ConnectorType.AZURE_CP, connector_state=ConnectorState.CONNECTED
    )
    properties_param = ConnectorQueryParams(connector_type=ConnectorType.DEFENDER, properties='{"prop_1": "val_1"}')
    non_existent_properties_param = ConnectorQueryParams(
        connector_type=ConnectorType.DEFENDER, properties='{"prop_1": "val_2"}'
    )

    defender_connectors = await connector_repository.get_connectors_by_params(defender_param)
    test_connected_connectors = await connector_repository.get_connectors_by_params(test_connected_param)
    properties_connectors = await connector_repository.get_connectors_by_params(properties_param)
    no_connectors = await connector_repository.get_connectors_by_params(non_existent_properties_param)

    assert len(defender_connectors.connectors) == 2
    assert len(test_connected_connectors.connectors) == 1
    assert len(properties_connectors.connectors) == 1
    assert len(no_connectors.connectors) == 0
    assert defender_connectors.model_fields_set == {"connectors"}

    # Test pagination
    connector_repository.page_limit = 1
    paginated_response = await connector_repository.get_connectors_by_params(defender_param)

    # We expect a single item per page due to page_limit=1
    assert len(paginated_response.connectors) <= 1

    # Since we have more items, we should have LastEvaluatedKey
    assert "last_evaluated_key" in paginated_response.model_fields_set
    assert paginated_response.last_evaluated_key is not None


@pytest.mark.asyncio
async def test_get_connectors_by_operating_state(connector_repository, create_persisted_connector):
    """Tests getting connectors filtered by operating state (active/inactive)"""
    # Create test connectors with different operating states
    await create_persisted_connector(
        pk="active-1", connector_type="azure-cp", operating_state="active", connector_state="connected"
    )
    await create_persisted_connector(
        pk="active-2", connector_type="defender", operating_state="active", connector_state="connected"
    )
    await create_persisted_connector(
        pk="inactive-1", connector_type="azure-cp", operating_state="inactive", connector_state="setup"
    )
    await create_persisted_connector(
        pk="inactive-2", connector_type="defender", operating_state="inactive", connector_state="error"
    )

    # Test getting only active connectors
    active_params = ConnectorQueryParams(operating_state="active")
    active_response = await connector_repository.get_connectors_by_params(active_params)
    assert len(active_response.connectors) == 2
    assert all(c.operating_state == "active" for c in active_response.connectors)

    # Test getting only inactive connectors
    inactive_params = ConnectorQueryParams(operating_state="inactive")
    inactive_response = await connector_repository.get_connectors_by_params(inactive_params)
    assert len(inactive_response.connectors) == 2
    assert all(c.operating_state == "inactive" for c in inactive_response.connectors)

    # Test getting both active and inactive connectors
    all_params = ConnectorQueryParams()  # No operating_state filter
    all_response = await connector_repository.get_connectors_by_params(all_params)
    assert len(all_response.connectors) == 4

    # Test pagination for active connectors
    connector_repository.page_limit = 1
    paginated_active_response = await connector_repository.get_connectors_by_params(active_params)
    assert len(paginated_active_response.connectors) == 1
    assert "last_evaluated_key" in paginated_active_response.model_fields_set
    assert paginated_active_response.last_evaluated_key is not None

    # Test pagination for inactive connectors
    paginated_inactive_response = await connector_repository.get_connectors_by_params(inactive_params)
    assert len(paginated_inactive_response.connectors) == 1
    assert "last_evaluated_key" in paginated_inactive_response.model_fields_set
    assert paginated_inactive_response.last_evaluated_key is not None


@pytest.mark.asyncio
async def test_get_connectors_no_connectors_returned(connector_repository):
    """Tests the 'Get Connectors' access pattern when no connectors exist for a given internal_brain_id"""
    internal_brain_id = "saas_1"
    response = await connector_repository.get_connectors_for_vui(internal_brain_id)
    assert response.connectors == []


@pytest.mark.asyncio
async def test_update_connector(connector, connector_repository):
    await connector_repository.persist_connector(connector)

    properties = {"name": "new_name"}

    update = ConnectorUpdate(**properties)

    response: Connector = await connector_repository.update_connector(
        connector.connector_id, connector.internal_brain_id, update.model_dump(exclude_none=True)
    )

    assert response.connector_id == connector.connector_id
    assert response.name != connector.name


@pytest.mark.asyncio
async def test_update_connector_model_validation(connector, connector_repository):
    await connector_repository.persist_connector(connector)

    properties = {"name": "new_name", "test_property": "updated"}
    with pytest.raises(ValidationError) as err:
        ConnectorUpdate(**properties)
    assert err.typename == "ValidationError"


@pytest.mark.asyncio
async def test_update_connector_brain_id_mismatch(connector, connector_repository):
    await connector_repository.persist_connector(connector)

    properties = {"name": "new_name"}
    update = ConnectorUpdate(**properties)
    with pytest.raises(ForbiddenException) as err:
        await connector_repository.update_connector(connector.connector_id, "mock-brain-id", update.model_dump())
    assert err.typename == "ForbiddenException"


@pytest.mark.asyncio
async def test_update_connector_item_not_found(connector_repository):
    properties = {"name": "new_name"}
    update = ConnectorUpdate(**properties)
    with pytest.raises(NotFoundException) as err:
        await connector_repository.update_connector("incorrect-id", "mock-brain-id", update.model_dump())
    assert err.typename == "NotFoundException"


@pytest.mark.asyncio
async def test_update_connector_state(create_persisted_connector, connector_repository):
    connector = await create_persisted_connector()
    connector_state_updates = {"connector_state": "error", "error": {"error_code": "", "error_message": "message"}}
    update = ConnectorStateUpdate(**connector_state_updates)

    response: Connector = await connector_repository.update_connector(
        connector_id=connector.connector_id, internal_brain_id=None, updates=update.model_dump()
    )

    assert response.connector_id == connector.connector_id
    assert response.connector_state == "error"
    assert response.updated_at == update.updated_at


@pytest.mark.asyncio
async def test_get_non_deleted_connector_by_name_and_brain_no_exclusion(
    connector_repository, create_persisted_connector
):
    connector1 = await create_persisted_connector(pk="id-1", name="same-name", internal_brain_id="brain-1")
    await create_persisted_connector(pk="id-2", name="same-name", internal_brain_id="brain-2")
    response = await connector_repository.get_non_deleted_connector_by_name_and_brain(
        name="same-name", internal_brain_id="brain-1"
    )

    assert len(response.connectors) == 1
    assert response.connectors[0].connector_id == connector1.connector_id
    assert response.connectors[0].internal_brain_id == "brain-1"


@pytest.mark.asyncio
async def test_get_non_deleted_connector_by_name_and_brain_with_exclusion(
    connector_repository, create_persisted_connector
):
    connector1 = await create_persisted_connector(pk="id-1", name="same-name", internal_brain_id="brain-1")
    connector2 = await create_persisted_connector(pk="id-2", name="same-name", internal_brain_id="brain-1")

    response = await connector_repository.get_non_deleted_connector_by_name_and_brain(
        name="same-name", internal_brain_id="brain-1", exclude_connector_id=connector1.connector_id
    )

    assert len(response.connectors) == 1
    assert response.connectors[0].connector_id == connector2.connector_id
    assert response.connectors[0].name == "same-name"
    assert response.connectors[0].internal_brain_id == "brain-1"


@pytest.mark.asyncio
async def test_get_non_deleted_connector_by_name_and_brain_deleted_connector(
    connector_repository, create_persisted_connector
):
    await create_persisted_connector(
        pk="id-1", name="test-name", internal_brain_id="brain-1", connector_state="deleted"
    )
    connector2 = await create_persisted_connector(pk="id-2", name="test-name", internal_brain_id="brain-1")

    response = await connector_repository.get_non_deleted_connector_by_name_and_brain(
        name="test-name", internal_brain_id="brain-1"
    )
    assert len(response.connectors) == 1
    assert response.connectors[0].connector_id == connector2.connector_id
    assert response.connectors[0].connector_state != "deleted"


@pytest.mark.asyncio
async def test_get_non_deleted_connector_by_name_and_brain_no_matches(connector_repository, create_persisted_connector):
    await create_persisted_connector(pk="id-1", name="other-name", internal_brain_id="brain-1")

    response = await connector_repository.get_non_deleted_connector_by_name_and_brain(
        name="test-name", internal_brain_id="brain-1"
    )

    assert len(response.connectors) == 0


@pytest.mark.asyncio
async def test_get_non_deleted_connector_by_name_and_brain_different_brain(
    connector_repository, create_persisted_connector
):
    await create_persisted_connector(pk="id-1", name="test-name", internal_brain_id="brain-1")

    response = await connector_repository.get_non_deleted_connector_by_name_and_brain(
        name="test-name", internal_brain_id="brain-2"
    )

    assert len(response.connectors) == 0


# Commented out tests can be implemented when connector repository methods are implemented

# def test_operating_state_gsi_get_active_connectors(dynamodb_adapter):
#     """
#     Access pattern: Retrieve all active connectors
#     """
#     response = dynamodb_adapter.query_by_gsi(
#         index_name=GlobalIndex.OPERATING_STATE_GSI,
#         key_condition_expression=Key("operating_state").eq("active"),
#     )
#     assert len(response) == 5
#
#
# def test_operating_state_gsi_get_connectors_by_connector_type(dynamodb_adapter):
#     """
#     Access pattern: Retrieve active connectors of a specific type
#     """
#     response = dynamodb_adapter.query_by_gsi(
#         index_name=GlobalIndex.OPERATING_STATE_GSI,
#         key_condition_expression=Key("operating_state").eq("active") & Key("data").begins_with("aws"),
#     )
#     assert len(response) == 3
#
#
# def test_operating_state_gsi_get_connectors_by_connector_type_and_state(dynamodb_adapter):
#     """
#     Access pattern: Retrieve active connectors of a specific type in a particular state
#     """
#     response = dynamodb_adapter.query_by_gsi(
#         index_name=GlobalIndex.OPERATING_STATE_GSI,
#         key_condition_expression=Key("operating_state").eq("active") & Key("data").eq("aws#connected"),
#     )
#     assert len(response) == 2
#
#
# def test_operating_state_gsi_get_connectors_by_connector_type_and_state_with_filter(dynamodb_adapter):
#     """
#     Access pattern: Retrieve all connectors of a specific type with a specific property
#     """
#     response = dynamodb_adapter.query_by_gsi(
#         index_name=GlobalIndex.OPERATING_STATE_GSI,
#         key_condition_expression=Key("operating_state").eq("active") & Key("data").eq("aws#connected"),
#         filter_expression=Attr("ingesting_state").eq("ingesting"),
#     )
#     assert len(response) == 1
#
#
# def test_internal_brain_id_gsi_get_connectors_by_connector_type_and_state_with_filter(dynamodb_adapter):
#     """
#     Access pattern: Retrieve all connectors (per tenant) that are not deleted
#     """
#     response = dynamodb_adapter.query_by_gsi(
#         index_name=GlobalIndex.INTERNAL_BRAIN_ID_GSI,
#         key_condition_expression=Key("internal_brain_id").eq("saas_1"),
#         filter_expression=Attr("connector_state").ne("deleted"),
#     )
#     assert len(response) == 2
