import pytest_asyncio

from nexus.repositories.dynamodb import DynamoDBBaseRepository
from nexus.utils.config import config


@pytest_asyncio.fixture(scope="function")
async def base_repository(create_table) -> DynamoDBBaseRepository:
    """
    Fixture to create a DynamoDBBaseRepository instance for async tests
    """
    config.DDB_ENDPOINT_URL = None
    return DynamoDBBaseRepository(create_table["TableDescription"]["TableName"])
