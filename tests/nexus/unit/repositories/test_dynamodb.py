import pytest
from botocore.exceptions import ClientError

from nexus.models import ConnectorDelete
from nexus.repositories.dynamodb import ConditionalExpressionComponents
from nexus.utils.config import config


@pytest.mark.asyncio
async def test_put_item(dynamodb_async_client, connector, base_repository):
    """Tests put_item functionality on DynamoDB"""

    # Given: An item is persisted
    await base_repository.put_item(connector.model_dump(by_alias=True))

    # When: Retrieve the item and verify it was inserted with the expected attributes.
    response = await dynamodb_async_client.get_item(
        TableName=config.NEXUS_TABLE_NAME, Key={"pk": {"S": "mock_id"}, "sk": {"S": "connector"}}
    )

    # Then: We can get the item.
    item = response.get("Item")
    assert item["pk"]["S"] == "mock_id"
    assert item["sk"]["S"] == "connector"
    assert item["internal_brain_id"]["S"] == "mock_brain_id"


@pytest.mark.asyncio
async def test_get_item(base_repository, create_persisted_connector):
    """Tests get_item functionality on DynamoDB"""

    # Given: An item has been created
    await create_persisted_connector(pk="connector_001")

    # When: Retrieve the item using the repository.
    retrieved_item = await base_repository.get_item(pk="connector_001", sk="connector")

    # Then: Verify that the retrieved item matches the expected attributes.
    assert retrieved_item["pk"] == "connector_001"
    assert retrieved_item["sk"] == "connector"
    assert retrieved_item["internal_brain_id"] == "mock_brain_id"


@pytest.mark.asyncio
async def test_update_item(base_repository, create_persisted_connector):
    """Tests update_item functionality on DynamoDB"""

    conditions = ConditionalExpressionComponents(
        condition_expression="attribute_exists(pk)",
        expression_attribute_values={},
    )

    # Given:
    # - The PK and SK of an item to be updated.
    pk, sk = "connector_001", "connector"
    await create_persisted_connector(pk=pk)

    # When we update we expect the following results
    # - Update 1: Modify a string value
    update_type_1 = {"connector_state": "connected"}
    await base_repository.update_item(pk, sk, update_type_1, conditions)
    item = await base_repository.get_item(pk, sk)
    assert item["connector_state"] == "connected"

    # - Update 2: Add map object
    update_type_2 = {"setup_records": {"consent": "awaiting", "user_input": "awaiting"}}
    await base_repository.update_item(pk, sk, update_type_2, conditions)
    item = await base_repository.get_item(pk, sk)
    assert item["setup_records"] == {"consent": "awaiting", "user_input": "awaiting"}

    # - Update 3: Update the subfield in a map
    update_type_3 = {"setup_records.consent": "completed"}
    await base_repository.update_item(pk, sk, update_type_3, conditions)
    item = await base_repository.get_item(pk, sk)
    assert item["setup_records"] == {"consent": "completed", "user_input": "awaiting"}


@pytest.mark.asyncio
async def test_update_item_doesnt_exist(base_repository):
    update = {"connector_state": "updated"}

    conditions = ConditionalExpressionComponents(
        condition_expression="attribute_exists(pk)",
        expression_attribute_values={},
    )

    with pytest.raises(ClientError) as err:
        await base_repository.update_item("mock", "mock", update, conditions)
    assert err.typename == "ConditionalCheckFailedException"


@pytest.mark.asyncio
async def test_update_item_conditional(base_repository, create_persisted_connector):
    pk, sk = "connector_001", "connector"
    await create_persisted_connector(pk=pk, internal_brain_id="saas_1")

    update = {"connector_state": "updated"}

    conditions = ConditionalExpressionComponents(
        condition_expression="internal_brain_id = :internal_brain_id",
        expression_attribute_values={":internal_brain_id": "saas_1"},
    )

    await base_repository.update_item(pk, sk, update, conditions)
    item = await base_repository.get_item(pk, sk)
    assert item["connector_state"] == "updated"


@pytest.mark.asyncio
async def test_update_item_conditional_failure(base_repository, create_persisted_connector):
    pk, sk = "connector_001", "connector"
    await create_persisted_connector(pk=pk)

    update = {"connector_state": "updated"}

    conditions = ConditionalExpressionComponents(
        condition_expression="internal_brain_id = :internal_brain_id",
        expression_attribute_values={":internal_brain_id": "incorrect_brain_id"},
    )

    with pytest.raises(ClientError) as err:
        await base_repository.update_item(pk, sk, update, conditions)
    assert err.typename == "ConditionalCheckFailedException"


@pytest.mark.asyncio
async def test_delete_item(base_repository, create_persisted_connector):
    """Tests update_item functionality on DynamoDB"""

    conditions = ConditionalExpressionComponents(
        condition_expression="attribute_exists(pk)",
        expression_attribute_values={},
    )

    # Given:
    # - The PK and SK of an item to be updated.
    pk, sk = "connector_001", "connector"
    await create_persisted_connector(pk=pk)

    # When we update we expect the following results
    update = ConnectorDelete().model_dump()
    await base_repository.update_item(pk, sk, update, conditions)
    item = await base_repository.get_item(pk, sk)
    assert item["connector_state"] == "deleted"
    assert item["operating_state"] == "inactive"
