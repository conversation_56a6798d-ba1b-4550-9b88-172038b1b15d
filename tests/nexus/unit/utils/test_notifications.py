from unittest.mock import AsyncMock

import pytest
from botocore.exceptions import ClientError

from nexus.models import Connector, NexusEvent
from nexus.services.notifications import Notification<PERSON>anager


def test_queue_is_singleton():
    manager1 = NotificationManager()
    manager2 = NotificationManager()

    assert manager1.queue is manager2.queue
    assert id(manager1.queue) == id(manager2.queue)


@pytest.mark.asyncio
async def test_publish_event(crowdstrike_connector_connecting):
    manager = NotificationManager()

    await manager.create_event(crowdstrike_connector_connecting)

    event_in_queue = await NotificationManager.queue.get()
    assert isinstance(event_in_queue, NexusEvent)
    assert event_in_queue.connector_id == "mock_id"


@pytest.mark.asyncio
async def test_process_event_raises_client_error(mocker, crowdstrike_connector_connecting: Connector):
    event = NexusEvent(**crowdstrike_connector_connecting.model_dump())
    manager = NotificationManager()

    mock_publish = mocker.patch("nexus.services.notifications.publish_nexus_event", new_callable=AsyncMock)
    mock_put = mocker.patch.object(manager.queue, "put", new_callable=AsyncMock)
    mock_sleep = mocker.patch("asyncio.sleep", new_callable=AsyncMock)

    mock_publish.side_effect = ClientError({"Error": {"Code": "500"}}, "Publish")

    await manager.process_event(event)

    mock_sleep.assert_awaited_once_with(5)
    mock_put.assert_awaited_once_with(event)
