import pytest

from nexus.api.exceptions import ForbiddenException
from nexus.utils.utils import mask_dict_values, validate_tenancy


def test_validate_tenancy_match():
    assert validate_tenancy("mock-id", "mock-id") is None


def test_validate_tenancy_mismatch():
    with pytest.raises(ForbiddenException) as e:
        validate_tenancy("mock-id", "incorrect-id")

    assert e.typename == "ForbiddenException"
    assert e.value.detail == "You are forbidden from modifying this resource"


def test_mask_dict_values_all_cases():
    # Empty dictionary
    data = {}
    keys_to_mask = ["key1"]
    unmasked_start = 3
    expected = {}
    assert mask_dict_values(data, keys_to_mask, unmasked_start) == expected

    # No keys to mask specified
    data = {"key1": "value1", "key2": "value2"}
    keys_to_mask = []
    expected = {"key1": "value1", "key2": "value2"}
    assert mask_dict_values(data, keys_to_mask, unmasked_start) == expected

    # Keys to mask not in dictionary
    data = {"key1": "value1", "key2": "value2"}
    keys_to_mask = ["key3"]
    expected = {"key1": "value1", "key2": "value2"}
    assert mask_dict_values(data, keys_to_mask, unmasked_start) == expected

    # Different key values to mask
    data = {"key1": "value1", "key2": "ab", "key3": None, "key4": "value"}
    keys_to_mask = ["key1", "key2", "key3"]
    expected = {"key1": "val***", "key2": "ab", "key3": None, "key4": "value"}
    assert mask_dict_values(data, keys_to_mask, unmasked_start) == expected
