from unittest.mock import AsyncMock

from fastapi.testclient import TestClient

from nexus.main import app
from nexus.schemas import ConnectorResponse
from nexus.services.notifications import NotificationManager

client = TestClient(app)


def test_complete_defender_consent(mocker, defender_connector):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_consent = AsyncMock(return_value=defender_connector)

    mocker.patch.object(NotificationManager, "create_event", new_callable=AsyncMock)

    body = {"properties": {"connector_type": "defender", "ms_tenant_id": "mock-id"}}
    response = client.post("/v1/connectors/mock-id/setup/consent", headers={"x-vectra-service": "consent"}, json=body)
    expected_response = ConnectorResponse(**defender_connector.model_dump())

    assert response.status_code == 200
    assert response.json() == expected_response.model_dump()


def test_complete_azure_properties(mocker, azure_cp_connector_connecting):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_properties = AsyncMock(return_value=azure_cp_connector_connecting)

    mock_publish_event = mocker.patch.object(NotificationManager, "create_event", new_callable=AsyncMock)

    body = {"properties": {"connector_type": "azure-cp", "resource_group_id": "mock-id"}}
    response = client.post(
        "/v1/connectors/mock-id/setup/properties",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
        json=body,
    )
    expected_response = ConnectorResponse(**azure_cp_connector_connecting.model_dump())

    assert response.status_code == 200
    assert response.json() == expected_response.model_dump()
    mock_publish_event.assert_called_once()


def test_complete_crowdstrike_secrets(mocker, crowdstrike_connector_connecting):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_secrets = AsyncMock(return_value=crowdstrike_connector_connecting)

    mock_publish_event = mocker.patch.object(NotificationManager, "create_event", new_callable=AsyncMock)

    body = {
        "connector_type": "crowdstrike",
        "secret": {
            "client_id": "298342j382u34n83u2",
            "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890",
        },
        "properties": {
            "crowdstrike_url": "https://api.crowdstrike.com",
        },
    }

    response = client.post(
        "/v1/connectors/mock-id/setup/secrets",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
        json=body,
    )
    expected_response = ConnectorResponse(**crowdstrike_connector_connecting.model_dump())

    assert response.status_code == 200
    assert response.json() == expected_response.model_dump()
    mock_publish_event.assert_called_once()


def test_complete_secrets_bad_body_connector_type():
    body = {
        "connector_type": "azure-cp",  # incorrect connector type
        "secret": {
            "client_id": "298342j382u34n83u2",
            "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890",
        },
        "properties": {
            "crowdstrike_url": "https://api.crowdstrike.com",
        },
    }
    response = client.post(
        "/v1/connectors/mock-id/setup/secrets",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock-brain-id"},
        json=body,
    )
    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "literal_error"
    assert response.json()["detail"][0]["loc"] == ["body", "connector_type"]


def test_complete_secrets_bad_body_missing_field():
    body = {
        "connector_type": "crowdstrike",
        # missing client_secret
        "secret": {"client_id": "298342j382u34n83u2"},
        "properties": {"crowdstrike_url": "https://api.crowdstrike.com"},
    }
    response = client.post(
        "/v1/connectors/mock-id/setup/secrets",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock-brain-id"},
        json=body,
    )
    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "missing"
    assert response.json()["detail"][0]["loc"] == ["body", "secret", "ConsentSecret", "client_secret"]


def test_update_crowdstrike_secrets(mocker, crowdstrike_connector):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.update_secret = AsyncMock(return_value=crowdstrike_connector)

    mocker.patch.object(NotificationManager, "create_event", new_callable=AsyncMock)

    bodies = [
        # update client_secret only
        {"connector_type": "crowdstrike", "secret": {"client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890"}},
        # update crowdstrike_url only
        {"connector_type": "crowdstrike", "properties": {"crowdstrike_url": "https://api.crowdstrike.com"}},
        # update client_secret and crowdstrike_url
        {
            "connector_type": "crowdstrike",
            "secret": {"client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890"},
            "properties": {"crowdstrike_url": "https://api.crowdstrike.com"},
        },
    ]

    for body in bodies:
        response = client.patch(
            "/v1/connectors/mock-id/setup/secrets",
            headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
            json=body,
        )
        expected_response = ConnectorResponse(**crowdstrike_connector.model_dump())

        assert response.status_code == 200
        assert response.json() == expected_response.model_dump()


def test_update_secrets_incorrect_properties():
    body = {
        "connector_type": "crowdstrike",
        "properties": {"some_url": "https://fake-url.com"},
    }
    response = client.patch(
        "/v1/connectors/mock-id/setup/secrets",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock-brain-id"},
        json=body,
    )
    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "missing"
    assert response.json()["detail"][0]["loc"] == ["body", "properties", "CrowdstrikeProperties", "crowdstrike_url"]


def test_complete_sentinelone_secrets(mocker, sentinelone_connector_connecting):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_secrets = AsyncMock(return_value=sentinelone_connector_connecting)

    mock_publish_event = mocker.patch.object(NotificationManager, "create_event", new_callable=AsyncMock)

    body = {
        "connector_type": "sentinelone",
        "secret": {
            "api_token": "token-secret",
        },
        "properties": {
            "sentinelone_url": "https://usea1-partners.sentinelone.net",
        },
    }

    response = client.post(
        "/v1/connectors/mock-id/setup/secrets",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
        json=body,
    )
    expected_response = ConnectorResponse(**sentinelone_connector_connecting.model_dump())

    assert response.status_code == 200
    assert response.json() == expected_response.model_dump()
    mock_publish_event.assert_called_once()


def test_complete_aws_properties(mocker, aws_connector_connecting):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_properties = AsyncMock(return_value=aws_connector_connecting)

    mock_publish_event = mocker.patch.object(NotificationManager, "create_event", new_callable=AsyncMock)

    body = {
        "properties": {
            "connector_type": "aws",
            "s3_bucket_name": "mock-name",
            "iam_role_arn": "arn:aws:iam::123456789009:role/mock-name",
            "sns_topic_arn": "arn:aws:sns:us-west-2:123456789009:mock-name",
        }
    }
    response = client.post(
        "/v1/connectors/mock-id/setup/properties",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
        json=body,
    )
    expected_response = ConnectorResponse(**aws_connector_connecting.model_dump())

    assert response.status_code == 200
    assert response.json() == expected_response.model_dump()
    mock_publish_event.assert_called_once()


def test_aws_incorrect_property(mocker, aws_connector_connecting):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_properties = AsyncMock(return_value=aws_connector_connecting)

    body = {
        "properties": {
            "connector_type": "aws",
            "s3_bucket_name": "MOCK_NAME",
            "iam_role_arn": "arn:aws:iam::123456789009:role/mock-name",
            "sns_topic_arn": "arn:aws:sns:us-west-2:123456789009:mock-name",
        }
    }
    response = client.post(
        "/v1/connectors/mock-id/setup/properties",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
        json=body,
    )

    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "string_pattern_mismatch"


def test_aws_missing_property(mocker, aws_connector_connecting):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_properties = AsyncMock(return_value=aws_connector_connecting)

    body = {
        "properties": {
            "connector_type": "aws",
            "s3_bucket_name": "mock-name",
            "iam_role_arn": "arn:aws:iam::123456789009:role/mock-name",
        }
    }
    response = client.post(
        "/v1/connectors/mock-id/setup/properties",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
        json=body,
    )

    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "missing"
    assert response.json()["detail"][0]["loc"] == ["body", "properties", "aws", "sns_topic_arn"]


def test_aws_iam_rolename_too_long(mocker, aws_connector_connecting):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.setup_properties = AsyncMock(return_value=aws_connector_connecting)

    body = {
        "properties": {
            "connector_type": "aws",
            "s3_bucket_name": "MOCK_NAME",
            "iam_role_arn": """"arn:aws:iam::123456789009:role/dsafsadfsafsdfasdfasdfsadgqwefewfsdfsdsfs
            dgrgertregerfsdfsdfname/dsafsadfsafsdfasdfasdfsadgqwefewfsdfsdsfsdgrgertregerdfsdfsdfname/dsa
            fsadfsafsdfasdfasdfsadgqwefewfsdfsdsfsdgrgertregerfsdfsdfname/dsafsadfsafsdfasdfasdfsadgqwefew
            fsdfsdsfsdgrgertregerfsdfsdfname/dsafsadfsafsdfasdfasdfsadgqwefewfsdfsdsfsdgrgertregerfsdfsdf
            name/dsafsadfsafsdfasdfasdfsadgqwefewfsdfsdsfsdgrgertregerfsdfsdfname/dsafsadfsafsdfasdfasdfs
            adgqdefewfsdfsdsfsdgrgertregerfsdfsdfname/dsfasdfsdfsfdsdfsdfdsfsdfsdfdsfsdsdfsdfdfssfdsafsdfsdfdsd
            """,
            "sns_topic_arn": "arn:aws:sns:us-west-2:123456789009:mock-name",
        }
    }
    response = client.post(
        "/v1/connectors/mock-id/setup/properties",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock_brain_id"},
        json=body,
    )

    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "string_pattern_mismatch"
