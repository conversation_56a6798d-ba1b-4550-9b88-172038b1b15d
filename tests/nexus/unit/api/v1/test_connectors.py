from unittest.mock import AsyncMock

import pytest
from fastapi.testclient import Test<PERSON>lient

from nexus.api.exceptions import NotFoundException
from nexus.main import app
from nexus.models import VectraService
from nexus.schemas import ConnectorResponse

client = TestClient(app)


def test_create_connector(mocker, connector_controller_responses):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.create_connector = AsyncMock(return_value=connector_controller_responses.connector)

    body = {"name": "mock-name", "connector_type": "azure-cp"}
    response = client.post(
        "/v1/connectors",
        headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": VectraService.VUI},
        json=body,
    )

    assert response.status_code == 201
    assert response.json() == connector_controller_responses.connector.model_dump()


def test_create_connector_with_external_id(mocker, connector_controller_responses):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value

    external_id = "550e8400-e29b-41d4-a716-446655440000"
    connector_response = connector_controller_responses.connector.model_copy(deep=True)
    connector_response.connector_id = external_id
    mock_controller.create_connector = AsyncMock(return_value=connector_response)

    body = {"name": "mock-name", "connector_type": "azure-cp", "connector_id": external_id}
    response = client.post(
        "/v1/connectors", headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": "vui"}, json=body
    )

    expected_response = connector_response.model_dump()

    assert response.status_code == 201
    assert response.json() == expected_response


def test_create_missing_brain_id_header():
    body = {"name": "mock-name", "connector_type": "azure-cp"}
    response = client.post("/v1/connectors", headers={"x-vectra-service": "vui"}, json=body)

    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "missing"
    assert response.json()["detail"][0]["loc"] == ["header", "x-internal-brain-id"]


def test_create_connector_missing_body():
    response = client.post(
        "/v1/connectors", headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": "vui"}
    )
    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "missing"
    assert response.json()["detail"][0]["loc"] == ["body"]


def test_create_connector_incorrect_type():
    body = {"name": "mock-name", "connector_type": "mock"}
    response = client.post(
        "/v1/connectors", headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": "vui"}, json=body
    )
    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "enum"
    assert response.json()["detail"][0]["loc"] == ["body", "connector_type"]


def test_patch_connector(mocker, connector_controller_responses):
    mock_update = AsyncMock(return_value=connector_controller_responses.connector)
    mocker.patch("nexus.controllers.ConnectorController.update_connector", mock_update)

    body = {"name": "mock_name"}
    response = client.patch(
        "/v1/connectors/sid_connector_id",
        headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": "logflow"},
        json=body,
    )

    assert response.status_code == 200
    assert response.json() == connector_controller_responses.connector.model_dump()


@pytest.mark.parametrize(
    "body, expected_status, error_msg_extractor, expected_msg",
    [
        (
            {"wrong_property": "bar"},
            422,
            lambda r: r.json()["detail"][0]["msg"],
            "Extra inputs are not permitted",
        ),
        (
            {"size": "jumbotron"},
            422,
            lambda r: r.json()["detail"][0]["msg"],
            "Input should be 'small', 'medium' or 'large'",
        ),
        (
            {"last_log_received": "2024-11-14 15:37:11"},
            400,
            lambda r: r.json()["message"],
            "The date must be in UTC ISO format with Zulu notation (e.g. 2024-02-26T10:37:11Z)",
        ),
    ],
)
def test_patch_connector_invalid_update(body, expected_status, error_msg_extractor, expected_msg):
    response = client.patch(
        "/v1/connectors/sid_connector_id",
        headers={"x-vectra-service": "vui", "x-internal-brain-id": "mock-brain-id"},
        json=body,
    )
    assert response.status_code == expected_status
    assert error_msg_extractor(response) == expected_msg


def test_patch_connector_logflow(mocker, connector_controller_responses):
    mock_update = AsyncMock(return_value=connector_controller_responses.connector)
    mocker.patch("nexus.controllers.ConnectorController.update_connector", mock_update)

    body = {"last_log_received": "2024-11-14T15:37:11.750Z", "size": "medium"}
    response = client.patch("/v1/connectors/sid_connector_id", headers={"x-vectra-service": "logflow"}, json=body)
    assert response.status_code == 200
    assert response.json() == connector_controller_responses.connector.model_dump()


def test_delete_connector(mocker, connector):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    deleted_connector = connector.model_copy(deep=True)
    deleted_connector.connector_state = "deleted"
    deleted_connector.operating_state = "inactive"

    mock_controller.soft_delete_connector = AsyncMock(return_value=deleted_connector)

    body = {"connector_type": "azure-cp"}

    # need to use request as client.delete does not support json
    response = client.request(
        method="DELETE",
        url=f"/v1/connectors/{connector.connector_id}",
        headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": VectraService.VUI},
        json=body,
    )

    expected_response = ConnectorResponse(**deleted_connector.model_dump())

    assert response.status_code == 200
    assert response.json() == expected_response.model_dump()


def test_get_connectors_internal_brain_id(mocker, connector_controller_responses):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connectors_by_internal_brain_id = AsyncMock(
        return_value=connector_controller_responses.connector_list
    )

    response = client.get(
        "/v1/connectors", headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": "logflow"}
    )

    assert response.status_code == 200
    assert response.json() == connector_controller_responses.connector_list.model_dump()


def test_get_connectors_returns_none(mocker, connector_controller_responses):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    # modify fixture so no connectors are returned
    empty_list = connector_controller_responses.connector_list.model_copy(deep=True)
    empty_list.connectors = []
    mock_controller.get_connectors_by_internal_brain_id = AsyncMock(return_value=empty_list)

    response = client.get(
        "/v1/connectors", headers={"x-internal-brain-id": "mock-brain-id", "x-vectra-service": "logflow"}
    )

    assert response.status_code == 200
    assert response.json() == empty_list.model_dump()


@pytest.mark.parametrize(
    "url",
    [
        "/v1/connectors",
        "/v1/connectors?connector_type=crowdstrike",
        "/v1/connectors?connector_type=crowdstrike&connector_state=setup",
        '/v1/connectors?connector_type=crowdstrike&properties={"foo": "bar"}',
        "/v1/connectors?connector_type=crowdstrike&next_token=slkdufosdji",
        "/v1/connectors?connector_type=crowdstrike&operating_state=active",
    ],
)
def test_get_connectors_query_params_crowdstrike(mocker, connector_controller_responses, url):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connectors_by_params = AsyncMock(return_value=connector_controller_responses.connector_list)

    response = client.get(url, headers={"x-vectra-service": VectraService.LOGFLOW})

    assert response.status_code == 200
    assert ("connectors" and "next_link") in response.json()


@pytest.mark.parametrize(
    "url",
    [
        "/v1/connectors",
        "/v1/connectors?connector_type=sentinelone",
        "/v1/connectors?connector_type=sentinelone&connector_state=setup",
        '/v1/connectors?connector_type=sentinelone&properties={"foo": "bar"}',
        "/v1/connectors?connector_type=sentinelone&next_token=slkdufosdji",
        "/v1/connectors?connector_type=sentinelone&operating_state=active",
    ],
)
def test_get_connectors_query_params(mocker, connector_controller_responses, url):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connectors_by_params = AsyncMock(return_value=connector_controller_responses.connector_list)

    response = client.get(url, headers={"x-vectra-service": VectraService.LOGFLOW})

    assert response.status_code == 200
    assert ("connectors" and "next_link") in response.json()


def test_get_connectors_pagination(mocker, connector_controller_responses):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connectors_by_params = AsyncMock(return_value=connector_controller_responses.connector_list)

    # check next link is none when no pagination
    response = client.get("/v1/connectors", headers={"x-vectra-service": VectraService.LOGFLOW})
    assert response.json()["next_link"] is None

    # check next link exists and contains url
    mock_controller.get_connectors_by_params = AsyncMock(
        return_value=connector_controller_responses.connector_list_with_next_page
    )
    response = client.get("/v1/connectors", headers={"x-vectra-service": VectraService.LOGFLOW})
    # Match portion of the next_link, which will include x_vectra_service parameter
    assert "next_token=" in response.json()["next_link"]


def test_get_connectors_for_vui_service(mocker, connector_controller_responses):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connectors_by_internal_brain_id = AsyncMock(
        return_value=connector_controller_responses.connector_list
    )

    response = client.get(
        "/v1/connectors", headers={"x-vectra-service": VectraService.VUI, "x-internal-brain-id": "test"}
    )

    assert response.status_code == 200


def test_get_connectors_for_missing_service():
    """Test that the API returns a 422 status code when the x-vectra-service header is missing"""
    response = client.get("/v1/connectors")

    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "missing"
    assert response.json()["detail"][0]["loc"] == ["header", "x-vectra-service"]


@pytest.mark.parametrize("vectra_service", [VectraService.LOGFLOW, VectraService.VUI])
def test_get_connector_with_service_header(mocker, connector, vectra_service):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connector_by_id = AsyncMock(return_value=connector)

    headers = {"x-vectra-service": vectra_service}
    if vectra_service == VectraService.VUI:
        headers["x-internal-brain-id"] = "mock-brain-id"

    response = client.get(f"/v1/connectors/{connector.connector_id}", headers=headers)

    expected_response = ConnectorResponse(**connector.model_dump()).model_dump()
    assert response.status_code == 200
    assert response.json() == expected_response


def test_get_connectors_query_params_bad_headers():
    # Test with no headers
    # no_header_response = client.get("/v1/connectors")
    # assert no_header_response.status_code == 422
    # assert no_header_response.json()["detail"][0]["type"] == "missing"
    # assert no_header_response.json()["detail"][0]["loc"] == ["header", "x-vectra-service"]

    # Test with bad service header
    bad_service_response = client.get("/v1/connectors", headers={"x-vectra-service": "fake-service"})
    assert bad_service_response.status_code == 422
    assert bad_service_response.json()["detail"][0]["type"] == "enum"
    assert bad_service_response.json()["detail"][0]["loc"] == ["header", "x-vectra-service"]


def test_get_connectors_query_params_bad_params():
    headers = {"x-vectra-service": "logflow"}
    bad_conn_state_response = client.get("/v1/connectors?connector_state=fake-state", headers=headers)
    bad_conn_type_response = client.get("/v1/connectors?connector_type=fake-type", headers=headers)
    bad_state_missing_type_response = client.get("/v1/connectors?connector_state=connected", headers=headers)
    bad_properties_missing_type_response = client.get("/v1/connectors?properties=not-dict", headers=headers)
    bad_properties_non_json_response = client.get(
        "/v1/connectors?connector_type=azure-cp&properties=not-dict", headers=headers
    )

    assert bad_conn_state_response.status_code == 422
    assert bad_conn_state_response.json()["detail"][0]["loc"] == ["query", "connector_state"]
    assert bad_conn_type_response.status_code == 422
    assert bad_conn_type_response.json()["detail"][0]["loc"] == ["query", "connector_type"]
    assert bad_state_missing_type_response.status_code == 400
    assert (
        bad_state_missing_type_response.json()["message"]
        == "connector_type is required when filtering with connector_state"
    )
    assert bad_properties_missing_type_response.status_code == 400
    assert (
        bad_properties_missing_type_response.json()["message"]
        == "connector_type is required when filtering with properties"
    )
    assert bad_properties_non_json_response.status_code == 400
    assert bad_properties_non_json_response.json()["message"] == "Invalid JSON format for properties"


def test_get_connector(mocker, connector):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connector_by_id = AsyncMock(return_value=connector)

    response = client.get("/v1/connectors/mock_id", headers={"x-vectra-service": "logflow"})

    expected_response = ConnectorResponse(**connector.model_dump()).model_dump()
    assert response.status_code == 200
    assert response.json() == expected_response


def test_get_connector_missing_header(connector):
    """Test that a 422 is returned when the x-vectra-service header is missing"""
    response = client.get(f"/v1/connectors/{connector.connector_id}", headers={})

    assert response.status_code == 422
    assert response.json()["detail"][0]["type"] == "missing"
    assert response.json()["detail"][0]["loc"] == ["header", "x-vectra-service"]


def test_get_connector_item_doesnt_exist(mocker):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.get_connector_by_id = AsyncMock(
        side_effect=NotFoundException(detail="Connector non_existent_connector cannot be found")
    )

    response = client.get("/v1/connectors/non_existent_connector", headers={"x-vectra-service": "logflow"})

    assert response.status_code == 404
    assert response.json()["message"] == "Connector non_existent_connector cannot be found"


def test_get_connector_incorrect_vectra_service_header(connector):
    response = client.get(
        f"/v1/connectors/{connector.connector_id}",
        headers={"x-vectra-service": "fake-service"},
    )

    assert response.status_code == 422
    assert response.json()["detail"][0]["loc"] == ["header", "x-vectra-service"]


@pytest.mark.parametrize(
    "body",
    [
        {"connector_state": "connected"},
        {"connector_state": "error", "error": {"error_code": "sample-error", "error_message": "error message"}},
    ],
)
def test_update_connector_state(mocker, connector, body):
    mock_controller = mocker.patch("nexus.utils.factories.ConnectorFactory.get_connector_controller").return_value
    mock_controller.update_connector_state = AsyncMock(return_value=connector)

    response = client.post(
        f"/v1/connectors/{connector.connector_id}/state",
        headers={"x-vectra-service": "logflow"},
        json=body,
    )
    assert response.status_code == 200


@pytest.mark.parametrize(
    "body, expected_msg",
    [
        (
            {"connector_state": "fake-state"},
            "Input tag 'fake-state' found using 'connector_state' does not match any of the expected tags: connected, error",
        ),
        (
            {"connector_state": "deleted"},
            "Input tag 'deleted' found using 'connector_state' does not match any of the expected tags: connected, error",
        ),
        (
            {"connector_state": "error"},
            "Field required",
        ),
        (
            {"connector_state": "error", "error": "bad error object"},
            "Input should be a valid dictionary or object to extract fields from",
        ),
    ],
)
def test_update_connector_state_errors(connector, body, expected_msg):
    response = client.post(
        f"/v1/connectors/{connector.connector_id}/state",
        headers={"x-vectra-service": "logflow"},
        json=body,
    )
    assert response.status_code == 422
    assert response.json()["detail"][0]["msg"] == expected_msg
