from nexus.schemas import ConnectorResponse


def test_connector_response_initialises_with_missing_values():
    # Initial fields of connector response object
    connector = {
        "pk": "mock_id",
        "sk": "connector",
        "name": "mock_name",
        "internal_brain_id": "mock_brain_id",
        "connector_type": "azure-cp",
        "connector_state": "setup",
        "operating_state": "inactive",
        "created_at": "2024-11-11T10:10:10Z",
        "updated_at": "2024-11-11T10:10:10Z",
        "first_log_received": None,
        "last_log_received": None,
        "size": "medium",
        "last_sequence_id": None,
        "data": "test#setup",
        "setup_records": {},
        "error": None,
        "properties": {},
    }

    # Initializing this class will cause the test to fail if any additional fields are
    # added without default values
    ConnectorResponse(**connector)
