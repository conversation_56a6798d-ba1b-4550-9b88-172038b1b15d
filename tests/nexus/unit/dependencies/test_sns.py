from unittest.mock import AsyncMock, MagicMock

import pytest

from nexus.models import ConnectorState, ConnectorType, NexusEvent
from nexus.utils.config import config
from nexus.utils.sns import publish_nexus_event


@pytest.mark.asyncio
async def test_publish_event(sns_client):
    sns_client.publish = MagicMock()
    await sns_client.create_topic(Name="test-topic")
    response = await sns_client.list_topics()
    config.NEXUS_SNS_TOPIC_ARN = response["Topics"][0]["TopicArn"]

    event = NexusEvent(
        connector_id="1234",
        connector_type=ConnectorType.CROWDSTRIKE,
        connector_state=ConnectorState.CONNECTING,
        internal_brain_id="saas-1234",
    )

    await publish_nexus_event(event, sns_client=sns_client)

    sns_client.publish.assert_called_once_with(
        TopicArn=config.NEXUS_SNS_TOPIC_ARN,
        Message=event.model_dump_json(exclude={"request_context"}),
        MessageAttributes={
            "ConnectorId": {"DataType": "String", "StringValue": event.connector_id},
            "ConnectorType": {"DataType": "String", "StringValue": event.connector_type.value},
            "ConnectorState": {"DataType": "String", "StringValue": event.connector_state.value},
            "InternalBrainId": {"DataType": "String", "StringValue": event.internal_brain_id},
        },
    )
