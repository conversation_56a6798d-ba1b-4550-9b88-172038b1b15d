import json
from unittest.mock import AsyncMock

import pytest
from vectra.consent_service_client.models import ConsentUrlResponse

from nexus.api.exceptions import BadGatewayException
from nexus.dependencies.consent import create_consent, create_secret, delete_secret, update_secret
from nexus.models import ConnectorType
from nexus.schemas import ConnectorSecretCreate, ConnectorSecretUpdate


####################
# HAPPY PATH TESTS #
####################
@pytest.mark.asyncio
async def test_create_consent(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.post_consent_url.return_value = ConsentUrlResponse(consent_url="mock-url")
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    resp = await create_consent("mock-id", ConnectorType.DEFENDER, "mock-brain-id")
    assert resp.consent_url == "mock-url"


@pytest.mark.asyncio
async def test_create_secret(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.create_secret.return_value = None
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    create_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {
            "client_id": "298342j382u34n83u2",
            "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890",
        },
        "properties": {
            "crowdstrike_url": "https://api.crowdstrike.com",
        },
    }

    resp = await create_secret("mock-id", "mock-brain-id", ConnectorSecretCreate(**create_secret_body))

    assert resp is None


@pytest.mark.asyncio
async def test_update_secret(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.patch_named_secret.return_value = None
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    update_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {"client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890"},
        "properties": {"crowdstrike_url": "https://api.crowdstrike.com"},
    }

    resp = await update_secret("mock-id", "mock-brain-id", ConnectorSecretUpdate(**update_secret_body))

    assert resp is None


@pytest.mark.asyncio
async def test_delete_secret(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.delete_secret.return_value = None
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    resp = await delete_secret("mock-id", "mock-brain-id")

    assert resp is None


@pytest.mark.asyncio
async def test_delete_secret_when_secret_already_deleted(mocker):
    error_message = json.dumps(
        {
            "error_code": "resource_not_found",
            "error": "The requested resource does not exist: ('mock-id', 'active')",
        }
    )
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.delete_secret.side_effect = RuntimeError(error_message)
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    resp = await delete_secret("mock-id", "mock-brain-id")

    assert resp is None


######################
# UNHAPPY PATH TESTS #
######################


@pytest.mark.asyncio
async def test_create_consent_exception(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.post_consent_url.side_effect = RuntimeError()
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    with pytest.raises(BadGatewayException) as err:
        await create_consent("mock-id", ConnectorType.DEFENDER, "mock-brain-id")

    assert err.typename == "BadGatewayException"
    assert err.value.detail == "Invalid response from a downstream service. Please try again later"


@pytest.mark.asyncio
async def test_create_secret_exception(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.create_secret.side_effect = RuntimeError()
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    create_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {
            "client_id": "298342j382u34n83u2",
            "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890",
        },
        "properties": {
            "crowdstrike_url": "https://api.crowdstrike.com",
        },
    }

    with pytest.raises(BadGatewayException) as err:
        await create_secret("mock-id", "mock-brain-id", ConnectorSecretCreate(**create_secret_body))

    assert err.typename == "BadGatewayException"
    assert err.value.detail == "Invalid response from a downstream service. Please try again later"


@pytest.mark.asyncio
async def test_update_secret_exception(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.patch_named_secret.side_effect = RuntimeError()
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    update_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {
            "client_id": "298342j382u34n83u2",
            "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890",
        },
        "properties": {"crowdstrike_url": "https://api.crowdstrike.com"},
    }

    with pytest.raises(BadGatewayException) as err:
        await update_secret("mock-id", "mock-brain-id", ConnectorSecretUpdate(**update_secret_body))

    assert err.typename == "BadGatewayException"
    assert err.value.detail == "Invalid response from a downstream service. Please try again later"


@pytest.mark.asyncio
async def test_delete_secret_exception(mocker):
    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.delete_secret.side_effect = RuntimeError()
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    with pytest.raises(RuntimeError) as err:
        await delete_secret("mock-id", "mock-brain-id")

    assert err.typename == "RuntimeError"


@pytest.mark.asyncio
async def test_create_secret_conflict(mocker):
    class MockResponse:
        status_code = 409

    class HTTPError(Exception):
        def __init__(self):
            self.response = MockResponse()

    class ConflictError(RuntimeError):
        def __init__(self):
            super().__init__()
            self.__cause__ = HTTPError()

    mock_client = mocker.patch("nexus.dependencies.consent._client")
    mock_client.create_secret.side_effect = ConflictError()
    mocker.patch(
        "nexus.utils.utils.run_in_executor", AsyncMock(side_effect=lambda f, *args, **kwargs: f(*args, **kwargs))
    )

    create_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {
            "client_id": "298342j382u34n83u2",
            "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890",
        },
        "properties": {
            "crowdstrike_url": "https://api.crowdstrike.com",
        },
    }

    resp = await create_secret("mock-id", "mock-brain-id", ConnectorSecretCreate(**create_secret_body))
    assert resp is None
