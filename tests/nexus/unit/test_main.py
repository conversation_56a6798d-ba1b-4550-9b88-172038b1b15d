from unittest.mock import AsyncMock

from fastapi.testclient import <PERSON><PERSON>lient

from nexus.main import app
from nexus.services.notifications import NotificationManager


def test_root():
    with TestClient(app) as client:
        response = client.get("/")
        assert response.status_code == 200
        assert response.json() == {"Health": "OK"}


def test_lifespan_called_on_startup(mocker):
    mock_publish_event = mocker.patch.object(NotificationManager, "run", new_callable=AsyncMock)
    with TestClient(app):
        mock_publish_event.assert_called_once()
