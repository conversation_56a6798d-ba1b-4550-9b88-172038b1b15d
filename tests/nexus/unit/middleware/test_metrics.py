from fastapi.testclient import TestClient

from nexus.main import app
from nexus.middleware.metrics import extract_connector_id, normalize_url

SENSIBLE_SID = "db5dde43-8b58-499c-9f65-82b93a1c52d8"


def test_normalize_url():
    assert normalize_url(f"/v1/connectors/{SENSIBLE_SID}/setup/secrets") == "/v1/connectors/***/setup/secrets"
    assert normalize_url(f"/v1/connectors/{SENSIBLE_SID}") == "/v1/connectors/***"
    assert normalize_url("/v1/connectors") == "/v1/connectors"


def test_extract_connector_id():
    assert extract_connector_id("/v1/connectors/db5dde43-8b58-499c-9f65-82b93a1c52d8/setup/secrets") == SENSIBLE_SID
    assert extract_connector_id("/v1/connectors/db5dde43-8b58-499c-9f65-82b93a1c52d8") == SENSIBLE_SID
    assert extract_connector_id("/v1/connectors") is None
    assert extract_connector_id("/v1.connectors/BarneyRubble") is None


client = TestClient(app)


def test_successful_api_response(mocker):
    mock_metrics = mocker.patch("nexus.middleware.metrics.Metrics.timing")
    mock_logger = mocker.patch("nexus.api.exceptions.logger")

    response = client.get("/")

    assert response.status_code == 200
    assert response.json() == {"Health": "OK"}

    mock_metrics.assert_called_once()
    mock_logger.assert_not_called()


def test_client_error_api_response(mocker):
    mock_metrics = mocker.patch("nexus.middleware.metrics.Metrics.timing")
    mock_logger = mocker.patch("nexus.api.exceptions.logger")

    # body with incorrect connector type
    body = {"name": "mock-name", "connector_type": "mock"}
    response = client.post("/v1/connectors", headers={"x-internal-brain-id": "mock-brain-id"}, json=body)

    assert response.status_code == 422
    mock_metrics.assert_called_once()
    mock_logger.error.assert_called_once()
