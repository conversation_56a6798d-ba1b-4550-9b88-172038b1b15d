# pylint: disable=redefined-outer-name
import asyncio
from collections import namedtuple

import boto3
import pytest
import pytest_asyncio
from moto import mock_aws

from nexus.models import Connector, ConnectorSize, ConnectorState, SetupAction, SetupActionState
from nexus.schemas import ConnectorListResponse, ConnectorResponse
from nexus.utils.config import config


@pytest_asyncio.fixture(scope="function")
async def sns_client():
    """Asynchronous SNS client for tests"""
    mock_aws().reset()
    client = boto3.client("sns", region_name=config.REGION)

    class AsyncSNSClient:
        async def create_topic(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.create_topic(**kwargs))

        async def publish(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.publish(**kwargs))

        async def list_topics(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.list_topics(**kwargs))

    yield AsyncSNSClient()


@pytest_asyncio.fixture(scope="function", autouse=True)
async def sts_client():
    """Asynchronous STS client for tests (auto-used in all tests)"""
    mock_aws().reset()
    client = boto3.client("sts", region_name=config.REGION)

    class AsyncSTSClient:
        async def assume_role(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.assume_role(**kwargs))

    yield AsyncSTSClient()


@pytest.fixture(scope="function")
def connector_defaults():
    return {
        "pk": "mock_id",
        "sk": "connector",
        "name": "mock_name",
        "internal_brain_id": "mock_brain_id",
        "connector_type": "azure-cp",
        "connector_state": "setup",
        "operating_state": "inactive",
        "created_at": "2024-11-11T10:10:10Z",
        "updated_at": "2024-11-11T10:10:10Z",
        "first_log_received": None,
        "last_log_received": None,
        "size": ConnectorSize.MEDIUM,
        "last_sequence_id": None,
        "data": "test#setup",
        "setup_records": {},
        "error": None,
        "properties": {},
    }


@pytest.fixture(scope="function")
def defender_connector(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="defender",
        setup_records={SetupAction.CONSENT: SetupActionState.AWAITING},
        properties={"consent_url": "mock_url"},
    )


@pytest.fixture(scope="function")
def azure_cp_connector(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="azure-cp",
        setup_records={
            SetupAction.CONSENT: SetupActionState.AWAITING,
            SetupAction.PROPERTIES: SetupActionState.AWAITING,
        },
        properties={"consent_url": "mock_url"},
    )


@pytest.fixture(scope="function")
def azure_cp_connector_connecting(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="azure-cp",
        setup_records={
            SetupAction.CONSENT: SetupActionState.COMPLETED,
            SetupAction.PROPERTIES: SetupActionState.COMPLETED,
        },
        connector_state=ConnectorState.CONNECTING,
        properties={
            "consent_url": "mock_url",
            "ms_tenant_id": "mock-id",
            "resource_group_id": "mock_group_id",
        },
    )


@pytest.fixture(scope="function")
def crowdstrike_connector(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="crowdstrike", setup_records={SetupAction.SECRETS: SetupActionState.AWAITING}
    )


@pytest.fixture(scope="function")
def crowdstrike_connector_connecting(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="crowdstrike",
        setup_records={SetupAction.SECRETS: SetupActionState.COMPLETED},
        connector_state=ConnectorState.CONNECTING,
        properties={"crowdstrike_url": "https://api.crowdstrike.com"},
    )


@pytest.mark.asyncio
async def create_persisted_connector(base_repository, connector_defaults):
    """
    Factory function to create a connector with default or specified attributes
    """

    async def _create_connector(**kwargs):
        defaults = connector_defaults

        # Merge default connector data with overrides provided in kwargs
        connector_data = {**defaults, **kwargs}
        connector_data["data"] = f"{connector_data['connector_type']}#{connector_data['connector_state']}"

        await base_repository.put_item(connector_data)
        return Connector(**connector_data)

    return _create_connector


@pytest.fixture(scope="function")
def create_connector_object(connector_defaults) -> Connector:
    """
    Factory function to create a connector with default or specified attributes
    """

    def _create_connector(**kwargs):
        defaults = connector_defaults
        connector_data = {**defaults, **kwargs}
        connector_data["data"] = f"{connector_data['connector_type']}#{connector_data['connector_state']}"

        return Connector(**connector_data)

    return _create_connector


@pytest.fixture(scope="function")
def connector(connector_defaults):
    return Connector(**connector_defaults)


@pytest.fixture(scope="function")
def connector_controller_responses(connector):
    """Fixture that contains different types of connector responses"""
    ControllerResponses = namedtuple(
        "ControllerResponses", ["connector", "connector_list", "connector_list_with_next_page"]
    )
    connector_resp = ConnectorResponse(**connector.model_dump())
    connector_list = ConnectorListResponse(**{"Items": [connector_resp]})
    connector_list_with_next_link = ConnectorListResponse(
        **{"Items": [connector_resp], "LastEvaluatedKey": {"foo": "bar"}}
    )

    return ControllerResponses(
        connector=connector_resp,
        connector_list=connector_list,
        connector_list_with_next_page=connector_list_with_next_link,
    )


@pytest.fixture(scope="function")
def sentinelone_connector(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="sentinelone", setup_records={SetupAction.SECRETS: SetupActionState.AWAITING}
    )


@pytest.fixture(scope="function")
def sentinelone_connector_connecting(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="sentinelone",
        setup_records={SetupAction.SECRETS: SetupActionState.COMPLETED},
        connector_state=ConnectorState.CONNECTING,
        properties={"sentinelone_url": "https://usea1-partners.sentinelone.net"},
    )


@pytest.fixture(scope="function")
def aws_connector(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="aws", setup_records={SetupAction.PROPERTIES: SetupActionState.AWAITING}
    )


@pytest.fixture(scope="function")
def aws_connector_connecting(create_connector_object) -> Connector:
    return create_connector_object(
        connector_type="aws",
        setup_records={SetupAction.PROPERTIES: SetupActionState.COMPLETED},
        connector_state=ConnectorState.CONNECTING,
        properties={
            "s3_bucket_name": "mock-name",
            "iam_role_arn": "arn:aws:iam::123456789009:role/mock-name",
            "sns_topic_arn": "arn:aws:sns:us-west-2:123456789009:mock-name",
        },
    )
