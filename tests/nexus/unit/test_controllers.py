import pytest
from pydantic import <PERSON><PERSON><PERSON>pter
from vectra.consent_service_client.models import ConsentUrlResponse

from nexus.api.exceptions import ConflictException, ConsentNotCompletedException, NotFoundException
from nexus.controllers import (
    AwsConnectorController,
    AzureCPConnectorController,
    ConnectorController,
    CrowdstrikeConnectorController,
    DefenderConnectorController,
    SentinelOneConnectorController,
)
from nexus.models import Connector, ConnectorState, ConnectorType, SetupAction, SetupActionState
from nexus.schemas import (
    ConnectorAwsProperties,
    ConnectorAzureCPProperties,
    ConnectorCreate,
    ConnectorPropertiesInput,
    ConnectorSecretCreate,
    ConnectorSecretUpdate,
    ConnectorStateInput,
    ConnectorUpdate,
    ConsentGrantRecord,
    MicrosoftConsentGrant,
)

connector_state_input_adapter = TypeAdapter(ConnectorStateInput)


@pytest.mark.asyncio
async def test_base_controller_update_name(connector, connector_repository):
    await connector_repository.persist_connector(connector)

    data = {"name": "foo"}

    update_input = ConnectorUpdate(**data)

    controller = ConnectorController(connector_repository)
    response: Connector = await controller.update_connector(
        update_input, connector.connector_id, connector.internal_brain_id
    )

    assert response.name == "foo" and response.name != connector.name
    assert response.connector_type == "azure-cp" and response.connector_type == connector.connector_type


@pytest.mark.asyncio
async def test_base_controller_update_additional_fields(connector, connector_repository):
    """
    Test update with size and last_log_received fields added.
    """
    await connector_repository.persist_connector(connector)
    data = {"last_log_received": "2024-11-14T15:37:11.750Z", "size": "small"}

    update_input = ConnectorUpdate(**data)
    controller = ConnectorController(connector_repository)
    response: Connector = await controller.update_connector(
        update_input, connector.connector_id, connector.internal_brain_id
    )
    assert (
        response.last_log_received == "2024-11-14T15:37:11.750Z"
        and response.last_log_received != connector.last_log_received
    )
    assert response.size == "small"


@pytest.mark.asyncio
async def test_base_controller_delete(connector, connector_repository):
    await connector_repository.persist_connector(connector)
    controller = ConnectorController(connector_repository)
    response: Connector = await controller.soft_delete_connector(connector.connector_id, connector.internal_brain_id)

    assert response.connector_state == "deleted"
    assert response.operating_state == "inactive"
    assert (await connector_repository.get_item(pk="mock_id", sk="connector"))[
        "data"
    ] == f"{response.connector_type}#deleted"


@pytest.mark.asyncio
async def test_base_controller_update_state(connector, connector_repository):
    await connector_repository.persist_connector(connector)
    controller = ConnectorController(connector_repository)

    assert connector.operating_state == "inactive"
    assert connector.first_log_received is None

    # initial update to connected
    connected_state_data = {"connector_state": "connected"}
    update_input = connector_state_input_adapter.validate_python(connected_state_data)
    response: Connector = await controller.update_connector_state(update_input, connector.connector_id)
    first_log_received = response.first_log_received
    assert response.connector_state == "connected"
    assert response.operating_state == "active"
    assert first_log_received is not None
    assert response.error is None
    assert (await connector_repository.get_item(pk=connector.connector_id, sk="connector"))[
        "data"
    ] == "azure-cp#connected"

    # update from connected -> error
    error_state_data = {"connector_state": "error", "error": {"error_code": "BAD", "error_message": "failure"}}
    update_input = connector_state_input_adapter.validate_python(error_state_data)
    response: Connector = await controller.update_connector_state(update_input, connector.connector_id)
    assert response.connector_state == "error" and response.connector_state != connector.connector_state
    assert response.operating_state == "active"
    assert response.error.model_dump() == {"error_code": "BAD", "error_message": "failure"}
    assert (await connector_repository.get_item(pk=connector.connector_id, sk="connector"))["data"] == "azure-cp#error"

    # update from error -> connected
    update_input = connector_state_input_adapter.validate_python(connected_state_data)
    response: Connector = await controller.update_connector_state(update_input, connector.connector_id)
    assert response.first_log_received == first_log_received
    assert response.operating_state == "active"
    assert (await connector_repository.get_item(pk=connector.connector_id, sk="connector"))[
        "data"
    ] == "azure-cp#connected"
    assert response.error is None


@pytest.mark.parametrize(
    "connector_data",
    [
        {"name": "mock-name", "connector_type": ConnectorType.DEFENDER},
        {
            "name": "mock-name",
            "connector_type": ConnectorType.DEFENDER,
            "connector_id": "550e8400-e29b-41d4-a716-446655440000",
        },
    ],
)
@pytest.mark.asyncio
async def test_defender_controller_create(mocker, connector_repository, connector_data):
    create_consent = mocker.patch("nexus.controllers.create_consent")
    create_consent.return_value = ConsentUrlResponse(consent_url="mock-url")

    controller = DefenderConnectorController(connector_repository)

    connector = await controller.create_connector(ConnectorCreate(**connector_data), "mock-brain-id")

    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    assert persisted_connector.properties["consent_url"] == "mock-url"
    if "connector_id" in connector_data:
        assert persisted_connector.connector_id == connector_data["connector_id"]


@pytest.mark.asyncio
async def test_defender_setup_consent(defender_connector, connector_repository):
    await connector_repository.persist_connector(defender_connector)

    controller = DefenderConnectorController(connector_repository)

    ms_consent_grant = MicrosoftConsentGrant(connector_type="defender", ms_tenant_id="mock_tenant_id")
    consent_record = ConsentGrantRecord(properties=ms_consent_grant)

    await controller.setup_consent(defender_connector.connector_id, consent_record)

    persisted_connector = await connector_repository.get_connector(defender_connector.connector_id)

    assert persisted_connector.connector_state == ConnectorState.CONNECTING
    assert persisted_connector.properties["ms_tenant_id"] == "mock_tenant_id"
    assert persisted_connector.setup_records[SetupAction.CONSENT] == SetupActionState.COMPLETED


@pytest.mark.asyncio
async def test_azure_cp_setup_properties(azure_cp_connector, connector_repository):
    azure_cp_connector.setup_records[SetupAction.CONSENT] = SetupActionState.COMPLETED
    await connector_repository.persist_connector(azure_cp_connector)

    controller = ConnectorController(connector_repository)

    request_properties = ConnectorAzureCPProperties(connector_type="azure-cp", resource_group_id="mock_group_id")
    props = ConnectorPropertiesInput(properties=request_properties)

    await controller.setup_properties(azure_cp_connector.connector_id, azure_cp_connector.internal_brain_id, props)

    persisted_connector = await connector_repository.get_connector(azure_cp_connector.connector_id)

    assert persisted_connector.connector_state == ConnectorState.CONNECTING
    assert persisted_connector.properties["resource_group_id"] == "mock_group_id"
    assert persisted_connector.setup_records[SetupAction.PROPERTIES] == SetupActionState.COMPLETED


@pytest.mark.asyncio
async def test_azure_cp_setup_properties_enforces_consent_first(azure_cp_connector, connector_repository):
    """Test that AzureCPConnectorController enforces consent setup before properties setup"""
    azure_cp_connector.setup_records[SetupAction.CONSENT] = SetupActionState.AWAITING
    await connector_repository.persist_connector(azure_cp_connector)

    controller = AzureCPConnectorController(connector_repository)

    request_properties = ConnectorAzureCPProperties(
        connector_type=ConnectorType.AZURE_CP, resource_group_id="mock_group_id"
    )
    props = ConnectorPropertiesInput(properties=request_properties)

    with pytest.raises(ConsentNotCompletedException) as exc:
        await controller.setup_properties(azure_cp_connector.connector_id, azure_cp_connector.internal_brain_id, props)

    assert "Consent setup must be completed before properties setup" in str(exc.value.detail)

    persisted_connector = await connector_repository.get_connector(azure_cp_connector.connector_id)
    assert persisted_connector.connector_state == ConnectorState.SETUP
    assert persisted_connector.setup_records[SetupAction.PROPERTIES] == SetupActionState.AWAITING
    assert "resource_group_id" not in persisted_connector.properties


@pytest.mark.asyncio
async def test_azure_cp_setup_properties_succeeds_when_consent_completed(azure_cp_connector, connector_repository):
    """Test that properties setup succeeds when consent is completed"""
    azure_cp_connector.setup_records[SetupAction.CONSENT] = SetupActionState.COMPLETED
    await connector_repository.persist_connector(azure_cp_connector)

    controller = AzureCPConnectorController(connector_repository)

    request_properties = ConnectorAzureCPProperties(
        connector_type=ConnectorType.AZURE_CP, resource_group_id="mock_group_id"
    )
    props = ConnectorPropertiesInput(properties=request_properties)

    await controller.setup_properties(azure_cp_connector.connector_id, azure_cp_connector.internal_brain_id, props)

    persisted_connector = await connector_repository.get_connector(azure_cp_connector.connector_id)
    assert persisted_connector.connector_state == ConnectorState.CONNECTING
    assert persisted_connector.properties["resource_group_id"] == "mock_group_id"
    assert persisted_connector.setup_records[SetupAction.PROPERTIES] == SetupActionState.COMPLETED


@pytest.mark.asyncio
async def test_azure_cp_setup_properties_with_setup_error(azure_cp_connector, connector_repository):
    """Test connector moving from setup error to connecting has no error value"""
    azure_cp_connector.setup_records[SetupAction.CONSENT] = SetupActionState.COMPLETED
    azure_cp_connector.properties["ms_tenant_id"] = "mock_tenant_id"
    azure_cp_connector.connector_state = ConnectorState.ERROR
    azure_cp_connector.error = {
        "error_code": "TOKEN_VALIDATION_ERROR",
        "error_message": "Unexpected error when validating token permissions",
    }
    await connector_repository.persist_connector(azure_cp_connector)

    controller = AzureCPConnectorController(connector_repository)

    request_properties = ConnectorAzureCPProperties(
        connector_type=ConnectorType.AZURE_CP, resource_group_id="mock_group_id"
    )
    props = ConnectorPropertiesInput(properties=request_properties)

    await controller.setup_properties(azure_cp_connector.connector_id, azure_cp_connector.internal_brain_id, props)

    persisted_connector = await connector_repository.get_connector(azure_cp_connector.connector_id)
    assert persisted_connector.connector_state == ConnectorState.CONNECTING
    assert persisted_connector.error is None
    assert persisted_connector.properties["resource_group_id"] == "mock_group_id"
    assert persisted_connector.setup_records[SetupAction.PROPERTIES] == SetupActionState.COMPLETED


@pytest.mark.parametrize(
    "connector_data",
    [
        {"name": "mock-name", "connector_type": ConnectorType.CROWDSTRIKE},
        {
            "name": "mock-name",
            "connector_type": ConnectorType.CROWDSTRIKE,
            "connector_id": "550e8400-e29b-41d4-a716-446655440000",
        },
    ],
)
@pytest.mark.asyncio
async def test_crowdstrike_controller_create(connector_repository, connector_data):
    controller = CrowdstrikeConnectorController(connector_repository)

    connector = await controller.create_connector(ConnectorCreate(**connector_data), "mock-brain-id")

    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    assert persisted_connector.setup_records == {SetupAction.SECRETS: SetupActionState.AWAITING}
    if "connector_id" in connector_data:
        assert persisted_connector.connector_id == connector_data["connector_id"]


@pytest.mark.asyncio
async def test_crowdstrike_controller_setup_secrets(mocker, connector_repository):
    create_secret = mocker.patch("nexus.controllers.create_secret")
    create_secret.return_value = None

    controller = CrowdstrikeConnectorController(connector_repository)
    connector = await controller.create_connector(
        ConnectorCreate(name="mock-name", connector_type=ConnectorType.CROWDSTRIKE), "mock-brain-id"
    )

    create_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {
            "client_id": "298342j382u34n83u2",
            "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890",
        },
        "properties": {"crowdstrike_url": "https://api.crowdstrike.com"},
    }
    await controller.setup_secrets(
        connector.connector_id, connector.internal_brain_id, ConnectorSecretCreate(**create_secret_body)
    )

    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    assert persisted_connector.setup_records == {SetupAction.SECRETS: SetupActionState.COMPLETED}
    assert persisted_connector.connector_state == ConnectorState.CONNECTING
    assert persisted_connector.properties["crowdstrike_url"] == "https://api.crowdstrike.com"


@pytest.mark.asyncio
async def test_crowdstrike_controller_update_secret(mocker, connector_repository):
    create_secret = mocker.patch("nexus.controllers.create_secret")
    create_secret.return_value = None
    update_secret = mocker.patch("nexus.controllers.update_secret")
    update_secret.return_value = None

    controller = CrowdstrikeConnectorController(connector_repository)
    # Create a connector and setup secret
    connector = await controller.create_connector(
        ConnectorCreate(name="mock-name", connector_type=ConnectorType.CROWDSTRIKE), "mock-brain-id"
    )
    create_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {"client_id": "298342j382u34n83u2", "client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890"},
        "properties": {"crowdstrike_url": "https://api.v1.crowdstrike.com"},
    }
    await controller.setup_secrets(
        connector.connector_id, connector.internal_brain_id, ConnectorSecretCreate(**create_secret_body)
    )

    # Update the connector secret and url
    update_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {"client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890"},
        "properties": {"crowdstrike_url": "https://api.v2.crowdstrike.com"},
    }
    secret_body = ConnectorSecretUpdate(**update_secret_body)
    await controller.update_secret(connector.connector_id, connector.internal_brain_id, secret_body)
    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    # Assert that request to dependencies are made
    update_secret.assert_called_once()

    # Assert that connector values are updated
    assert persisted_connector.properties["crowdstrike_url"] == "https://api.v2.crowdstrike.com"
    assert persisted_connector.connector_state == ConnectorState.CONNECTING

    # Update the url only
    update_secret.reset_mock()
    update_secret_body = {
        "connector_type": "crowdstrike",
        "properties": {"crowdstrike_url": "https://api.v3.crowdstrike.com"},
    }
    secret_body = ConnectorSecretUpdate(**update_secret_body)
    await controller.update_secret(connector.connector_id, connector.internal_brain_id, secret_body)
    # Assert that consent.update_secret wasn't called and url was updated
    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    update_secret.assert_not_called()
    assert persisted_connector.properties["crowdstrike_url"] == "https://api.v3.crowdstrike.com"
    assert persisted_connector.connector_state == ConnectorState.CONNECTING

    # Update the secret only
    update_secret_body = {
        "connector_type": "crowdstrike",
        "secret": {"client_secret": "abcD1234EFghIjkLmNoPqRstUvWxYz567890"},
    }
    secret_body = ConnectorSecretUpdate(**update_secret_body)
    await controller.update_secret(connector.connector_id, connector.internal_brain_id, secret_body)

    # Assert that consent.update_secret was called once and url is unchanged
    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    update_secret.assert_called_once()
    assert persisted_connector.properties["crowdstrike_url"] == "https://api.v3.crowdstrike.com"
    assert persisted_connector.connector_state == ConnectorState.CONNECTING


@pytest.mark.asyncio
async def test_delete_connector_removes_ms_tenant_id_if_exists(azure_cp_connector, connector_repository):
    azure_cp_connector.properties["ms_tenant_id"] = "mock_tenant_id"
    await connector_repository.persist_connector(azure_cp_connector)

    controller = ConnectorController(connector_repository)

    response: Connector = await controller.soft_delete_connector(
        azure_cp_connector.connector_id, azure_cp_connector.internal_brain_id
    )

    assert response.connector_state == ConnectorState.DELETED
    assert response.properties["ms_tenant_id"] is None


@pytest.mark.asyncio
async def test_delete_connector_removes_azure_properties_if_exists(azure_cp_connector_connecting, connector_repository):
    await connector_repository.persist_connector(azure_cp_connector_connecting)

    controller = ConnectorController(connector_repository)

    response: Connector = await controller.soft_delete_connector(
        azure_cp_connector_connecting.connector_id, azure_cp_connector_connecting.internal_brain_id
    )

    assert response.connector_state == ConnectorState.DELETED
    assert response.properties["ms_tenant_id"] is None
    assert response.properties["resource_group_id"] is None


@pytest.mark.asyncio
async def test_delete_connector_ms_tenant_id_does_not_exist(azure_cp_connector, connector_repository):
    await connector_repository.persist_connector(azure_cp_connector)

    controller = ConnectorController(connector_repository)

    response: Connector = await controller.soft_delete_connector(
        azure_cp_connector.connector_id, azure_cp_connector.internal_brain_id
    )

    assert response.connector_state == ConnectorState.DELETED
    assert "ms_tenant_id" not in response.properties


@pytest.mark.asyncio
async def test_delete_connector_makes_request_to_delete_secret_if_exists(
    mocker, crowdstrike_connector_connecting: Connector, connector_repository
):
    delete_secret = mocker.patch("nexus.controllers.delete_secret")
    delete_secret.return_value = None

    await connector_repository.persist_connector(crowdstrike_connector_connecting)

    controller = ConnectorController(connector_repository)

    response: Connector = await controller.soft_delete_connector(
        crowdstrike_connector_connecting.connector_id, crowdstrike_connector_connecting.internal_brain_id
    )

    delete_secret.assert_called_once()
    assert response.connector_state == ConnectorState.DELETED


def test_setup_complete():
    setup_records = {
        "mock-record-1": SetupActionState.COMPLETED,
        "mock-record-2": SetupActionState.COMPLETED,
        "mock-record-3": SetupActionState.COMPLETED,
    }

    assert ConnectorController._is_setup_complete(setup_records) is True


def test_setup_not_complete():
    setup_records = {
        "mock-record-1": SetupActionState.COMPLETED,
        "mock-record-2": SetupActionState.AWAITING,
        "mock-record-3": SetupActionState.COMPLETED,
    }

    assert ConnectorController._is_setup_complete(setup_records) is False


def test_setup_complete_empty_records():
    assert ConnectorController._is_setup_complete({}) is True


@pytest.mark.parametrize(
    "connector_data",
    [
        {"name": "mock-aws-controller", "connector_type": ConnectorType.AWS},
        {
            "name": "mock-name",
            "connector_type": ConnectorType.AWS,
            "connector_id": "bd0f6dac-d527-47ac-bcf4-9ec9e3aba7ea",
        },
    ],
)
@pytest.mark.asyncio
async def test_aws_controller_create(connector_repository, connector_data):
    controller = AwsConnectorController(connector_repository)

    connector = await controller.create_connector(ConnectorCreate(**connector_data), "mock-brain-id")

    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    assert persisted_connector.setup_records == {SetupAction.PROPERTIES: SetupActionState.AWAITING}
    if "connector_id" in connector_data:
        assert persisted_connector.connector_id == connector_data["connector_id"]


@pytest.mark.asyncio
async def test_aws_controller_setup_properties(aws_connector, connector_repository):
    await connector_repository.persist_connector(aws_connector)
    controller = ConnectorController(connector_repository)
    request_properties = ConnectorAwsProperties(
        connector_type="aws",
        s3_bucket_name="mock-bucket",
        iam_role_arn="arn:aws:iam::123456789009:role/mock-bucket",
        sns_topic_arn="arn:aws:sns:us-west-2:123456789009:mock-bucket",
    )
    props = ConnectorPropertiesInput(properties=request_properties)
    await controller.setup_properties(aws_connector.connector_id, aws_connector.internal_brain_id, props)
    persisted_connector = await connector_repository.get_connector(aws_connector.connector_id)
    assert persisted_connector.connector_state == ConnectorState.CONNECTING
    assert persisted_connector.properties["s3_bucket_name"] == "mock-bucket"
    assert persisted_connector.properties["iam_role_arn"] == "arn:aws:iam::123456789009:role/mock-bucket"
    assert persisted_connector.properties["sns_topic_arn"] == "arn:aws:sns:us-west-2:123456789009:mock-bucket"
    assert persisted_connector.setup_records[SetupAction.PROPERTIES] == SetupActionState.COMPLETED


@pytest.mark.parametrize(
    "connector_data",
    [
        {"name": "mock-name", "connector_type": ConnectorType.SENTINELONE},
        {
            "name": "mock-name",
            "connector_type": ConnectorType.SENTINELONE,
            "connector_id": "550e8400-e29b-41d4-a716-446655440000",
        },
    ],
)
@pytest.mark.asyncio
async def test_sentinelone_controller_create(connector_repository, connector_data):
    controller = SentinelOneConnectorController(connector_repository)

    connector = await controller.create_connector(ConnectorCreate(**connector_data), "mock-brain-id")

    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    assert persisted_connector.setup_records == {SetupAction.SECRETS: SetupActionState.AWAITING}
    if "connector_id" in connector_data:
        assert persisted_connector.connector_id == connector_data["connector_id"]


@pytest.mark.asyncio
async def test_sentinelone_controller_setup_secrets(mocker, connector_repository):
    create_secret = mocker.patch("nexus.controllers.create_secret")
    create_secret.return_value = None

    controller = SentinelOneConnectorController(connector_repository)
    connector = await controller.create_connector(
        ConnectorCreate(name="mock-name", connector_type=ConnectorType.SENTINELONE), "mock-brain-id"
    )

    create_secret_body = {
        "connector_type": "sentinelone",
        "secret": {
            "api_token": "token-secret",
        },
        "properties": {"sentinelone_url": "https://usea1-partners.sentinelone.net"},
    }
    await controller.setup_secrets(
        connector.connector_id, connector.internal_brain_id, ConnectorSecretCreate(**create_secret_body)
    )

    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    assert persisted_connector.setup_records == {SetupAction.SECRETS: SetupActionState.COMPLETED}
    assert persisted_connector.connector_state == ConnectorState.CONNECTING
    assert persisted_connector.properties["sentinelone_url"] == "https://usea1-partners.sentinelone.net"


@pytest.mark.asyncio
async def test_sentinelone_controller_update_secret(mocker, connector_repository):
    create_secret = mocker.patch("nexus.controllers.create_secret")
    create_secret.return_value = None
    update_secret = mocker.patch("nexus.controllers.update_secret")
    update_secret.return_value = None

    controller = SentinelOneConnectorController(connector_repository)
    # Create a connector and setup secret
    connector = await controller.create_connector(
        ConnectorCreate(name="mock-name", connector_type=ConnectorType.SENTINELONE), "mock-brain-id"
    )
    create_secret_body = {
        "connector_type": "sentinelone",
        "secret": {
            "api_token": "token-secret",
        },
        "properties": {"sentinelone_url": "https://usea1-partners.sentinelone.net"},
    }
    await controller.setup_secrets(
        connector.connector_id, connector.internal_brain_id, ConnectorSecretCreate(**create_secret_body)
    )

    # Update the connector secret and url
    update_secret_body = {
        "connector_type": "sentinelone",
        "secret": {
            "api_token": "token-secret",
        },
        "properties": {"sentinelone_url": "https://usea1-partners.sentinelone.net"},
    }
    secret_body = ConnectorSecretUpdate(**update_secret_body)
    await controller.update_secret(connector.connector_id, connector.internal_brain_id, secret_body)
    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    # Assert that request to dependencies are made
    update_secret.assert_called_once()

    # Assert that connector values are updated
    assert persisted_connector.properties["sentinelone_url"] == "https://usea1-partners.sentinelone.net"
    assert persisted_connector.connector_state == ConnectorState.CONNECTING

    # Update the url only
    update_secret.reset_mock()
    update_secret_body = {
        "connector_type": "sentinelone",
        "properties": {"sentinelone_url": "https://usea1-partners.sentinelone.net"},
    }
    secret_body = ConnectorSecretUpdate(**update_secret_body)
    await controller.update_secret(connector.connector_id, connector.internal_brain_id, secret_body)
    # Assert that consent.update_secret wasn't called and url was updated
    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    update_secret.assert_not_called()
    assert persisted_connector.properties["sentinelone_url"] == "https://usea1-partners.sentinelone.net"
    assert persisted_connector.connector_state == ConnectorState.CONNECTING

    # Update the secret only
    update_secret_body = {
        "connector_type": "sentinelone",
        "secret": {
            "api_token": "new-token-secret",
        },
    }
    secret_body = ConnectorSecretUpdate(**update_secret_body)
    await controller.update_secret(connector.connector_id, connector.internal_brain_id, secret_body)

    # Assert that consent.update_secret was called once and url is unchanged
    persisted_connector = await connector_repository.get_connector(connector.connector_id)
    update_secret.assert_called_once()
    assert persisted_connector.properties["sentinelone_url"] == "https://usea1-partners.sentinelone.net"
    assert persisted_connector.connector_state == ConnectorState.CONNECTING


@pytest.mark.asyncio
async def test_validate_unique_name_fails_on_duplicate(connector, connector_repository):
    await connector_repository.persist_connector(connector)
    controller = ConnectorController(connector_repository)

    with pytest.raises(ConflictException) as exc:
        await controller._validate_unique_name(connector.name, connector.internal_brain_id)

    assert str(exc.value.detail) == f"Connector with name '{connector.name}' already exists for this brain"


@pytest.mark.asyncio
async def test_validate_unique_name_allows_same_name_different_brain(connector, connector_repository):
    await connector_repository.persist_connector(connector)

    controller = ConnectorController(connector_repository)

    # Should not raise an exception
    await controller._validate_unique_name(connector.name, "different-brain-id")


@pytest.mark.parametrize(
    "setup_data",
    [
        {"name": "test-name", "same_name": True},
        {"name": "other-name", "same_name": False},
    ],
)
@pytest.mark.asyncio
async def test_validate_unique_name_allows_update_same_name(connector, connector_repository, setup_data):
    await connector_repository.persist_connector(connector)
    controller = ConnectorController(connector_repository)

    # Should not raise an exception when excluding current connector
    await controller._validate_unique_name(setup_data["name"], connector.internal_brain_id, connector.connector_id)


@pytest.mark.parametrize(
    "connector_data",
    [
        {"name": "mock-name", "connector_type": ConnectorType.DEFENDER},
        {
            "name": "mock-name",
            "connector_type": ConnectorType.DEFENDER,
            "connector_id": "550e8400-e29b-41d4-a716-446655440000",
        },
    ],
)
@pytest.mark.asyncio
async def test_validate_unique_name_on_create(mocker, connector_repository, connector_data):
    create_consent_mock = mocker.patch("nexus.controllers.create_consent")
    create_consent_mock.return_value = ConsentUrlResponse(consent_url="mock-url")

    controller = DefenderConnectorController(connector_repository)

    # First creation should succeed
    first_connector = await controller.create_connector(ConnectorCreate(**connector_data), "mock-brain-id")
    assert first_connector.name == connector_data["name"]

    # Second creation with same name should fail
    with pytest.raises(ConflictException) as exc:
        await controller.create_connector(ConnectorCreate(**connector_data), "mock-brain-id")
    assert str(exc.value.detail) == f"Connector with name '{connector_data['name']}' already exists for this brain"


@pytest.mark.asyncio
async def test_validate_unique_name_on_update(mocker, connector, connector_repository):
    create_consent_mock = mocker.patch("nexus.controllers.create_consent")
    create_consent_mock.return_value = ConsentUrlResponse(consent_url="mock-url")

    # Create first connector
    await connector_repository.persist_connector(connector)

    # Create second connector
    controller = DefenderConnectorController(connector_repository)
    second_connector = await controller.create_connector(
        ConnectorCreate(name="other-name", connector_type=ConnectorType.DEFENDER), connector.internal_brain_id
    )

    # Try to update second connector's name to first connector's name
    update_input = ConnectorUpdate(name=connector.name)

    # Should fail when trying to use first connector's name
    with pytest.raises(ConflictException) as exc:
        await controller.update_connector(
            update_input, second_connector.connector_id, second_connector.internal_brain_id
        )

    assert str(exc.value.detail) == f"Connector with name '{connector.name}' already exists for this brain"

    # Should succeed when updating with a new unique name
    new_name = "new-unique-name"
    update_input = ConnectorUpdate(name=new_name)
    updated_connector = await controller.update_connector(
        update_input, second_connector.connector_id, second_connector.internal_brain_id
    )
    assert updated_connector.name == new_name


@pytest.mark.asyncio
async def test_validate_unique_name_on_update_is_skipped_when_name_unchanged(mocker, connector, connector_repository):
    create_consent_mock = mocker.patch("nexus.controllers.create_consent")
    create_consent_mock.return_value = ConsentUrlResponse(consent_url="mock-url")

    # Create first connector
    await connector_repository.persist_connector(connector)

    # Create second connector
    controller = DefenderConnectorController(connector_repository)
    second_connector = await controller.create_connector(
        ConnectorCreate(name="other-name", connector_type=ConnectorType.DEFENDER), connector.internal_brain_id
    )
    # Give second connector the same name as first connector. (This shouldn't happen in practice)
    second_connector.name = connector.name
    await connector_repository.persist_connector(second_connector)

    # Try to update second connector's name to its current name (which is also a duplicate of the first connector)
    update_input = ConnectorUpdate(name=second_connector.name)

    # Should succeed when trying update name even though it is a duplicate because it isn't changing the name
    updated_connector = await controller.update_connector(
        update_input, second_connector.connector_id, second_connector.internal_brain_id
    )

    # Should succeed without updating the name.
    assert updated_connector.name == second_connector.name


@pytest.mark.asyncio
async def test_validate_unique_name_allows_deleted_name_reuse(mocker, connector, connector_repository):
    create_consent_mock = mocker.patch("nexus.controllers.create_consent")
    create_consent_mock.return_value = ConsentUrlResponse(consent_url="mock-url")

    # Create and delete first connector
    await connector_repository.persist_connector(connector)
    controller = ConnectorController(connector_repository)
    await controller.soft_delete_connector(connector.connector_id, connector.internal_brain_id)

    # Should be able to create new connector with same name
    new_controller = DefenderConnectorController(connector_repository)
    new_connector = await new_controller.create_connector(
        ConnectorCreate(name=connector.name, connector_type=ConnectorType.DEFENDER), connector.internal_brain_id
    )

    assert new_connector.name == connector.name
    assert new_connector.connector_id != connector.connector_id


@pytest.mark.asyncio
async def test_validate_not_deleted(connector, connector_repository):
    """Test that operations on deleted connectors raise NotFoundException"""
    # Setup a connector in DELETED state
    connector.connector_state = ConnectorState.DELETED
    await connector_repository.persist_connector(connector)

    # Test direct validation
    controller = ConnectorController(connector_repository)
    with pytest.raises(NotFoundException) as exc:
        await controller._set_and_validate_connector(connector.connector_id)
    assert str(exc.value.detail) == "Connector has been deleted"

    # Test update_connector
    controller = ConnectorController(connector_repository)
    update_input = ConnectorUpdate(name="new-name")
    with pytest.raises(NotFoundException) as exc:
        await controller.update_connector(update_input, connector.connector_id, connector.internal_brain_id)
    assert str(exc.value.detail) == "Connector has been deleted"

    # Test update_connector_state
    controller = ConnectorController(connector_repository)
    state_input = connector_state_input_adapter.validate_python({"connector_state": "connected"})
    with pytest.raises(NotFoundException) as exc:
        await controller.update_connector_state(state_input, connector.connector_id)
    assert str(exc.value.detail) == "Connector has been deleted"

    # Test setup_consent
    controller = ConnectorController(connector_repository)
    consent_record = ConsentGrantRecord(
        properties=MicrosoftConsentGrant(connector_type="defender", ms_tenant_id="mock_tenant_id")
    )
    with pytest.raises(NotFoundException) as exc:
        await controller.setup_consent(connector.connector_id, consent_record)
    assert str(exc.value.detail) == "Connector has been deleted"

    # Test setup_properties
    controller = ConnectorController(connector_repository)
    properties_input = ConnectorPropertiesInput(
        properties=ConnectorAzureCPProperties(connector_type="azure-cp", resource_group_id="test-id")
    )
    with pytest.raises(NotFoundException) as exc:
        await controller.setup_properties(connector.connector_id, connector.internal_brain_id, properties_input)
    assert str(exc.value.detail) == "Connector has been deleted"

    # Test setup_secrets
    controller = ConnectorController(connector_repository)
    secret_create = ConnectorSecretCreate(
        connector_type="crowdstrike",
        secret={"client_id": "test-id", "client_secret": "test-secret"},
        properties={"crowdstrike_url": "https://api.crowdstrike.com"},
    )
    with pytest.raises(NotFoundException) as exc:
        await controller.setup_secrets(connector.connector_id, connector.internal_brain_id, secret_create)
    assert str(exc.value.detail) == "Connector has been deleted"

    # Test update_secret
    controller = ConnectorController(connector_repository)
    secret_update = ConnectorSecretUpdate(connector_type="crowdstrike", secret={"client_secret": "new-secret"})
    with pytest.raises(NotFoundException) as exc:
        await controller.update_secret(connector.connector_id, connector.internal_brain_id, secret_update)
    assert str(exc.value.detail) == "Connector has been deleted"
