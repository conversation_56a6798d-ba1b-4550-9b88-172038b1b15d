import asyncio
import os

import boto3


async def setup_table(dynamo_db_client):
    return await dynamo_db_client.create_table(
        TableName=os.getenv("NEXUS_TABLE_NAME"),
        BillingMode="PAY_PER_REQUEST",
        KeySchema=[{"AttributeName": "pk", "KeyType": "HASH"}, {"AttributeName": "sk", "KeyType": "RANGE"}],
        AttributeDefinitions=[
            {"AttributeName": "pk", "AttributeType": "S"},
            {"AttributeName": "sk", "AttributeType": "S"},
            {"AttributeName": "internal_brain_id", "AttributeType": "S"},
            {"AttributeName": "operating_state", "AttributeType": "S"},
            {"AttributeName": "data", "AttributeType": "S"},
        ],
        GlobalSecondaryIndexes=[
            {
                "IndexName": "operating_state_gsi",
                "KeySchema": [
                    {"AttributeName": "operating_state", "KeyType": "HASH"},
                    {"AttributeName": "data", "KeyType": "RANGE"},
                ],
                "Projection": {"ProjectionType": "ALL"},
            },
            {
                "IndexName": "internal_brain_id_gsi",
                "KeySchema": [
                    {"AttributeName": "internal_brain_id", "KeyType": "HASH"},
                    {"AttributeName": "operating_state", "KeyType": "RANGE"},
                ],
                "Projection": {"ProjectionType": "ALL"},
            },
        ],
    )


if __name__ == "__main__":

    async def main():
        client = boto3.client("dynamodb", endpoint_url=os.getenv("DDB_ENDPOINT_URL"))

        class AsyncDynamoDBClient:
            async def create_table(self, **kwargs):
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, lambda: client.create_table(**kwargs))

        await setup_table(AsyncDynamoDBClient())

    asyncio.run(main())
