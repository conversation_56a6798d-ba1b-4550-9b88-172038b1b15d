"""
Mock service for Local Sensible
"""

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>
from vectra.consent_service_client.models import ConsentUrlResponse

app = FastAPI()


# --------------------------------------------
#
# CONSENT SERVICE
#
# --------------------------------------------
@app.post("/consent/url")
async def generate_url():
    """
    Get token from consent service
    """

    return ConsentUrlResponse(consent_url="https://mock-consent.ai")


@app.put("/consent/secrets")
async def create_secret():
    """
    Create secret
    """
    return None


@app.patch("/consent/secrets/{external_id}/{named_secret}")
async def patch_named_secret(external_id, named_secret):
    """
    Update a secret
    """
    return None


@app.delete("/consent/secrets/{external_id}")
async def delete_secret(external_id):
    """
    Delete secret
    """
    return None


if __name__ == "__main__":
    uvicorn.run("tests.nexus.utils.mock_api:app", host="0.0.0.0", port=8002, workers=1)
