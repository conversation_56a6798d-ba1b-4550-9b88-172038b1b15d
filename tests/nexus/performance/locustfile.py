"""
Nexus Performance Testing
"""

import logging
import os
import threading
import time

from locust import FastHttpUser, constant_pacing, events, task
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.table import Table

FORMAT = "%(message)s"
logging.basicConfig(level="NOTSET", format=FORMAT, datefmt="[%X]", handlers=[RichHandler()])
log = logging.getLogger("rich")

NPT_LAST_LOG_RECEIVED_CONNECTOR_ID = os.getenv("NPT_LAST_LOG_RECEIVED_CONNECTOR_ID")
NPT_VUI_INTERNAL_BRAIN_ID = os.getenv("NPT_VUI_INTERNAL_BRAIN_ID")


class RequestStats:
    def __init__(self):
        # Output console for table
        self.console = Console()
        # stats
        self._start_time = None
        self.total_requests_sent = 0
        self.total_responses_received = 0
        self.bucket_requests_sent = 0
        self.bucket_responses_received = 0

    def increment_requests_sent(self):
        self.total_requests_sent += 1
        self.bucket_requests_sent += 1

    def increment_responses_received(self):
        self.total_responses_received += 1
        self.bucket_responses_received += 1

    def reset_bucket(self):
        self.bucket_requests_sent = 0
        self.bucket_responses_received = 0

    def start(self):
        """Start stats collection"""
        self._start_time = time.monotonic()

    def stop(self):
        """Stop stats collection"""
        self._start_time = None
        self.total_requests_sent = 0
        self.total_responses_received = 0
        self.bucket_requests_sent = 0
        self.bucket_responses_received = 0

    def log_stats(self, bucket_duration):
        """Calculate stats and output to terminal"""
        if self._start_time:
            now = time.monotonic()
            bucket_requests_per_sec = self.bucket_requests_sent / bucket_duration
            bucket_responses_per_sec = self.bucket_responses_received / bucket_duration

            total_elapsed = now - self._start_time
            requests_per_sec = self.total_requests_sent / total_elapsed if total_elapsed > 0 else 0
            responses_per_sec = self.total_responses_received / total_elapsed if total_elapsed > 0 else 0
            in_flight = self.total_requests_sent - self.total_responses_received

            table = Table(title=f"Request Stats (Updated every {bucket_duration}s)")
            table.add_column("Type", justify="left", style="cyan")
            table.add_column("Requests Sent", justify="right")
            table.add_column("Responses Received", justify="right")
            table.add_column("Requests In Flight", justify="right")
            table.add_column("Sent RPS", justify="right")
            table.add_column("Received RPS", justify="right")

            table.add_row(
                "total",
                str(self.total_requests_sent),
                str(self.total_responses_received),
                str(in_flight),
                f"{requests_per_sec:.2f}",
                f"{responses_per_sec:.2f}",
            )

            table.add_row(
                f"last {bucket_duration}s",
                str(self.bucket_requests_sent),
                str(self.bucket_responses_received),
                str(self.bucket_requests_sent - self.bucket_responses_received),
                f"{bucket_requests_per_sec:.2f}",
                f"{bucket_responses_per_sec:.2f}",
            )

            self.console.print(table)
            self.reset_bucket()


stats = RequestStats()


def background_logger(interval_sec=5):
    """Background thread to log stats every x seconds"""
    while True:
        time.sleep(interval_sec)
        stats.log_stats(bucket_duration=interval_sec)


@events.init.add_listener
def start_up(**kwargs):
    """Locust startup"""
    log.info("Starting Locust test with the following env vars:")

    log.info(f"NPT_LAST_LOG_RECEIVED_CONNECTOR_ID = {(NPT_LAST_LOG_RECEIVED_CONNECTOR_ID or 'NOT SET')}")
    log.info(f"NPT_VUI_INTERNAL_BRAIN_ID = {(NPT_VUI_INTERNAL_BRAIN_ID or 'NOT SET')}")

    threading.Thread(target=background_logger, daemon=True).start()


@events.test_start.add_listener
def on_test_start(**kwargs):
    stats.start()


@events.test_stop.add_listener
def on_test_stop(**kwargs):
    stats.stop()


class LogflowUser(FastHttpUser):
    """Class to implement logflow requests"""

    weight = 80
    wait_time = constant_pacing(1)

    @task(80)
    def update_last_log_received_for_non_existent_connector(self):
        """
        Update 'last_log_received' time of a connector that doesn't exist.
        Executed in 80% of Logflow requests.
        """
        connector_id = "00000000-000a-00a0-aa00-0a00aaaa00a0"
        headers = {"x-vectra-service": "logflow"}
        body = {"last_log_received": "2025-04-20T09:00:01.123Z"}
        stats.increment_requests_sent()
        with self.rest("PATCH", url=f"/v1/connectors/{connector_id}", headers=headers, json=body) as resp:
            if resp.status_code == 404:
                resp.success()
        stats.increment_responses_received()

    @task(20 if NPT_LAST_LOG_RECEIVED_CONNECTOR_ID else 0)
    def update_last_log_received(self):
        """
        Update 'last_log_received' time of a connector that exists.
        Executed in 20% of Logflow requests.
        """
        if not NPT_LAST_LOG_RECEIVED_CONNECTOR_ID:
            return

        headers = {"x-vectra-service": "logflow"}
        body = {"last_log_received": "2025-04-20T09:00:01.123Z"}
        stats.increment_requests_sent()
        self.client.patch(url=f"/v1/connectors/{NPT_LAST_LOG_RECEIVED_CONNECTOR_ID}", headers=headers, json=body)
        stats.increment_responses_received()

    @task(2 if NPT_LAST_LOG_RECEIVED_CONNECTOR_ID else 0)
    def setup_consent(self):
        """
        Send POST request to /v1/connectors/{connector_id}/setup/consent using CONSENT headers.
        """
        if not NPT_LAST_LOG_RECEIVED_CONNECTOR_ID:
            return

        connector_id = NPT_LAST_LOG_RECEIVED_CONNECTOR_ID

        headers = {"x-vectra-service": "consent"}
        body = {"properties": {"connector_type": "azure-cp", "ms_tenant_id": "dda75ba7-c192-4bf3-8139-2d4b1hqbc6cc"}}

        stats.increment_requests_sent()
        with self.rest("POST", url=f"/v1/connectors/{connector_id}/setup/consent", headers=headers, json=body) as resp:
            if resp.status_code == 404:
                resp.success()
        stats.increment_responses_received()


class VuiUser(FastHttpUser):
    """
    Class to implement vui requests.
    - 20% of requests made through this class
    """

    weight = 20
    wait_time = constant_pacing(1)

    @task
    def get_connectors(self):
        """
        Send GET request to /v1/connectors using VUI headers.
        """
        if not NPT_VUI_INTERNAL_BRAIN_ID:
            return

        headers = {"x-vectra-service": "vui", "x-internal-brain-id": NPT_VUI_INTERNAL_BRAIN_ID}
        stats.increment_requests_sent()
        self.client.get(url="/v1/connectors", headers=headers)
        stats.increment_responses_received()
