# Nexus Performace Testing
Nexus uses the [locust](https://docs.locust.io/en/stable/index.html) package for performance testing.

### What is locust
> Locust is an open source performance/load testing tool for HTTP and other protocols. Its developer-friendly approach lets you define your tests in regular Python code.

## How to run

Tests can be run from command line or using its web-based UI. Throughput, response times and errors can be viewed in real time and/or exported for later analysis.

Install the packages

```bash
pip install -r tests/nexus/performance/requirements.txt
```

Set environment variables(NPT = Nexus Performance Test)

```bash
export NPT_LAST_LOG_RECEIVED_CONNECTOR_ID=...
export NPT_VUI_INTERNAL_BRAIN_ID=...
```

- NPT_LAST_LOG_RECEIVED_CONNECTOR_ID: used for Logflow user to update last log received 
- NPT_VUI_INTERNAL_BRAIN_ID: used for VUI user

> [!NOTE]
>
> If these are not set, the performance test suite will still work, but will not execute all tasks.

It is possible to run the tests against your deployed version of nexus or your local instance

```bash
# Option 1: from repo root
locust -f tests/nexus/performance/locustfile.py --host=<host>

# Option 2
cd tests/nexus/performance
locust --host=<host>

# *host: url endpoint of your nexus app 
#        e.g. http://localhost:8000 or https://nexus-blynch-uw2.ingestion.dev.vectra-svc.ai
```

After running the above command you will be able to open the UI at http://localhost:8089/ and will look like

![image-20250428150330280](../../../docs/assets/image-20250428150330280.png) 

For the Nexus load tests, **Number of users** equates to number of request exectutions per second due to the usage of `wait_time = constant_pacing(1)` [link](https://docs.locust.io/en/stable/api.html#locust.wait_time.constant_pacing).

After inputting values and clicking start, you will be able to view the graphs page to see metrics.

![image-20250428152959003](../../../docs/assets/image-20250428152959003.png)

> Can be seen in the image above that the app it a limit of 70-75 RPS

There is also metrics logged to output in table format using the `RequestStats()` object.

![image-20250428173237953](../../../docs/assets/image-20250428173237953.png) 

This can be expanded in the future to include more custom metrics.



#### Additional tips

##### View Docker stats

Use [ctop](https://github.com/bcicen/ctop) to view a top-like interface for container metrics

```bash
brew install ctop
ctop -a
```

##### View logs via Rich

Add the `--skip-log-setup` flag to use rich for formatting the logs

```
locust ... --skip-log-setup
```

