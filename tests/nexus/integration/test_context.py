"""
Integration testing for request context.
"""

import logging

from fastapi.testclient import <PERSON><PERSON><PERSON>
from pytest import LogCaptureFixture
from vectra.consent_service_client.models import ConsentUrlResponse

from nexus.main import app
from nexus.repositories.connector import ConnectorRepository

client = TestClient(app)


def test_request_context_is_reset(mocker, connector_repository: ConnectorRepository, caplog: LogCaptureFixture):
    """
    Test that request context is reset between different requests.

    The test ensures that:
    - The `error_class` set in the context of the first request does not carry over to the second request.
    - The `request_id` is unique for each request to ensure proper isolation and tracking of requests.

    Args:
        connector_repository: mocks the dynamodb table with moto
        caplog: A pytest fixture that captures log messages during the test execution.
    """
    # Mock the external call to the Consent Service
    post_consent_url = mocker.patch("nexus.dependencies.consent.ConsentClient.post_consent_url")
    post_consent_url.return_value = ConsentUrlResponse(consent_url="mock-url")

    # Capture logs from vui_logger
    caplog.set_level(logging.INFO, logger="vectra.vui_logger.logger")

    headers = {"x-internal-brain-id": "test-brain-id"}

    # First request: Simulate a bad request to populate the context with an error
    bad_payload = {"connector_type": "fake", "name": "Test Connector"}
    response = client.post("/v1/connectors", json=bad_payload, headers=headers)
    assert response.status_code == 422

    # Get error log created by exception handler
    fail_log_record = caplog.records.pop()
    assert fail_log_record.levelname == "ERROR"
    assert fail_log_record.error_class == "RequestValidationError"

    # Second request: Simulate a valid request to ensure context reset
    request_payload = {"connector_type": "defender", "name": "Test Connector"}
    response = client.post("/v1/connectors", json=request_payload, headers=headers)
    assert response.status_code == 201

    # Get info log created by Consent request
    success_log_record = caplog.records.pop()
    assert success_log_record.levelname == "INFO"
    assert success_log_record.error_class is None

    # Ensure that the request_id is different between the two requests
    assert fail_log_record.request_id != success_log_record.request_id
