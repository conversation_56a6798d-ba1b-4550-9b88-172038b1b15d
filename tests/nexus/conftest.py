import asyncio
import os

import boto3
import pytest
import pytest_asyncio
from moto import mock_aws

from nexus.repositories.connector import ConnectorRepository
from nexus.utils.config import config

from tests.nexus.utils.ddb_setup import setup_table


def pytest_configure():
    """Configures test environment."""
    config.DDB_ENDPOINT_URL = None
    del os.environ["AWS_ENDPOINT"], os.environ["STS_ENDPOINT"]
    mock_aws().start()


@pytest_asyncio.fixture(scope="function")
async def dynamodb_async_client():
    """Asynchronous DynamoDB client for tests that need direct access"""
    mock_aws().reset()
    client = boto3.client("dynamodb", region_name=config.REGION)

    class AsyncDynamoDBClient:
        async def create_table(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.create_table(**kwargs))

        async def get_item(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.get_item(**kwargs))

        async def put_item(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.put_item(**kwargs))

        async def update_item(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.update_item(**kwargs))

        async def query(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.query(**kwargs))

        async def scan(self, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: client.scan(**kwargs))

        def get_waiter(self, waiter_name):
            waiter = client.get_waiter(waiter_name)

            class AsyncWaiter:
                async def wait(self, **kwargs):
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(None, lambda: waiter.wait(**kwargs))

            return AsyncWaiter()

    yield AsyncDynamoDBClient()


@pytest_asyncio.fixture(scope="function")
async def create_table(dynamodb_async_client):
    """
    Fixture to create the Nexus DynamoDB table
    """
    table = await setup_table(dynamodb_async_client)
    waiter = dynamodb_async_client.get_waiter("table_exists")
    await waiter.wait(TableName=config.NEXUS_TABLE_NAME)
    yield table


@pytest_asyncio.fixture(scope="function")
async def connector_repository(create_table) -> ConnectorRepository:
    """
    Fixture to create a ConnectorRepository instance
    """
    return ConnectorRepository(create_table["TableDescription"]["TableName"])


@pytest.fixture(scope="function")
def connector_defaults():
    """Default connector attributes for testing"""
    from nexus.models import ConnectorSize

    return {
        "pk": "mock_id",
        "sk": "connector",
        "name": "mock_name",
        "internal_brain_id": "mock_brain_id",
        "connector_type": "azure-cp",
        "connector_state": "setup",
        "operating_state": "inactive",
        "created_at": "2024-11-11T10:10:10Z",
        "updated_at": "2024-11-11T10:10:10Z",
        "first_log_received": None,
        "last_log_received": None,
        "size": ConnectorSize.MEDIUM,
        "last_sequence_id": None,
        "data": "test#setup",
        "setup_records": {},
        "error": None,
        "properties": {},
    }


@pytest_asyncio.fixture(scope="function")
async def create_persisted_connector(connector_repository, connector_defaults):
    """Factory for creating and persisting connector objects"""
    from nexus.models import Connector

    async def _create_connector(**kwargs):
        # Merge defaults with provided overrides and set data field
        data = {**connector_defaults, **kwargs}
        data["data"] = f"{data['connector_type']}#{data['connector_state']}"

        # Persist and return the connector
        await connector_repository.put_item(data)
        return Connector(**data)

    return _create_connector
