# Nexus

## Infrastructure

![architecture](docs/assets/architecture.svg) 

Nexus is a multi-tenant service deployed within LC-39. 
It is responsible for the lifecycle management of data source connectors.

- The application is a **REST API** built with **FastAPI**
- It has a single **DynamoDB** table as a **database**.
- It has an **SNS Topic** to publish **events**, currently used to notify Logflow when a Connector transitions into 'CONNECTING' state.
- The infrastructure is deployed inside the **saas-ingestion** AWS accounts.
- The application is deployed as a docker image inside an EKS Pod in the **ingestionv1** cluster.

## Built With

- Python 3.11
- [FastAPI](https://fastapi.tiangolo.com/)
- [Pydantic](https://docs.pydantic.dev/latest/)
- [Boto3](https://boto3.amazonaws.com/v1/documentation/api/latest/index.html)
- [Terraform](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)

## History

Nexus was built to replace Vectra's existing connector management service [Sensible](https://confluence.vectra.io/pages/viewpage.action?pageId=********).

Sensible was built when Vectra moved from Azure to AWS and replaced Vectra's original connector management service, [Global API](https://confluence.vectra.io/display/VEC/API). 

The reason to create Nexus was to have a single source of truth for connector data. For more see [Sensible - reason why we need Nexus](https://confluence.vectra.io/display/PC/Sensible+-+reason+why+we+need+Nexus).

For info on the initial planning and design please see the [Nexus Initial Design](https://confluence.vectra.io/display/PC/Nexus+Initial+Design) docs.

## What does Nexus do?

It manages the state lifecycle of a [Connector](https://confluence.vectra.io/display/GLOSS/term/172234858), which is an entity used to read/ingest data from customers.

The general lifecycle is

1. A customer **creates** a new connector.
2. The customer must complete a **setup** phase to grant permissions to Vectra to ingest data.
3. Once setup phase is complete, the connector is **connected** and Vectra will start ingesting logs until the connector it is **deleted**.

### Connector State Lifecycle

![state-diagram](docs/assets/state-diagram.svg) 

1. When a connector is **created**, it is initially in the **setup** state and **inactive**.
2. Once setup is complete it transitions to **connecting**.
3. Logflow will be notified and will test the connection. 
4. If successful, Logflow will make a request to Nexus to transition the connector to **connected**.
   Once connected, its operational state will transition to **active**, and will remain active until it is deleted.

For a more detailed info on the state lifecycle and how connectors move through different states, please see the [Connector State Lifecycle](https://confluence.vectra.io/display/PC/Nexus%3A+Connector+State+Lifecycle) doc.

## Inbound/Outbound Requests

### Inbound Requests

![inbound-requests](docs/assets/inbound-requests.svg) 

#### Logflow

- `POST /v1/connectors/:id/state` to update connector state
- WIP
  - Updates connector `last_log_received` timestamp

#### Consent

- `POST /v1/connectors/:id/setup/consent` to complete consent setup

#### VUI Provisioning

- `DELETE /v1/connectors/:id` when tenant is deprovisioned

#### VUI Appliance/Cloud

- `POST /v1/connectors` to create a connector
- `PATCH /v1/connector/:is` to update connector name
- `GET /v1/connector` to list connectors
- `DELETE /v1/connectors/:id` to delete connector

#### AWS Ingestion

- `GET /v1/connectors/:id/state` to get connector info
- `POST /v1/connectors/:id/state` to update AWS connector state

#### DataAPI / Jetstream

- TBC

#### Updater

- TBC

### Outbound Requests

![outbound-requests](docs/assets/outbound-requests.svg) 

#### CIDM

- TBC

#### Consent

- `POST /consent/url` to create public url for consenting to Microsoft connectors (O365, Azure, Defender)
- `POST /consent/secrets` to create secret record for EDR connectors
- `PATCH /consent/secrets/:id` to update secret record values
- `DELETE /consent/secrets/:id/:secret_name` to remove a secret after a connector has been deleted

#### SNS Topic/Logflow

Nexus has a **notification module** where **events** are sent to an internal **async queue**.
These events are published to a SNS Topic.
There is a subscription between this SNS Topic and Logflow's SQS Queue

- When a connector transitions to connecting, an event is published to the Async Queue

> ##### AWS SNS
>
> Nexus sends events to AWS SNS via the `src/nexus/dependencies/sns.py` module.
>
> ###### Event JSON
>
> ```json
> {
>     "connector_id": "string",
>     "connector_type": "string",
>     "connector_state": "string",
>     "internal_brain_id": "string"
> }
> ```
>
> ###### Example SNS Message Body
>
> ```json
> {
>   "Message": "{\\"created_at\\":\\"2024-07-20 10:45:37\\",\\"connector_id\\":\\"76d6d891-7e5d-47c8-a078-666044c0bbdb\\",\\"connector_state\\":\\"connecting\\",\\"connector_type\\":\\"crowdstrike\\",\\"internal_brain_id\\":\\"vui-1\\"}", 
>   "MessageAttributes": {
>   	"ConnectorId": {"Type": "String", "Value": "76d6d891-7e5d-47c8-a078-666044c0bbdb"}, 
> 		"ConnectorType": {"Type": "String", "Value": "crowdstrike"}, 
> 		"ConnectorState": {"Type": "String", "Value": "connecting"}, 
> 		"InternalBrainId": {"Type": "String", "Value": "vui-1"}
> 	}
> }
> ```

## API Documentation

Please see the following for API Docs and how to integrate with Nexus: [Nexus: API Docs](https://confluence.vectra.io/display/PC/Nexus%3A+API+Docs)

## Database Design

Please see the following for DynamoDB table: [Nexus](https://confluence.vectra.io/display/PC/Nexus)(View Data Model Section)

## Development

Please see the following doc for working on Nexus: [Nexus Development](./docs/development.md)

## Deployment

Please see the following doc for deploying nexus to dev and prod: [Nexus Deployment](./docs/deployment.md)

## Testing Tools

### Connection Test Script

The repository includes a script for testing Network Load Balancer (NLB) connection timeouts. This is useful for identifying if and when the NLB disconnects idle connections, which can help diagnose connection-related issues in production environments.

#### Purpose

- Tests for NLB connection timeouts by making repeated API requests with deliberate long pauses
- Runs multiple concurrent threads to simulate real-world connection behavior
- Logs connection-related activities and errors
- Specifically identifies RemoteDisconnected errors that may indicate NLB timeouts

#### Usage

```bash
python src/vectra/tests/connection_test_script.py [sleep_seconds]
```

Where `sleep_seconds` is an optional parameter specifying the time in seconds to sleep between requests (default: 360 seconds / 6 minutes).

#### Examples

```bash
# Test with default sleep interval (360 seconds)
python src/vectra/tests/connection_test_script.py

# Test with 5-minute sleep intervals
python src/vectra/tests/connection_test_script.py 300

# Test with 2-minute sleep intervals
python src/vectra/tests/connection_test_script.py 120
```

#### Configuration

Before running the script, you may want to modify the following variables in the script:

- `NEXUS_URL`: The URL of the Nexus API (default: "http://localhost:8000")
- `CONNECTOR_ID`: The ID of the connector to use for testing

## Links & Docs

### Documentation

- [Confluence Design Document](https://confluence.vectra.io/display/PC/Nexus+-+Data+Source+Management+-+Design+Document)
- [Setup Process Document](https://confluence.vectra.io/display/~vukhatyuk/Nexus+-+Setup+Process)

### ArgoCD

- [dev](https://argo-cd.root.vectra-svc.ai/applications?showFavorites=false&proj=ingestion-dev&namespace=nexus)
- [prod](https://argo-cd.root.vectra-svc.ai/applications?showFavorites=false&proj=ingestion-prod&namespace=nexus)

### Dashboards

- [dev](https://grafana.dev.vectra-svc.ai/d/ddx00e0pes9a8f/nexus?orgId=1)
- [prod](https://grafana.prod.vectra-svc.ai/d/ddx00e0pes9a8f/nexus?orgId=1)

