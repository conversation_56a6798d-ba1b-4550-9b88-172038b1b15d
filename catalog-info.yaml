---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  annotations:
    backstage.io/techdocs-ref: url:https://github.com/VectraAI-Engineering/nexus/raw/README.md
  description: Data Source Management
  name: nexus
spec:
  lifecycle: development
  owner: sdp-atlas
  type: service
---
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  annotations:
    backstage.io/techdocs-ref: url:https://github.com/VectraAI-Engineering/nexus/raw/terraform/modules/nexus/README.md
  description: Terraform Module
  name: nexus
spec:
  owner: sdp-atlas
  partOf: [component:default/nexus]
  type: module
---
apiVersion: backstage.io/v1alpha1
kind: Resource
metadata:
  description: Docker Artifact
  name: nexus
spec:
  owner: sdp-atlas
  partOf: [component:default/nexus]
  type: image
