# setup.py created by build-tools Python.wheel_validate.py
[project]
name = "vectra-nexus"
dynamic = [
    "version",
    "dependencies",
    "optional-dependencies"
]
description = "Data Source Management "
readme = "README.md"
requires-python = ">=3.8"
license = {text = "Proprietary"}
authors = [{name = "Vectra AI"}]
classifiers = ["Private :: Do Not Upload"]

[tool.setuptools.dynamic]
version = {attr = "version.VERSION"}

[tool.setuptools.dynamic.optional-dependencies]
server = {file = "requirements.txt"}
client = {file = "src/vectra/nexus_client/requirements.txt"}
dev = {file = "requirements-dev.txt"}

[tool.setuptools]
include-package-data = true

# Scanning for namespace packages in the ``src`` directory is true by
# default in pyproject.toml, so you do NOT need to include the
# `tool.setuptools.packages.find` if it looks like the following:
# [tool.setuptools.packages.find]
# namespaces = true
# where = ["src"]
# https://setuptools.pypa.io/en/latest/userguide/datafiles.html#subdirectory-for-data-files

[tool.pylint.'MESSAGES CONTROL']
disable = []

[tool.pylint.'MASTER']
init-hook = 'import sys; sys.path.append("/data/src")'
ignore = [
    "tests",
    ".Makefiles",
    "setup.py",
]
ignore-paths = [
    "^src/vectra/.*_client$",  # ignore openapi clients
]
fail-under = 10
recursive = true

[tool.pylint.'FORMAT']
max-line-length = 120

# black
[tool.black]
line-length = 120
extend-exclude = '(.*_client|.*tests\/.*_client)'  # ignore openapi clients

# isort
[tool.isort]
profile = "black"
line_length = 120
known_first_party = ["src"]
known_local_folder = ["tests"]

# skip_glob is used by the make target and not available to use here
extend_skip_glob = [
    "**/src/vectra/*_client/**", # ignore openapi clients
    "**/tests/*_client/**", # ignore openapi clients
]

# Mypy
[tool.mypy]
python_version = "3.12"
exclude = [
    ".Makefiles",                # ignore build-tools
    "^src/vectra/.*_client",           # ignore openapi clients
    "^tests/.*_client",         # ignore openapi clients
]
show_error_context = true
show_error_codes = true
ignore_missing_imports = true
# disallow_untyped_defs = true
install_types = true
non_interactive = true
html_report=".Makefiles/tmp/reports/lint"
cache_dir = ".Makefiles/tmp/mypy_cache/"

# Pytest add Coverage
[tool.pytest.ini_options]
addopts = [
    "--cov=.",
    "--cov-config=pyproject.toml",
    "--cov-report=html:.Makefiles/tmp/reports/unit-tests/html-cov",
    "--cov-report=xml:.Makefiles/tmp/reports/unit-tests/coverage.xml",
    "--cov-report=term"
]
env_files = ".env.test"

# Coverage
[tool.coverage.report]
omit = [
    "tests/*",
    "src/vectra/*_client/*",  # ignore openapi clients
    "version.py"
]
fail_under = 90
skip_empty = true

# Uncomment this section to auto-publish docs on release
# [tool.sphinx-pyproject]
# docs_repo = "dp/jetstream-docs"
# docs_root_dir_local = "docs"
# docs_root_dir_repo = "jetstream"
