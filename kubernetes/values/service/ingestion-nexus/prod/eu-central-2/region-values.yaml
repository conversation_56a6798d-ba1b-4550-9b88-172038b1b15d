# See docs on helm chart values:
# https://sourcecode.vectra.io/projects/SP/repos/kube-helm-charts/browse/kubernetes/charts/generic-service/README.md
image:
  registry: ************.dkr.ecr.eu-central-2.amazonaws.com
  tag: 202506.3.0
env:
  AWS_REGION: eu-central-2
  AWS_DEFAULT_REGION: eu-central-2
  CONSENT_INTERNAL_URL: https://sp-consent-service-eucl2.app.prod.vectra-svc.io
  CONSENT_INTERNAL_DELETE_ROLE_ARN: arn:aws:iam::************:role/sp-consent-service-delete-eucl2
  NEXUS_SNS_TOPIC_ARN: arn:aws:sns:eu-central-2:************:main-nexus-sns-topic
  METRICS_ENDPOINT: metrics-eu-central-2.prod.vectra-svc.ai:8125
ingress:
  enabled: true
  protocol: http
  port: 8000
  hostnamePublic: [nexus-ec2.ingestion.prod.vectra-svc.ai]
  hostnamePrivate: [nexus-ec2.ingestion.prod.vectra-svc.io]
ServiceAccount:
  role: arn:aws:iam::************:role/webidentity/nexus-app-eu-central-2-main
  enabled: true
