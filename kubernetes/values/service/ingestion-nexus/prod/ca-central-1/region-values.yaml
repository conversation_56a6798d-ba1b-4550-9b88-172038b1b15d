# See docs on helm chart values:
# https://sourcecode.vectra.io/projects/SP/repos/kube-helm-charts/browse/kubernetes/charts/generic-service/README.md
image:
  registry: ************.dkr.ecr.ca-central-1.amazonaws.com
  tag: 202506.3.0
env:
  AWS_REGION: ca-central-1
  AWS_DEFAULT_REGION: ca-central-1
  CONSENT_INTERNAL_URL: https://sp-consent-service-cacl1.app.prod.vectra-svc.io
  CONSENT_INTERNAL_DELETE_ROLE_ARN: arn:aws:iam::************:role/sp-consent-service-delete-cacl1
  NEXUS_SNS_TOPIC_ARN: arn:aws:sns:ca-central-1:************:main-nexus-sns-topic
  METRICS_ENDPOINT: metrics-ca-central-1.prod.vectra-svc.ai:8125
ingress:
  enabled: true
  protocol: http
  port: 8000
  hostnamePublic: [nexus-cc1.ingestion.prod.vectra-svc.ai]
  hostnamePrivate: [nexus-cc1.ingestion.prod.vectra-svc.io]
ServiceAccount:
  role: arn:aws:iam::************:role/webidentity/nexus-app-ca-central-1-main
  enabled: true
