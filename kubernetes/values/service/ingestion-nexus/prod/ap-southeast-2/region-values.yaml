# See docs on helm chart values:
# https://sourcecode.vectra.io/projects/SP/repos/kube-helm-charts/browse/kubernetes/charts/generic-service/README.md
image:
  registry: ************.dkr.ecr.ap-southeast-2.amazonaws.com
  tag: 202506.3.0
env:
  AWS_REGION: ap-southeast-2
  AWS_DEFAULT_REGION: ap-southeast-2
  CONSENT_INTERNAL_URL: https://sp-consent-service-apse2.app.prod.vectra-svc.io
  CONSENT_INTERNAL_DELETE_ROLE_ARN: arn:aws:iam::************:role/sp-consent-service-delete-apse2
  NEXUS_SNS_TOPIC_ARN: arn:aws:sns:ap-southeast-2:************:main-nexus-sns-topic
  METRICS_ENDPOINT: metrics-ap-southeast-2.prod.vectra-svc.ai:8125
ingress:
  enabled: true
  protocol: http
  port: 8000
  hostnamePublic: [nexus-as2.ingestion.prod.vectra-svc.ai]
  hostnamePrivate: [nexus-as2.ingestion.prod.vectra-svc.io]
ServiceAccount:
  role: arn:aws:iam::************:role/webidentity/nexus-app-ap-southeast-2-main
  enabled: true
