# See docs on helm chart values:
# https://sourcecode.vectra.io/projects/SP/repos/kube-helm-charts/browse/kubernetes/charts/generic-service/README.md
image:
  registry: ************.dkr.ecr.eu-west-1.amazonaws.com
  tag: 202506.3.0
env:
  AWS_REGION: eu-west-1
  AWS_DEFAULT_REGION: eu-west-1
  CONSENT_INTERNAL_URL: https://sp-consent-service-euwt1.app.prod.vectra-svc.io
  CONSENT_INTERNAL_DELETE_ROLE_ARN: arn:aws:iam::************:role/sp-consent-service-delete-euwt1
  NEXUS_SNS_TOPIC_ARN: arn:aws:sns:eu-west-1:************:main-nexus-sns-topic
  METRICS_ENDPOINT: metrics-eu-west-1.prod.vectra-svc.ai:8125
ingress:
  enabled: true
  protocol: http
  port: 8000
  hostnamePublic: [nexus-ew1.ingestion.prod.vectra-svc.ai]
  hostnamePrivate: [nexus-ew1.ingestion.prod.vectra-svc.io]
ServiceAccount:
  role: arn:aws:iam::************:role/webidentity/nexus-app-eu-west-1-main
  enabled: true
resources:
  requests:
    cpu: 1000m
    memory: 256Mi
  limits:
    cpu: 1000m
    memory: 512Mi
