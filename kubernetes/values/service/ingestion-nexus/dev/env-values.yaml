# See docs on helm chart values:
# https://sourcecode.vectra.io/projects/SP/repos/kube-helm-charts/browse/kubernetes/charts/generic-service/README.md
image:
  repository: cd/nexus
  tag: 202506.3.0
env:
  NEXUS_TABLE_NAME: main-nexus
  PAGINATION_LIMIT: 500
  METRICS_DATABASE_NAME: main-nexus
replicas: 5
autoscaling:
  enabled: false
resources:
  requests:
    cpu: 500m
    memory: 256Mi
  limits:
    cpu: 800m
    memory: 512Mi
