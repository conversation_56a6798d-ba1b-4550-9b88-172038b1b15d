---
# Helm chart related configuration
chart:
  name: generic-service
  type: service
  local: false
# The pinned version tags for the specified helm chart in each environment.
  version:
    dev: 202501.0.0
    prGen: 202501.0.0
    prod: 202501.0.0
argo:
  project: ingestion
# Flags to enable/disable auto sync in the application set for a specific env.
# selfHeal can only be enabled if auto=true for the same env.
  sync:
    dev:
      auto: true
      selfHeal: true
    prGen:
      auto: true
      selfHeal: false
    prod:
      auto: true
      selfHeal: true
# Target cluster related configuration
cluster:
  id: ingestionv1
values:
# The pinned version tags for values configuration in each environment.
# Dev and root envs can be set to any branch/tag while stage
# and prod are required to have a tag that was created by the Trigger Release.
# When creating a stage or prod environment a placeholder value will be added as the version.
# It is required to release this repository and bump the values
# version in the Trigger Release for the app/s to be created.
  version:
    dev: main
    prod: main
# The Pull Request Generator (prGen) automates the creation of isolated Kubernetes
# envs for each feature branch that matches the below regex.
# Values files for this Environment are found at values/<app>/prgen/*
prGen:
  branchMatch: .*-prgen
# When configured ArgoCD can reach out to the Argo Service Discovery API,
# retrieve the value and then inject that value as a value which can be consumed in a helm chart.
# Docs: https://confluence.vectra.io/display/DUBOPS/Argo+Service+Discovery
serviceDiscovery: {}
