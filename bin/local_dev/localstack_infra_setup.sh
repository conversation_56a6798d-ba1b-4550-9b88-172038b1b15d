#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create infra on localstack instance for local development
- SNS Topic (local-nexus-sns-topic), for nexus to publish event to
- SQS Queue (local-logflow-queue), to imitate logflow queue
- SNS Subscription between SNS and SQS
"""

import os

import boto3
from botocore.exceptions import ClientError

os.environ["AWS_ACCESS_KEY_ID"] = "accesskey"
os.environ["AWS_SECRET_ACCESS_KEY"] = "secretkey"
os.environ["AWS_DEFAULT_REGION"] = "us-west-2"


def get_sns_topic_arn(client, topic_name):
    response = client.list_topics()
    for topic in response["Topics"]:
        if topic_name in topic["TopicArn"]:
            return topic["TopicArn"]
    return None


def create_sns_topic(client, topic_name):
    topic_arn = get_sns_topic_arn(client, topic_name)
    if topic_arn:
        print(f"  SNS Topic '{topic_name}' already exists: {topic_arn}")
        return topic_arn
    response = client.create_topic(Name=topic_name)
    return response["TopicArn"]


def get_sqs_queue_url(client, queue_name):
    try:
        response = client.get_queue_url(QueueName=queue_name)
        return response["QueueUrl"]
    except ClientError as e:
        if e.response["Error"]["Code"] == "AWS.SimpleQueueService.NonExistentQueue":
            return None
        raise


def create_sqs_queue(client, queue_name):
    queue_url = get_sqs_queue_url(client, queue_name)
    if queue_url:
        print(f"  SQS Queue '{queue_name}' already exists: {queue_url}")
        return queue_url
    response = client.create_queue(QueueName=queue_name)
    return response["QueueUrl"]


def get_sqs_queue_attributes(client, queue_url):
    response = client.get_queue_attributes(QueueUrl=queue_url, AttributeNames=["All"])
    return response["Attributes"]


def subscribe_sns_to_sqs(client, topic_arn, queue_arn):
    response = client.list_subscriptions_by_topic(TopicArn=topic_arn)
    for subscription in response["Subscriptions"]:
        if subscription["Endpoint"] == queue_arn:
            print(f"  Subscription already exists: {subscription['SubscriptionArn']}")
            return subscription["SubscriptionArn"]
    response = client.subscribe(TopicArn=topic_arn, Protocol="sqs", Endpoint=queue_arn)
    return response["SubscriptionArn"]


def main():
    endpoint_url = "http://localhost:4566"

    print("Setting up infra on Localstack")
    sns_client = boto3.client("sns", endpoint_url=endpoint_url)
    sqs_client = boto3.client("sqs", endpoint_url=endpoint_url)

    print("Creating SNS Topic")
    topic_name = "local-nexus-sns-topic"
    topic_arn = create_sns_topic(sns_client, topic_name)
    print(f"  SNS Topic ARN: {topic_arn}")

    print("Creating SQS Topic")
    queue_name = "local-logflow-queue"
    queue_url = create_sqs_queue(sqs_client, queue_name)
    print(f"  SQS Queue URL: {queue_url}")

    print("Creating subscription between SNS and SQS")
    queue_attributes = get_sqs_queue_attributes(sqs_client, queue_url)
    queue_arn = queue_attributes["QueueArn"]
    subscription_arn = subscribe_sns_to_sqs(sns_client, topic_arn, queue_arn)
    print(f"  SNS Subscription ARN: {subscription_arn}")


if __name__ == "__main__":
    main()