"""
<PERSON>rip<PERSON> to mock the logflow process of validating connector.
Pulls messages from mock logflow SQS queue.
For each message, makes a request to Nexus to update the state to 'connected'
"""

import json
import os

import boto3
import requests

os.environ["AWS_ACCESS_KEY_ID"] = "accesskey"
os.environ["AWS_SECRET_ACCESS_KEY"] = "secretkey"
os.environ["AWS_DEFAULT_REGION"] = "us-west-2"


def process_message(message):
    """Make request to Nexus to set connector state to 'connected'"""
    connector_id = json.loads(json.loads(message["Body"]).get("Message")).get("connector_id")
    if connector_id:
        url = f"http://0.0.0.0:8000/v1/connectors/{connector_id}/state"
        headers = {"x-vectra-service": "logflow"}
        body = {"connector_state": "connected"}
        response = requests.post(url, headers=headers, json=body)
    return response


def delete_message(client, queue_url, receipt_handle):
    client.delete_message(QueueUrl=queue_url, ReceiptHandle=receipt_handle)


def main():
    print("Querying for messages from logflow queue...")

    endpoint_url = "http://localhost:4566"
    queue_name = "local-logflow-queue"

    sqs_client = boto3.client("sqs", endpoint_url=endpoint_url)
    queue_url = sqs_client.get_queue_url(QueueName=queue_name)["QueueUrl"]
    print(f"  Queue url found: {queue_url}")

    messages = sqs_client.receive_message(QueueUrl=queue_url, MaxNumberOfMessages=10).get("Messages", [])
    print(f"Received {len(messages)} messages from the queue")

    if messages == []:
        print("Exiting, no messages received from queue")
    else:
        for message in messages:
            response = process_message(message)
            if response.status_code == 200:
                delete_message(sqs_client, queue_url, message["ReceiptHandle"])
                print(f"Successfully processed message: \n\t{message}")
            else:
                print(f"Failed to process message: \n\t{message}")


if __name__ == "__main__":
    main()
