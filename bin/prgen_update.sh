#!/bin/bash
echo "################## Updating PR Gen image tag ##################"

repository="059340521413.dkr.ecr.us-west-2.amazonaws.com/preview/nexus"
yaml_file="kubernetes/values/service/ingestion-nexus/prgen/us-west-2/region-values.yaml"

# get tag of latest preview image 
tag=$(docker images --format "{{.Repository}} {{.Tag}} {{.CreatedAt}}" | grep "^$repository" | sort -r | head -n 1 | awk '{print $2}')
trimmed_tag="${tag%-larm64}"
dev_name=$(git config user.email | sed -E "s/@vectra.ai//")

# update helm chart
sed -i '' -E "s/nexus-[a-z]*-uw2/nexus-$dev_name-uw2/g" "$yaml_file"
sed -i '' -E "s/tag:.*/tag: $trimmed_tag/g" "$yaml_file"
echo "Updated tag in $yaml_file to: $trimmed_tag"

if [[ "$1" == "auto_commit" ]]; then
    # Get ticket number
    branch_name=$(git rev-parse --abbrev-ref HEAD)
    branch_prefix=$(echo $branch_name | sed -E "s/(^(RKT|HYPERION)-[0-9]+).*/\1/")

    if [[ ! -z "$branch_prefix" ]]; then
        echo "Running the following commands: "
        echo "  git add ."
        echo "  git commit -m '${branch_prefix}: updating prgen'"
        echo "  git push origin $branch_name"
        git add .
        git commit -m "${branch_prefix}: updating prgen"
        git push origin $branch_name
    else
        echo "Branch prefix not found, skipping git operations."
    fi
fi