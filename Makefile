# Portions of this file are auto-maintained. Place all your edits after the "-include" line below!
# See https://confluence.vectra.io/display/DUBOPS/Unified+CI+Pipeline+onboarding
# This file came from build-tools/project_setup/Makefile

# In order to use `make setup` you will need to configure a github PAT and export it to GH_TOKEN. 
# `make configure_github` can be run to do this.

SHELL := /bin/bash

# Either sourcecode or github
BUILD_TOOLS_MAKEFILES_PATH  ?= $(shell pwd)/.Makefiles

.PHONY: get_archive
get_archive:
	@echo "build-tools path is $(BUILD_TOOLS_MAKEFILES_PATH)"
	@mkdir -p $(BUILD_TOOLS_MAKEFILES_PATH)
	@if [[ -z "$(GH_TOKEN)" ]]; then \
		echo "No github PAT set in GH_TOKEN env var. Please run make configure_github to set this up."; exit 1; \
	fi;
	@if ! command -v jq &> /dev/null; then \
		echo "jq not istalled, please install it to set up build-tools: https://jqlang.github.io/jq"; exit 1; \
	fi;
	@if [[ -z "$(BUILD_TOOLS_VERSION)" ]]; then \
		release=$$(curl -sfL -H "Authorization: Bearer ${GH_TOKEN}" https://api.github.com/repos/VectraAI-Engineering/build-tools/releases/latest); \
		echo "Getting latest release of build-tools: $$(echo $${release} | jq -r ".tag_name")"; \
	else \
		release=$$(curl -sfL -H "Authorization: Bearer ${GH_TOKEN}" https://api.github.com/repos/VectraAI-Engineering/build-tools/releases/tags/$(BUILD_TOOLS_VERSION)); \
		if [[ -z "$${release}" ]]; then echo "Couldn't get build-tools release for $(BUILD_TOOLS_VERSION)"; exit 1; fi; \
		echo "Getting specific release of build-tools: $$(echo $${release} | jq -r ".tag_name")"; \
	fi; \
	curl -sfL -H "Accept: application/octet-stream" \
	-H "Authorization: Bearer ${GH_TOKEN}" \
	https://api.github.com/repos/VectraAI-Engineering/build-tools/releases/assets/$$(echo $${release} | jq -r ".assets[] | select(.name == \"build-tools.tar\") | .id") | \
	tar -xf - -C $(BUILD_TOOLS_MAKEFILES_PATH); \
	if [ $${PIPESTATUS[0]} -ne 0 ]; then printf "\nDownloading the build-tools release failed, check the error above and make sure your GH_TOKEN works.\n" >&2; exit 1;fi; \
	printf "$$(echo $${release} | jq -r ".tag_name")" > $(BUILD_TOOLS_MAKEFILES_PATH)/.version;

.PHONY: configure_github
configure_github: ## creates, configures and adds a GitHub PAT to your keychain
	@echo "This target will configure your machine with a GitHub personal access token (PAT)."
	@echo "We use PATs to clone repos over https as well as set up build-tools, but they need to be created in the GitHub web console and then added to your operating system's keychain (such as osxkeychain for MacOS)."
	@echo -e "Press \033[1mENTER\033[0m to open a web browser to login to Vectra's GitHub enterprise with your SSO credentials...";
	@read
	@python3 -m webbrowser https://launcher.myapps.microsoft.com/api/signin/42bce6d5-68da-4ced-9c8b-1a3be5a03a70?tenantId=a6cc66bc-f419-45c2-a9c2-8ff4ab685f2d; echo "https://launcher.myapps.microsoft.com/api/signin/42bce6d5-68da-4ced-9c8b-1a3be5a03a70?tenantId=a6cc66bc-f419-45c2-a9c2-8ff4ab685f2d";
	@echo
	@echo -e "Now, press \033[1mENTER\033[0m to open a web browser where you can create a GitHub PAT..."
	@read
	@python3 -m webbrowser "https://github.com/settings/tokens/new?description=created+by+build-tools+$$(date -u '+%b+%Y')&scopes=repo,workflow&default_expires_at=90"; echo "https://github.com/settings/tokens/new?description=created+by+build-tools+$$(date -u '+%b+%Y')&scopes=repo,workflow&default_expires_at=90";
	@echo
	@echo "Ensure you set at least the following permission scope:"
	@echo "    ✔️ Repo   Full control of private repositories"
	@echo -e "    Set \033[1mExpiration\033[0m to 90 days"
	@echo -e "Then click the \033[0;32mGenerate Token\033[0m button at the bottom of the page"
	@echo
	@if [[ -z "$$(git config --global credential.helper)" ]]; then \
		echo -e "\033[0;33mNo git credential helper set, using \"store\", which saves your PAT in plaintext in $${HOME}/.git-credential\033[0m"; \
		git config --global credential.helper "store --file $${HOME}/.git-credentials"; \
	fi;
	@stty -echo; printf "Next, enter the GitHub personal access token: ";read token;stty echo;echo; \
	username=$$(git config user.email | cut -d "@" -f 1); \
	if [[ -z "$${username}" ]]; then \
		printf "Now, enter your GitHub username (should end in _vectra): "; read username; echo; \
	else \
		echo "Got username from configured git email: $${username}_vectra"; \
	fi; \
	username=$$(echo "$${username/_vectra/}_vectra"); \
	echo -e "protocol=https\nhost=github.com\npassword=$${token}\nusername=$${username}\n\n" | git credential approve; \
	case $${SHELL} in */zsh) rc_path="$${HOME}/.zshrc" ;; *) rc_path="$${HOME}/.bashrc" ;; esac; \
	echo "Adding token to GH_TOKEN env var in your shell's rc file: $${rc_path}"; \
	echo -e "\nexport GH_TOKEN=\"$${token}\"" >> $${rc_path}; \
	echo; \
	echo "Finally, we need to authorize the token for use with our GitHub org"; \
	echo -e "Press \033[1mENTER\033[0m to open a new browser window to authorize the PAT for our GitHub org..."; \
	read; \
	python3 -m webbrowser https://github.com/settings/tokens; echo "https://github.com/settings/tokens"; \
	echo; \
	echo "Click \"Configure SSO\" followed by \"VectraAI-Engineering\", then click \"Continue\" on the next two pages."; \
	echo; \
	echo -e "When the above is done press \033[1mENTER\033[0m to test the newly set token by checking if we can access the build-tools repo..."; \
	read; \
	curl -sf -H "Authorization: Bearer $${token}" \
	https://api.github.com/repos/VectraAI-Engineering/build-tools/releases/latest &> /dev/null || \
	{ echo -e "\033[0;31mTest failed - something is wrong with your token, are you sure you gave it the correct permissions and configured SSO for it?\033[0m"; exit 1; }; \
	echo -e "\033[0;32mTest succeeded!\033[0m"; \
	echo; \
	echo "Git credentials should now be added to your configured keychain and shell rc file!"; \
	echo -e "\033[1mNote: to use the GH_TOKEN env var in your current shell, run the following command to reload it:\033[0m"; \
	echo "source $${rc_path}";

.PHONY: reset
reset:
	@rm -rf $(BUILD_TOOLS_MAKEFILES_PATH)
	@$(MAKE) setup

.PHONY: reset_local
reset_local:
	@rm -rf $(BUILD_TOOLS_MAKEFILES_PATH)
	@$(MAKE) setup_local

.PHONY: setup
setup: get_archive setup_custom
	@$(MAKE) plugins_install
	@$(MAKE) -f Makefile repo_setup_trigger
	@echo "build-tools setup complete"

.PHONY: setup_local
setup_local: get_archive setup_custom
	@$(MAKE) plugins_install
	@$(MAKE) -f Makefile repo_setup_trigger
	@echo "build-tools local setup complete"

# This comment at the end of the below line is needed to maintain backwards compatability with the old KEEP_KEY for make update
-include $(BUILD_TOOLS_MAKEFILES_PATH)/main.mk # Makefiles/main.mk


# EDIT below this line
# Note: your commands must be indented with TABS. Spaces will not work.

# Change this to the AWS account where integration tests will run. Integration tests usually only run in a dev account.
INTEGRATION_ACCOUNT = "saas-ingestion-dev"

# Change this to the relative path where python modules exist
PYTHON_MODULES = . # allow it to find /src and /tests

# (OPTIONAL)
# FIND_EXCLUDE_LOCAL must have the same format as the FIND_EXCLUDE
# e.g. FIND_EXCLUDE_LOCAL = -not -path "some/folders/*" -not -path "*/tobeignored/*"
FIND_EXCLUDE_LOCAL = 

#Add any custom setup that happens during "make setup"
.PHONY: setup_custom
setup_custom:
	echo ""

#Opt in to snyk scans failing the build if issues are detected in images
# SNYK_FAIL_BUILDS := true # Disabling for the moment while Snyk is a disaster 

#Opt in to `make vdg-check` failing the build if Terraform module README.md files are not included or are out of date
VDG_FAIL_BUILDS := true

# Fails the build if Terraform modules' examples are missing or modules are structured incorrectly
BUILD_TOOLS_VALIDATE_TERRAFORM_MODULES := false

# Fails the build if a file with the extension .yml is found.
BUILD_TOOLS_YAML_BLOCK_YML := true

# Fails the build if Markdown files (*.md) do not pass lint checks.
BUILD_TOOLS_MARKDOWN_LINT_CHECK := false

# Fails the build if any terraform submodule has a disallowed source location. (Modified modules will always be checked).
BUILD_TOOLS_TERRAFORM_STRICT_SOURCES := false

# ======================================================================================
# ======================================================================================
# ======================================================================================
# ======================================================================================

#----------------------------------
# CHECK ENV VARS
#----------------------------------
.PHONY: __ensure_ecr __ensure_aws_profile __ensure_basename __ensure_deploy_region
check_defined = \
    $(strip $(foreach 1,$1, \
        $(call __check_defined,$1,$(strip $(value 2)))))
__check_defined = \
    $(if $(value $1),, \
      $(error Undefined $1$(if $2, ($2))))

__ensure_basename:
	@:$(call check_defined, BASENAME, Needed to deploy basename-specific configuration. See README.md for details.)

__ensure_default_tag:
	@:$(call check_defined, DEFAULT_TAG, Please set a default ECR tag for the lambda image)

__ensure_aws_profile:
	@:$(call check_defined, AWS_PROFILE, Needed to connect to AWS. See README.md for details.)

__ensure_deploy_region:
	@:$(call check_defined, REGION, Needed to specify the deployment region. See README.md for details.)

__prerequisite: __ensure_basename __ensure_aws_profile __ensure_ecr __ensure_deploy_region

#----------------------------------
# TERRAFORM
#----------------------------------
REGION ?= us-west-2
STACK ?= nexus

ifndef AWS_PROFILE
AWS_PROFILE := bastion-sso
endif

ifndef TERRAFORM_DIR
TERRAFORM_DIR := ./terraform/examples/$(STACK)
endif

TERRAFORM_LOCAL_BT := docker run -i \
				-v $(shell pwd):/vectra:delegated \
				-w /vectra/$(TERRAFORM_DIR) \
				-v ~/.aws:/home/<USER>/.aws:rw \
				-v ~/.ssh:/home/<USER>/.ssh:ro \
				-v $(BUILD_TOOLS_MAKEFILES_PATH):/vectra/.Makefiles \
				-v $(shell pwd)/.Makefiles/tmp:/vectra/.Makefiles/tmp \
				-e AWS_PROFILE=$(AWS_PROFILE) \
				-e TF_LOG=$(TF_LOG) \
				-e TF_VAR_is_in_vpc=false \
				--entrypoint=terraform \
				375412324511.dkr.ecr.us-west-2.amazonaws.com/cd/terraform:202401.0.0

# Terraform variable definitions
TF_VARS := -var "basename=$(BASENAME)"

.PHONY: init
init: __prerequisite
	@echo -e "\033[1;32mSettings: \033[0m"
	@echo -e "  BASENAME    = $(BASENAME)"
	@echo -e "  AWS_PROFILE = $(AWS_PROFILE)"
	@echo -e "  REGION      = $(REGION)"
	@echo -e "\033[1;32mRunning Terraform init command:\033[0m"
	@echo -e "  ${TERRAFORM_LOCAL_BT} init $(TF_VARS) -backend-config=\"key=$(STACK)/$(BASENAME)/$(REGION).tfstate\"\n"
	${TERRAFORM_LOCAL_BT} init $(TF_VARS) -backend-config="key=$(STACK)/$(BASENAME)/$(REGION).tfstate"

.PHONY: validate
validate: init  ## validate the terrafrom configuration files
	${TERRAFORM_LOCAL_BT} validate

.PHONY: plan
plan: init
	${TERRAFORM_LOCAL_BT} plan $(TF_VARS)

.PHONY: apply
apply: init
	${TERRAFORM_LOCAL_BT} apply $(TF_VARS)

.PHONY: destroy
destroy: init
	${TERRAFORM_LOCAL_BT} destroy $(TF_VARS)


#----------------------------------
# PR GEN
#----------------------------------
.PONY: set_ingestion_cluster
set_ingestion_cluster: 
	aws eks update-kubeconfig --name ingestionv1 --profile saas-ingestion-dev --region us-west-2

.PONY: prgen_update
prgen_update: tag build docker_preview
	@echo -e "**** Use 'make argo_deploy' instead ****"
	${SHELL} ./bin/prgen_update.sh

.PONY: prgen_update_commit
prgen_update_commit: tag build docker_preview
	@echo -e "**** Use 'make argo_deploy' instead ****"
	${SHELL} ./bin/prgen_update.sh auto_commit

#----------------------------------
# LOCAL TESTING/DEVELOPMENT
#----------------------------------

.PONY: nexus-unit
nexus-unit:
	docker-compose run nexus-test tests/nexus/unit

.PONY: nexus-integration
nexus-integration:
	docker-compose run nexus-test tests/nexus/integration

.PONY: nexus
nexus:
	docker-compose up nexus

.PONY: nexus-debug
nexus-debug:
	docker-compose up nexus-debug

.PONY: shutdown
shutdown:
	docker-compose down

.PONY: restart
restart: shutdown nexus

.PONY: restart-debug
restart-debug: shutdown nexus-debug

.PONY: mock-logflow-validation
mock-logflow-validation:
	python3 bin/local_dev/mock_logflow_validation.py

#----------------------------------
# CLIENT GENERATION
#----------------------------------

.PONY: client
client:
	${SHELL} ./bin/generate_client.sh

