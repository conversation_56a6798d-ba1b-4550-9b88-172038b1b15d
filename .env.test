BASENAME=test

AWS_REGION=us-west-2
AWS_DEFAULT_REGION=us-west-2

AWS_ENDPOINT="http://localhost.localstack.cloud:4566"
STS_ENDPOINT="http://localhost.localstack.cloud:4566"
AWS_ACCESS_KEY_ID=accesskey
AWS_SECRET_ACCESS_KEY=secretkey

NEXUS_TABLE_NAME=nexus
DDB_ENDPOINT_URL=http://dynamodb:8001
NEXUS_SNS_TOPIC_ARN=arn:aws:sns:us-west-2:000000000000:local-nexus-sns-topic

CONSENT_INTERNAL_URL=http://mock-api:8002
CONSENT_INTERNAL_DELETE_ROLE_ARN=arn:aws:iam::00000000000:role/mock-service

PAGINATION_LIMIT=10
METRICS_ENDPOINT=localhost:8125

# Argo CD Values
APP_NAME="nexus"
APP_DESC="Nexus Kubernetes Application"
AUTHORIZATION_TOKEN=test