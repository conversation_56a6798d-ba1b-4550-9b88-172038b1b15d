# Cloudplatform kubeconform spec for use with make helm_lint
# See https://github.com/jtyr/kubeconform-helm

# Remote Schemas in the openapi json format
schema-location:
  - default
  - https://raw.githubusercontent.com/kubernetes/kubernetes/release-1.24/api/openapi-spec/v3/apis__{{.Group}}_openapi.json # Kubernetes Default Specs
  - https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/{{.Group}}/{{.ResourceKind}}_{{.ResourceAPIVersion}}.json # Most CRDs are here
  - https://raw.githubusercontent.com/istio/api/master/networking/{{.ResourceAPIVersion}}/{{.ResourceKind}}.gen.json # Most of Istio
  - https://raw.githubusercontent.com/istio/api/master/networking/{{.ResourceAPIVersion}}/envoy_filter.gen.json # Istio EnvoyFilters
  - https://raw.githubusercontent.com/istio/api/master/security/{{.ResourceAPIVersion}}/authorization_policy.gen.json # Istio AuthPolicy
  - https://raw.githubusercontent.com/istio/api/master/security/{{.ResourceAPIVersion}}/peer_authentication.gen.json # Istio PeerAuth
  
# Command line options that can be specified without a value must have boolean
# value in the config file
summary: false
verbose: false
strict: false
kubernetes-version: 1.24.0