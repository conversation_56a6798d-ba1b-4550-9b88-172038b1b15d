# Nexus Deployment

### Message

#### Dev

> Title: **ingestion-nexus 202409.0.0 Deployment to DEV**
>
> @Deployments, we are about to release the following to **saas-ingestion-dev**:
>
> Repos
>
> - nexus
>
> Regions
>
> - us-west-2, eu-west-1

#### Prod

> Title: **ingestion-nexus 202409.0.0 Deployment to PROD**
>
> @Deployments, we are about to release the following to **saas-ingestion-prod**:
>
> Repos
>
> - nexus
>
> Regions
>
> - us-west-2,eu-west-1,eu-central-2,ca-central-1,ap-southeast-2



## Jenkins Trigger Deployment

You can deploy Application Code changes, Infra changes or both.

Below are the options for doing each type of deployment. Using **202409.1.0** as an example, this will need to be updated.

### Jenkins Triggers

- [dev](https://jenkins.root.vectra-svc.ai/view/ingestion-nexus/job/saas-ingestion-nexus_-_dev_-_any_-_automation_-_Trigger_Release/)
- [prod](https://jenkins.root.vectra-svc.ai/view/ingestion-nexus/job/saas-ingestion-nexus_-_prod_-_any_-_automation_-_Trigger_Release/)



### Dev Deployment

> | Option                                | Description                                                  | App Code Only        | App Code and Infra                                     | Infra Only                                             |
> | ------------------------------------- | ------------------------------------------------------------ | -------------------- | ------------------------------------------------------ | ------------------------------------------------------ |
> | **release_repos**                     | publish a **new** version to the repo via a **git tag**      | ✅                    | ✅                                                      |                                                        |
> | **version**                           | choose '**new**' to create new major tag                     |                      | new                                                    |                                                        |
> | **repos_to_release**                  | choose '**nexus**' to release nexus                          |                      | nexus                                                  |                                                        |
> | **build_and_deliver_artifacts**       | we are updating code so need to build new docker images      | ✅                    | ✅                                                      |                                                        |
> | **build_and_deliver_wheel_artifacts** | we will create new wheel for the client                      | ✅                    | ✅                                                      |                                                        |
> | **bump_kube_manifest**                | we will bump the kube manifest to update the docker image used in the pod | us-west-2, eu-west-1 | us-west-2, eu-west-1                                   |                                                        |
> | **publish_modules**                   | publish terraform modules to citizen                         |                      | ✅                                                      | ✅                                                      |
> | **bump_module_refs**                  | update module version on saas-platform                       |                      | ✅                                                      | ✅                                                      |
> | **automerge_terraform_bump**          |                                                              |                      | ✅                                                      | ✅                                                      |
> | **plan_and_apply_terraform**          |                                                              |                      | ✅                                                      | ✅                                                      |
> | **terragrunt_subdirectory**           |                                                              |                      | terragrunt/live/ingestion-nexus/dev                    | terragrunt/live/ingestion-nexus/dev                    |
> | **AWS_CREDENTIALS_override**          | run **mfa -t**                                               |                      | ****************************************************** | ****************************************************** |

### Prod Deployment

> | Option                                | Description                                                  | App Code Only                                                | App Code and Infra                                           | Infra Only                                             |
> | ------------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------ |
> | **release_repos**                     | publish a **new** version to the repo via a **git tag**      | ✅                                                            | ✅                                                            |                                                        |
> | **version**                           | insert version created during dev deployment                 | 202409.1.0                                                   | 202409.1.0                                                   | 202409.1.0                                             |
> | **repos_to_release**                  | choose '**nexus**' to release nexus                          | nexus                                                        | nexus                                                        | nexus                                                  |
> | **build_and_deliver_artifacts**       | we are updating code so need to build new docker images      | ✅                                                            | ✅                                                            |                                                        |
> | **build_and_deliver_wheel_artifacts** | Wheel artifact created during dev                            |                                                              |                                                              |                                                        |
> | **bump_kube_manifest**                | we will bump the kube manifest to update the docker image used in the pod | us-west-2,eu-west-1,eu-central-2,ca-central-1,ap-southeast-2 | us-west-2,eu-west-1,eu-central-2,ca-central-1,ap-southeast-2 |                                                        |
> | **publish_modules**                   | publish terraform modules to citizen                         |                                                              | ✅                                                            | ✅                                                      |
> | **bump_module_refs**                  | update module version on saas-platform                       |                                                              | ✅                                                            | ✅                                                      |
> | **automerge_terraform_bump**          |                                                              |                                                              | ✅                                                            | ✅                                                      |
> | **plan_and_apply_terraform**          |                                                              |                                                              | ✅                                                            | ✅                                                      |
> | **terragrunt_subdirectory**           |                                                              |                                                              | terragrunt/live/ingestion-nexus/dev                          | terragrunt/live/ingestion-nexus/dev                    |
> | **AWS_CREDENTIALS_override**          | run **mfa -t**                                               |                                                              | ******************************************************       | ****************************************************** |

## Nexus-CIDM Sync deployments

The Nexus-CIDM Sync cron job can be deployed either in tandem with changes to Nexus or as a standalone deployment.
Simply follow the Nexus patterns for DEV and PROD above, but select 'nexus-cidm-sync' checkbox in the Repos section.

## Post deployment checks

### Manual Checks

#### Open public API health endpoints

##### Dev

- https://nexus-uw2.ingestion.dev.vectra-svc.ai/
- https://nexus-ew1.ingestion.dev.vectra-svc.ai/

##### Prod


- https://nexus-uw2.ingestion.prod.vectra-svc.ai
- https://nexus-ew1.ingestion.prod.vectra-svc.ai
- https://nexus-ec2.ingestion.prod.vectra-svc.ai
- https://nexus-as2.ingestion.prod.vectra-svc.ai
- https://nexus-cc1.ingestion.prod.vectra-svc.ai