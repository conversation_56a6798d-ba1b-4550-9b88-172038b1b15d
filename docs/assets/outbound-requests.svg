<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="786px" height="345px" viewBox="-0.5 -0.5 786 345" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.8 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36&quot; version=&quot;24.7.8&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;DXwr74U6XS5-uPAVooSO&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;3166&quot; dy=&quot;1906&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1920&quot; pageHeight=&quot;1200&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-23&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1920&quot; y=&quot;-1200&quot; width=&quot;786&quot; height=&quot;345&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;z6RTsAgXz8rAxlSO0AM7-1&quot; value=&quot;AWS Account: saas-ingestion&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1914&quot; y=&quot;-1116&quot; width=&quot;272&quot; height=&quot;220&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-1&quot; value=&quot;AWS Account: saas-network&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1611&quot; y=&quot;-1177&quot; width=&quot;198&quot; height=&quot;74&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-2&quot; value=&quot;CIDM&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;flipV=1;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;FQQEj_CTxN_2VqycvZyC-1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;27&quot; y=&quot;36&quot; width=&quot;63&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-3&quot; value=&quot;&amp;amp;nbsp;Nexus&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;verticalAlign=top;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1902&quot; y=&quot;-1081&quot; width=&quot;120&quot; height=&quot;135&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-4&quot; value=&quot;AWS Account: saas-app&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1611&quot; y=&quot;-1086&quot; width=&quot;198&quot; height=&quot;74&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-5&quot; value=&quot;Consent&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;flipV=1;&quot; parent=&quot;FQQEj_CTxN_2VqycvZyC-4&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;29&quot; y=&quot;36&quot; width=&quot;63&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-6&quot; value=&quot;AWS Account: saas-ingestion&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1611&quot; y=&quot;-986&quot; width=&quot;247&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-7&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;FQQEj_CTxN_2VqycvZyC-6&quot; source=&quot;FQQEj_CTxN_2VqycvZyC-8&quot; target=&quot;FQQEj_CTxN_2VqycvZyC-9&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-8&quot; value=&quot;SQS Queue&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;&quot; parent=&quot;FQQEj_CTxN_2VqycvZyC-6&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;29&quot; y=&quot;46&quot; width=&quot;83&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-9&quot; value=&quot;Logflow&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;FQQEj_CTxN_2VqycvZyC-6&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;154&quot; y=&quot;46&quot; width=&quot;83&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-10&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.51;exitDx=0;exitDy=0;exitPerimeter=0;&quot; parent=&quot;1&quot; source=&quot;FQQEj_CTxN_2VqycvZyC-18&quot; target=&quot;FQQEj_CTxN_2VqycvZyC-15&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1802&quot; y=&quot;-982&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-11&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;&quot; parent=&quot;1&quot; source=&quot;FQQEj_CTxN_2VqycvZyC-17&quot; target=&quot;FQQEj_CTxN_2VqycvZyC-16&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1802&quot; y=&quot;-982&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-12&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;&quot; parent=&quot;1&quot; source=&quot;FQQEj_CTxN_2VqycvZyC-17&quot; target=&quot;FQQEj_CTxN_2VqycvZyC-5&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1802&quot; y=&quot;-982&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YNjBo5ylqL8O-KlUKp2C-1&quot; value=&quot;&amp;lt;div&amp;gt;POST&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;PATCH&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;DELETE&amp;lt;/div&amp;gt;&quot; style=&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;FQQEj_CTxN_2VqycvZyC-12&quot;&gt;&#10;          &lt;mxGeometry x=&quot;0.1848&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-12&quot; y=&quot;1&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-13&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;&quot; parent=&quot;1&quot; source=&quot;FQQEj_CTxN_2VqycvZyC-17&quot; target=&quot;FQQEj_CTxN_2VqycvZyC-2&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1802&quot; y=&quot;-982&quot; as=&quot;sourcePoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-14&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;FQQEj_CTxN_2VqycvZyC-15&quot; target=&quot;FQQEj_CTxN_2VqycvZyC-8&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-15&quot; value=&quot;SNS Topic&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;flipV=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1733&quot; y=&quot;-940&quot; width=&quot;74&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-16&quot; value=&quot;DynamoDB&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;flipV=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1733&quot; y=&quot;-988&quot; width=&quot;79&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-17&quot; value=&quot;App&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1869&quot; y=&quot;-1048&quot; width=&quot;42&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-18&quot; value=&quot;Async Queue&quot; style=&quot;sketch=0;aspect=fixed;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=none;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;fillColor=#00188D;shape=mxgraph.azure.queue_generic&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1873&quot; y=&quot;-996&quot; width=&quot;50&quot; height=&quot;15&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-19&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; source=&quot;FQQEj_CTxN_2VqycvZyC-17&quot; target=&quot;FQQEj_CTxN_2VqycvZyC-18&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-20&quot; value=&quot;Nexus Infra&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1282&quot; y=&quot;-1026&quot; width=&quot;110&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-21&quot; value=&quot;&amp;lt;h1 style=&amp;quot;margin-top: 0px;&amp;quot;&amp;gt;Legend&amp;lt;/h1&amp;gt;&quot; style=&quot;text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1282&quot; y=&quot;-1077&quot; width=&quot;103&quot; height=&quot;57&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;FQQEj_CTxN_2VqycvZyC-22&quot; value=&quot;Dependant&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1282&quot; y=&quot;-978.75&quot; width=&quot;110&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;-sWKeZZiHoXMoF0Xh2kS-1&quot; value=&quot;Not Implemented&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1282&quot; y=&quot;-931.75&quot; width=&quot;110&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="FQQEj_CTxN_2VqycvZyC-23"><g><rect x="0" y="0" width="786" height="345" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/></g></g><g data-cell-id="z6RTsAgXz8rAxlSO0AM7-1"><g><path d="M 6 84 L 278 84 L 278 304 L 6 304 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 6 84 L 6 109 L 31 109 L 31 84 L 6 84 Z M 10.09 87.69 L 26.91 87.69 C 27.01 87.69 27.12 87.73 27.19 87.81 C 27.27 87.88 27.31 87.99 27.31 88.09 L 27.31 104.91 C 27.31 105.01 27.27 105.12 27.19 105.19 C 27.12 105.27 27.01 105.31 26.91 105.31 L 10.09 105.31 C 9.99 105.31 9.88 105.27 9.81 105.19 C 9.73 105.12 9.69 105.01 9.69 104.91 L 9.69 88.09 C 9.69 87.99 9.73 87.88 9.81 87.81 C 9.88 87.73 9.99 87.69 10.09 87.69 Z M 10.49 88.49 L 10.49 104.51 L 26.51 104.51 L 26.51 88.49 L 10.49 88.49 Z M 22.5 89.31 C 22.65 89.31 22.79 89.38 22.86 89.52 L 25.46 94.72 C 25.53 94.84 25.52 94.99 25.45 95.11 C 25.37 95.23 25.25 95.3 25.11 95.3 L 19.9 95.3 L 19.9 95.3 C 19.76 95.3 19.63 95.23 19.56 95.11 C 19.49 94.99 19.48 94.84 19.54 94.72 L 22.14 89.52 C 22.21 89.38 22.36 89.31 22.5 89.31 Z M 22.5 90.59 L 20.55 94.5 L 24.46 94.5 L 22.5 90.59 Z M 12.09 93.3 L 17.3 93.3 C 17.41 93.3 17.51 93.34 17.58 93.41 C 17.66 93.49 17.7 93.59 17.7 93.7 L 17.7 98.9 C 17.7 99.01 17.66 99.11 17.58 99.19 C 17.51 99.26 17.41 99.3 17.3 99.3 L 12.09 99.3 C 11.99 99.3 11.89 99.26 11.81 99.19 C 11.74 99.11 11.69 99.01 11.69 98.9 L 11.69 93.7 C 11.69 93.59 11.74 93.49 11.81 93.41 C 11.89 93.34 11.99 93.3 12.09 93.3 Z M 12.49 94.1 L 12.49 98.5 L 16.9 98.5 L 16.9 94.1 L 12.49 94.1 Z M 21.7 97.3 C 23.47 97.3 24.9 98.74 24.91 100.5 C 24.9 102.27 23.47 103.7 21.7 103.71 C 19.94 103.7 18.5 102.27 18.5 100.5 C 18.5 98.74 19.94 97.3 21.7 97.3 Z M 21.7 98.1 C 20.38 98.1 19.3 99.18 19.3 100.5 C 19.3 101.83 20.38 102.9 21.7 102.91 C 23.03 102.9 24.1 101.83 24.1 100.5 C 24.1 99.18 23.03 98.1 21.7 98.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 240px; height: 1px; padding-top: 91px; margin-left: 38px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: saas-ingestion</div></div></div></foreignObject><text x="38" y="103" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account: saas-ingestion</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-1"><g><path d="M 309 23 L 507 23 L 507 97 L 309 97 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 309 23 L 309 48 L 334 48 L 334 23 L 309 23 Z M 313.09 26.69 L 329.91 26.69 C 330.01 26.69 330.12 26.73 330.19 26.81 C 330.27 26.88 330.31 26.99 330.31 27.09 L 330.31 43.91 C 330.31 44.01 330.27 44.12 330.19 44.19 C 330.12 44.27 330.01 44.31 329.91 44.31 L 313.09 44.31 C 312.99 44.31 312.88 44.27 312.81 44.19 C 312.73 44.12 312.69 44.01 312.69 43.91 L 312.69 27.09 C 312.69 26.99 312.73 26.88 312.81 26.81 C 312.88 26.73 312.99 26.69 313.09 26.69 Z M 313.49 27.49 L 313.49 43.51 L 329.51 43.51 L 329.51 27.49 L 313.49 27.49 Z M 325.5 28.31 C 325.65 28.31 325.79 28.38 325.86 28.52 L 328.46 33.72 C 328.53 33.84 328.52 33.99 328.45 34.11 C 328.37 34.23 328.25 34.3 328.11 34.3 L 322.9 34.3 L 322.9 34.3 C 322.76 34.3 322.63 34.23 322.56 34.11 C 322.49 33.99 322.48 33.84 322.54 33.72 L 325.14 28.52 C 325.21 28.38 325.36 28.31 325.5 28.31 Z M 325.5 29.59 L 323.55 33.5 L 327.46 33.5 L 325.5 29.59 Z M 315.09 32.3 L 320.3 32.3 C 320.41 32.3 320.51 32.34 320.58 32.41 C 320.66 32.49 320.7 32.59 320.7 32.7 L 320.7 37.9 C 320.7 38.01 320.66 38.11 320.58 38.19 C 320.51 38.26 320.41 38.3 320.3 38.3 L 315.09 38.3 C 314.99 38.3 314.89 38.26 314.81 38.19 C 314.74 38.11 314.69 38.01 314.69 37.9 L 314.69 32.7 C 314.69 32.59 314.74 32.49 314.81 32.41 C 314.89 32.34 314.99 32.3 315.09 32.3 Z M 315.49 33.1 L 315.49 37.5 L 319.9 37.5 L 319.9 33.1 L 315.49 33.1 Z M 324.7 36.3 C 326.47 36.3 327.9 37.74 327.91 39.5 C 327.9 41.27 326.47 42.7 324.7 42.71 C 322.94 42.7 321.5 41.27 321.5 39.5 C 321.5 37.74 322.94 36.3 324.7 36.3 Z M 324.7 37.1 C 323.38 37.1 322.3 38.18 322.3 39.5 C 322.3 40.83 323.38 41.9 324.7 41.91 C 326.03 41.9 327.1 40.83 327.1 39.5 C 327.1 38.18 326.03 37.1 324.7 37.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 166px; height: 1px; padding-top: 30px; margin-left: 341px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: saas-network</div></div></div></foreignObject><text x="341" y="42" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account: saas-network</text></switch></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-2"><g><rect x="336" y="59" width="63" height="26" fill="none" stroke="none" transform="translate(0,72)scale(1,-1)translate(0,-72)" pointer-events="all"/><path d="M 336.31 58.65 C 336.31 58.65 336.31 58.65 336.31 58.65 M 336.31 58.65 C 336.31 58.65 336.31 58.65 336.31 58.65 M 336.05 65.04 C 339.13 62.24 338.4 59.69 341.29 59.01 M 336.05 65.04 C 338.33 62.39 340.39 59.5 341.29 59.01 M 335.78 71.44 C 338.77 70.02 342.8 63.81 346.28 59.37 M 335.78 71.44 C 339.79 68.02 342.58 63.43 346.28 59.37 M 336.18 77.09 C 344.84 71.23 345.88 66.68 351.92 58.97 M 336.18 77.09 C 342.26 70.89 346.96 66.17 351.92 58.97 M 335.92 83.48 C 339.71 74.79 342.47 70.56 356.91 59.33 M 335.92 83.48 C 342.66 78.39 346.62 71.75 356.91 59.33 M 338.28 86.86 C 347.02 83.2 350.69 72.37 362.55 58.94 M 338.28 86.86 C 345.19 79.86 354.94 69.01 362.55 58.94 M 343.27 87.22 C 350.87 78.02 355.94 73.53 367.54 59.3 M 343.27 87.22 C 351.23 75.66 358.9 67.2 367.54 59.3 M 348.91 86.83 C 356.63 79.58 360.08 68.4 373.18 58.9 M 348.91 86.83 C 356.38 75.98 366.68 67.19 373.18 58.9 M 353.9 87.19 C 357.22 81.04 366.11 72.16 378.17 59.26 M 353.9 87.19 C 361.1 79.98 368.12 71.48 378.17 59.26 M 359.54 86.79 C 371.78 79.92 377.85 63.76 383.81 58.87 M 359.54 86.79 C 366.53 81.5 371.21 71.56 383.81 58.87 M 364.53 87.15 C 370.82 78.65 378.95 69.61 388.8 59.23 M 364.53 87.15 C 373.29 79.15 378.82 69.91 388.8 59.23 M 370.17 86.76 C 381.04 81.27 388.5 71.8 394.44 58.83 M 370.17 86.76 C 374.84 79.3 379.29 74.5 394.44 58.83 M 375.16 87.12 C 382.39 78.75 392.88 70.84 399.43 59.19 M 375.16 87.12 C 382.03 79.36 387.78 73.22 399.43 59.19 M 380.8 86.72 C 388.45 81.26 388.12 77.45 399.17 65.59 M 380.8 86.72 C 384.97 79.45 390.4 72.66 399.17 65.59 M 385.79 87.08 C 386.74 78.94 396.7 74.36 399.56 71.24 M 385.79 87.08 C 390.18 84.68 391.53 78.01 399.56 71.24 M 391.43 86.69 C 394.72 83.42 394.56 80.22 399.3 77.63 M 391.43 86.69 C 395.32 83.82 396.98 78.78 399.3 77.63 M 396.42 87.05 C 397.28 86.16 397.95 86.06 399.04 84.03 M 396.42 87.05 C 397.3 85.96 398.05 85.81 399.04 84.03" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,72)scale(1,-1)translate(0,-72)" pointer-events="all"/><path d="M 336 59 C 351.63 56.74 373.59 60.21 399 59 M 336 59 C 359.37 60.85 384 59.55 399 59 M 399 59 C 396.56 67.63 395.4 69.95 399 85 M 399 59 C 396.9 66.36 397.22 71.02 399 85 M 399 85 C 387 87.75 374.65 87.85 336 85 M 399 85 C 386.94 83.54 374.55 85.26 336 85 M 336 85 C 336.11 75.16 339.96 69.63 336 59 M 336 85 C 334.43 76.51 334.87 70.65 336 59" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,72)scale(1,-1)translate(0,-72)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 72px; margin-left: 337px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">CIDM</div></div></div></foreignObject><text x="367" y="76" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">CIDM</text></switch></g></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-3"><g><rect x="18" y="119" width="120" height="135" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 126px; margin-left: 20px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"> Nexus</div></div></div></foreignObject><text x="20" y="138" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px"> Nexus</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-4"><g><path d="M 309 114 L 507 114 L 507 188 L 309 188 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 309 114 L 309 139 L 334 139 L 334 114 L 309 114 Z M 313.09 117.69 L 329.91 117.69 C 330.01 117.69 330.12 117.73 330.19 117.81 C 330.27 117.88 330.31 117.99 330.31 118.09 L 330.31 134.91 C 330.31 135.01 330.27 135.12 330.19 135.19 C 330.12 135.27 330.01 135.31 329.91 135.31 L 313.09 135.31 C 312.99 135.31 312.88 135.27 312.81 135.19 C 312.73 135.12 312.69 135.01 312.69 134.91 L 312.69 118.09 C 312.69 117.99 312.73 117.88 312.81 117.81 C 312.88 117.73 312.99 117.69 313.09 117.69 Z M 313.49 118.49 L 313.49 134.51 L 329.51 134.51 L 329.51 118.49 L 313.49 118.49 Z M 325.5 119.31 C 325.65 119.31 325.79 119.38 325.86 119.52 L 328.46 124.72 C 328.53 124.84 328.52 124.99 328.45 125.11 C 328.37 125.23 328.25 125.3 328.11 125.3 L 322.9 125.3 L 322.9 125.3 C 322.76 125.3 322.63 125.23 322.56 125.11 C 322.49 124.99 322.48 124.84 322.54 124.72 L 325.14 119.52 C 325.21 119.38 325.36 119.31 325.5 119.31 Z M 325.5 120.59 L 323.55 124.5 L 327.46 124.5 L 325.5 120.59 Z M 315.09 123.3 L 320.3 123.3 C 320.41 123.3 320.51 123.34 320.58 123.41 C 320.66 123.49 320.7 123.59 320.7 123.7 L 320.7 128.9 C 320.7 129.01 320.66 129.11 320.58 129.19 C 320.51 129.26 320.41 129.3 320.3 129.3 L 315.09 129.3 C 314.99 129.3 314.89 129.26 314.81 129.19 C 314.74 129.11 314.69 129.01 314.69 128.9 L 314.69 123.7 C 314.69 123.59 314.74 123.49 314.81 123.41 C 314.89 123.34 314.99 123.3 315.09 123.3 Z M 315.49 124.1 L 315.49 128.5 L 319.9 128.5 L 319.9 124.1 L 315.49 124.1 Z M 324.7 127.3 C 326.47 127.3 327.9 128.74 327.91 130.5 C 327.9 132.27 326.47 133.7 324.7 133.71 C 322.94 133.7 321.5 132.27 321.5 130.5 C 321.5 128.74 322.94 127.3 324.7 127.3 Z M 324.7 128.1 C 323.38 128.1 322.3 129.18 322.3 130.5 C 322.3 131.83 323.38 132.9 324.7 132.91 C 326.03 132.9 327.1 131.83 327.1 130.5 C 327.1 129.18 326.03 128.1 324.7 128.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 166px; height: 1px; padding-top: 121px; margin-left: 341px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: saas-app</div></div></div></foreignObject><text x="341" y="133" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account: saas-app</text></switch></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-5"><g><rect x="338" y="150" width="63" height="26" fill="#fff2cc" stroke="#d6b656" transform="translate(0,163)scale(1,-1)translate(0,-163)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 163px; margin-left: 339px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Consent</div></div></div></foreignObject><text x="370" y="167" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Consent</text></switch></g></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-6"><g><path d="M 309 214 L 556 214 L 556 304 L 309 304 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 309 214 L 309 239 L 334 239 L 334 214 L 309 214 Z M 313.09 217.69 L 329.91 217.69 C 330.01 217.69 330.12 217.73 330.19 217.81 C 330.27 217.88 330.31 217.99 330.31 218.09 L 330.31 234.91 C 330.31 235.01 330.27 235.12 330.19 235.19 C 330.12 235.27 330.01 235.31 329.91 235.31 L 313.09 235.31 C 312.99 235.31 312.88 235.27 312.81 235.19 C 312.73 235.12 312.69 235.01 312.69 234.91 L 312.69 218.09 C 312.69 217.99 312.73 217.88 312.81 217.81 C 312.88 217.73 312.99 217.69 313.09 217.69 Z M 313.49 218.49 L 313.49 234.51 L 329.51 234.51 L 329.51 218.49 L 313.49 218.49 Z M 325.5 219.31 C 325.65 219.31 325.79 219.38 325.86 219.52 L 328.46 224.72 C 328.53 224.84 328.52 224.99 328.45 225.11 C 328.37 225.23 328.25 225.3 328.11 225.3 L 322.9 225.3 L 322.9 225.3 C 322.76 225.3 322.63 225.23 322.56 225.11 C 322.49 224.99 322.48 224.84 322.54 224.72 L 325.14 219.52 C 325.21 219.38 325.36 219.31 325.5 219.31 Z M 325.5 220.59 L 323.55 224.5 L 327.46 224.5 L 325.5 220.59 Z M 315.09 223.3 L 320.3 223.3 C 320.41 223.3 320.51 223.34 320.58 223.41 C 320.66 223.49 320.7 223.59 320.7 223.7 L 320.7 228.9 C 320.7 229.01 320.66 229.11 320.58 229.19 C 320.51 229.26 320.41 229.3 320.3 229.3 L 315.09 229.3 C 314.99 229.3 314.89 229.26 314.81 229.19 C 314.74 229.11 314.69 229.01 314.69 228.9 L 314.69 223.7 C 314.69 223.59 314.74 223.49 314.81 223.41 C 314.89 223.34 314.99 223.3 315.09 223.3 Z M 315.49 224.1 L 315.49 228.5 L 319.9 228.5 L 319.9 224.1 L 315.49 224.1 Z M 324.7 227.3 C 326.47 227.3 327.9 228.74 327.91 230.5 C 327.9 232.27 326.47 233.7 324.7 233.71 C 322.94 233.7 321.5 232.27 321.5 230.5 C 321.5 228.74 322.94 227.3 324.7 227.3 Z M 324.7 228.1 C 323.38 228.1 322.3 229.18 322.3 230.5 C 322.3 231.83 323.38 232.9 324.7 232.91 C 326.03 232.9 327.1 231.83 327.1 230.5 C 327.1 229.18 326.03 228.1 324.7 228.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 215px; height: 1px; padding-top: 221px; margin-left: 341px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: saas-ingestion</div></div></div></foreignObject><text x="341" y="233" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account: saas-ingestion</text></switch></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-7"><g><path d="M 421 273 L 456.63 273" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 461.88 273 L 454.88 276.5 L 456.63 273 L 454.88 269.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-8"><g><rect x="338" y="260" width="83" height="26" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 81px; height: 1px; padding-top: 273px; margin-left: 339px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SQS Queue</div></div></div></foreignObject><text x="380" y="277" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SQS Queue</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-9"><g><rect x="463" y="260" width="83" height="26" fill="none" stroke="none" pointer-events="all"/><path d="M 462.9 260.12 C 462.9 260.12 462.9 260.12 462.9 260.12 M 462.9 260.12 C 462.9 260.12 462.9 260.12 462.9 260.12 M 463.29 265.76 C 465.52 265.09 466.32 263.53 468.54 259.72 M 463.29 265.76 C 465.17 263.12 467.21 261.89 468.54 259.72 M 463.03 272.16 C 462.71 269.05 469.4 265.57 473.53 260.08 M 463.03 272.16 C 464.58 268.37 469.6 264.22 473.53 260.08 M 462.77 278.56 C 468.67 272.5 473.1 271.41 479.17 259.69 M 462.77 278.56 C 468.34 270.17 473.72 264.67 479.17 259.69 M 463.16 284.2 C 466.31 280.49 467.83 278.34 484.16 260.05 M 463.16 284.2 C 469.65 277.11 473.47 272.21 484.16 260.05 M 466.18 286.82 C 476.43 272.5 481.8 265.28 489.8 259.65 M 466.18 286.82 C 470.42 280.67 478.1 272.07 489.8 259.65 M 471.83 286.43 C 483.4 274.73 490.85 263.34 494.79 260.01 M 471.83 286.43 C 479.98 278.85 486.86 267.88 494.79 260.01 M 476.81 286.79 C 487.47 280.57 497.07 270.03 499.78 260.37 M 476.81 286.79 C 484.67 278.23 495.79 266.35 499.78 260.37 M 482.46 286.39 C 487.05 278.03 492.9 274.66 505.42 259.98 M 482.46 286.39 C 488.93 276.53 499.75 266.62 505.42 259.98 M 487.44 286.75 C 494.47 278.69 504.9 264.38 510.41 260.34 M 487.44 286.75 C 492.2 281.39 501 270.91 510.41 260.34 M 493.09 286.36 C 500.53 283.86 506.26 270.53 516.05 259.94 M 493.09 286.36 C 497.41 282.24 503.4 274.94 516.05 259.94 M 498.07 286.72 C 503.02 280.33 505.98 274.27 521.04 260.3 M 498.07 286.72 C 502.7 277.91 509.95 274.09 521.04 260.3 M 503.72 286.33 C 512.59 278.58 519.01 268.64 526.68 259.91 M 503.72 286.33 C 512.37 276.34 519.6 269.53 526.68 259.91 M 508.7 286.69 C 511.47 279.36 524.84 273.32 531.67 260.27 M 508.7 286.69 C 512.95 279.33 517.79 274.23 531.67 260.27 M 514.35 286.29 C 523.51 276.25 532.94 265.64 537.31 259.88 M 514.35 286.29 C 520.93 279.01 527.73 267.98 537.31 259.88 M 519.33 286.65 C 522.87 279.54 534.58 270.94 542.3 260.24 M 519.33 286.65 C 527.45 277.13 534.09 269.64 542.3 260.24 M 524.98 286.26 C 525.73 280.13 538.09 274.11 545.97 262.11 M 524.98 286.26 C 530.93 276.23 540.2 267.83 545.97 262.11 M 529.96 286.62 C 535.36 280.59 535.85 279.44 546.37 267.75 M 529.96 286.62 C 532.38 283.01 536.83 277.82 546.37 267.75 M 534.95 286.98 C 537.55 281.67 541.33 279.06 546.1 274.15 M 534.95 286.98 C 536.67 283.39 541.97 282.25 546.1 274.15 M 540.59 286.58 C 543.46 285.9 541.72 281.7 546.5 279.79 M 540.59 286.58 C 542.5 283.98 546.04 281.91 546.5 279.79" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 463 260 C 484.49 258.91 511.82 257.18 546 260 M 463 260 C 487.08 260.52 513.73 260.27 546 260 M 546 260 C 545.75 268.24 544.64 277.09 546 286 M 546 260 C 545.73 268.82 546.61 275.11 546 286 M 546 286 C 518.26 287.59 486.39 284.67 463 286 M 546 286 C 520.06 285.44 498.72 283.59 463 286 M 463 286 C 465.66 276.25 460.94 272.43 463 260 M 463 286 C 463.14 274.78 461.91 269.13 463 260" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 81px; height: 1px; padding-top: 273px; margin-left: 464px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Logflow</div></div></div></foreignObject><text x="505" y="277" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Logflow</text></switch></g></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-10"><g><path d="M 97 211.65 L 127 211.65 L 157 273 L 180.63 273" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 185.88 273 L 178.88 276.5 L 180.63 273 L 178.88 269.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-11"><g><path d="M 93 165 L 123 165 L 157 225 L 180.63 225" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 185.88 225 L 178.88 228.5 L 180.63 225 L 178.88 221.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-12"><g><path d="M 93 165 L 123 165 L 308 163 L 331.63 163" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 336.88 163 L 329.88 166.5 L 331.63 163 L 329.88 159.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g data-cell-id="YNjBo5ylqL8O-KlUKp2C-1"><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 165px; margin-left: 226px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div>POST</div><div>PATCH</div><div>DELETE</div></div></div></div></foreignObject><text x="226" y="168" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">POST...</text></switch></g></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-13"><g><path d="M 93 165 L 123 165 L 306 72 L 329.63 72" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 334.88 72 L 327.88 75.5 L 329.63 72 L 327.88 68.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-14"><g><path d="M 261 273 L 331.63 273" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 336.88 273 L 329.88 276.5 L 331.63 273 L 329.88 269.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-15"><g><rect x="187" y="260" width="74" height="26" fill="#d5e8d4" stroke="#82b366" transform="translate(0,273)scale(1,-1)translate(0,-273)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 72px; height: 1px; padding-top: 273px; margin-left: 188px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SNS Topic</div></div></div></foreignObject><text x="224" y="277" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">SNS Topic</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-16"><g><rect x="187" y="212" width="79" height="26" fill="#d5e8d4" stroke="#82b366" transform="translate(0,225)scale(1,-1)translate(0,-225)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 225px; margin-left: 188px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">DynamoDB</div></div></div></foreignObject><text x="227" y="229" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">DynamoDB</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-17"><g><rect x="51" y="152" width="42" height="26" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 40px; height: 1px; padding-top: 165px; margin-left: 52px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">App</div></div></div></foreignObject><text x="72" y="169" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">App</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-18"><g><rect x="47" y="204" width="50" height="15" fill="none" stroke="none" pointer-events="all"/><path d="M 47.06 205.12 C 47.24 204.45 47.87 204 48.57 204.02 L 92.46 204.02 L 97 211.71 L 92.46 219 L 48.47 219 C 48.05 218.98 47.65 218.78 47.39 218.46 C 47.12 218.14 47 217.72 47.06 217.31 Z M 48.06 217.17 C 48.18 217.67 48.65 218.02 49.17 218.01 L 92.01 218.01 L 95.99 211.71 L 92.01 204.97 L 49.17 204.97 C 48.71 204.92 48.27 205.16 48.06 205.56 Z M 49.58 207.55 C 49.62 207.09 49.97 206.72 50.44 206.65 L 61.64 206.65 C 62.1 206.72 62.45 207.09 62.49 207.55 L 62.49 215.68 C 62.37 216.07 62.01 216.35 61.58 216.37 L 50.54 216.37 C 50.12 216.35 49.75 216.07 49.63 215.68 Z M 50.94 209.43 L 50.94 215.18 L 61.23 215.18 L 61.23 209.33 L 56.49 212.85 C 56.15 213.07 55.72 213.07 55.38 212.85 Z M 55.98 211.76 L 61.03 207.89 L 50.99 207.89 Z M 63.91 207.55 C 63.93 207.13 64.21 206.78 64.61 206.65 L 75.96 206.65 C 76.36 206.78 76.65 207.13 76.67 207.55 L 76.67 215.68 C 76.57 216.02 76.31 216.28 75.96 216.37 L 64.61 216.37 C 64.27 216.28 64 216.02 63.91 215.68 Z M 65.17 209.38 L 65.17 215.18 L 75.46 215.18 L 75.41 209.38 L 70.77 212.85 C 70.41 213.12 69.91 213.12 69.56 212.85 Z M 70.21 211.76 L 75.31 207.89 L 65.17 207.89 Z M 78.13 207.55 C 78.17 207.09 78.53 206.72 78.99 206.65 L 90.14 206.65 C 90.6 206.72 90.95 207.09 91 207.55 L 91 215.48 C 90.97 215.74 90.84 215.99 90.63 216.15 C 90.42 216.32 90.15 216.4 89.89 216.37 L 78.99 216.37 C 78.59 216.33 78.25 216.06 78.13 215.68 Z M 79.34 209.38 L 79.34 215.18 L 89.63 215.18 L 89.63 209.38 L 84.89 212.85 C 84.58 213.03 84.2 213.03 83.88 212.85 Z M 84.44 211.76 L 89.48 207.89 L 79.39 207.89 Z" fill="#00188d" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 226px; margin-left: 72px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">Async Queue</div></div></div></foreignObject><text x="72" y="238" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Async Qu...</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-19"><g><path d="M 72 178 L 72 197.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 72 202.88 L 68.5 195.88 L 72 197.63 L 75.5 195.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-20"><g><rect x="638" y="174" width="110" height="30" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 189px; margin-left: 639px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Nexus Infra</div></div></div></foreignObject><text x="693" y="193" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Nexus Infra</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-21"><g><rect x="638" y="123" width="103" height="57" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 101px; height: 1px; padding-top: 130px; margin-left: 640px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 53px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><h1 style="margin-top: 0px;">Legend</h1></div></div></div></foreignObject><text x="640" y="142" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px">Legend</text></switch></g></g></g><g data-cell-id="FQQEj_CTxN_2VqycvZyC-22"><g><rect x="638" y="221.25" width="110" height="30" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 236px; margin-left: 639px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Dependant</div></div></div></foreignObject><text x="693" y="240" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Dependant</text></switch></g></g></g><g data-cell-id="-sWKeZZiHoXMoF0Xh2kS-1"><g><rect x="638" y="268.25" width="110" height="30" fill="none" stroke="none" pointer-events="all"/><path d="M 638.17 268.05 C 638.17 268.05 638.17 268.05 638.17 268.05 M 638.17 268.05 C 638.17 268.05 638.17 268.05 638.17 268.05 M 637.91 274.45 C 640.46 273.3 641.2 271.45 643.16 268.41 M 637.91 274.45 C 640.57 272.62 641.33 269.79 643.16 268.41 M 638.3 280.09 C 643 279.13 642.32 270.92 648.8 268.02 M 638.3 280.09 C 640.54 276.12 644.69 275.67 648.8 268.02 M 638.04 286.49 C 637.57 280.66 644.58 278.95 653.79 268.38 M 638.04 286.49 C 643.47 281.45 647.67 274.7 653.79 268.38 M 637.78 292.89 C 640.13 283.26 648.23 281.93 659.43 267.98 M 637.78 292.89 C 643.48 289.13 648.84 281.16 659.43 267.98 M 638.18 298.53 C 649.09 286.8 653.36 277.16 664.42 268.34 M 638.18 298.53 C 650.1 287.43 657.87 274.76 664.42 268.34 M 643.82 298.14 C 651.17 288.07 664.62 277.13 670.06 267.95 M 643.82 298.14 C 650.92 288.62 657.05 282.66 670.06 267.95 M 648.81 298.5 C 655.74 294.45 659.5 282.09 675.05 268.31 M 648.81 298.5 C 661.18 285.96 669.73 274.99 675.05 268.31 M 654.45 298.1 C 665.06 291.26 665.4 284.13 680.69 267.92 M 654.45 298.1 C 663.44 290.42 670.63 280.2 680.69 267.92 M 659.44 298.46 C 668.99 289.94 671.87 280.67 685.68 268.28 M 659.44 298.46 C 669.44 286.54 681.2 274.81 685.68 268.28 M 664.42 298.82 C 669.95 289.73 680.87 280.22 691.32 267.88 M 664.42 298.82 C 671.52 289.75 680.64 280.32 691.32 267.88 M 670.07 298.43 C 679 287.53 684.59 283.95 696.31 268.24 M 670.07 298.43 C 676.03 290.58 681.75 283.86 696.31 268.24 M 675.05 298.79 C 680.13 289.63 686.35 287.64 701.3 268.6 M 675.05 298.79 C 682.46 289.08 692.32 279.8 701.3 268.6 M 680.7 298.39 C 687.81 285.48 701.48 272.16 706.94 268.21 M 680.7 298.39 C 692.35 286.83 702.14 273.76 706.94 268.21 M 685.68 298.75 C 695.54 289.73 695.15 283.08 711.93 268.57 M 685.68 298.75 C 696.62 288.7 705.84 274.52 711.93 268.57 M 691.33 298.36 C 699.98 291.31 708.37 280.1 717.57 268.17 M 691.33 298.36 C 698.02 293.54 702.5 284.56 717.57 268.17 M 696.31 298.72 C 704.88 289.3 711.96 281.87 722.56 268.53 M 696.31 298.72 C 701.33 290.9 708.37 284.13 722.56 268.53 M 701.96 298.33 C 707.95 287.52 724.8 278.71 728.2 268.14 M 701.96 298.33 C 709.03 294.13 712.89 288.06 728.2 268.14 M 706.94 298.69 C 717.26 287.45 722.72 274.27 733.19 268.5 M 706.94 298.69 C 717.66 285.74 725.32 276.42 733.19 268.5 M 712.59 298.29 C 723.92 289.87 729.53 277.53 738.83 268.1 M 712.59 298.29 C 717.36 289.53 724.18 282.39 738.83 268.1 M 717.57 298.65 C 722.76 289.46 737.75 277.93 743.82 268.46 M 717.57 298.65 C 724.09 291.96 732.67 282.81 743.82 268.46 M 723.22 298.26 C 732.9 289.34 743.79 278.74 748.8 268.82 M 723.22 298.26 C 731.67 286.34 739.12 280.36 748.8 268.82 M 728.2 298.62 C 736.46 290.53 740.74 287.44 748.54 275.22 M 728.2 298.62 C 735.66 291.44 738.77 286.4 748.54 275.22 M 733.85 298.22 C 739.11 294.66 740.2 291.89 748.94 280.86 M 733.85 298.22 C 737.41 292.93 745.63 284.65 748.94 280.86 M 738.83 298.58 C 744.08 295.37 745.43 294.68 748.67 287.26 M 738.83 298.58 C 741.54 294.41 744.03 291.05 748.67 287.26 M 744.48 298.19 C 744.8 296.3 745.8 295.94 749.07 292.9 M 744.48 298.19 C 745.83 296.86 747.55 294.38 749.07 292.9" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 638 268.25 C 662.32 269.08 679.87 267.07 748 268.25 M 638 268.25 C 676.54 269.3 720.23 266.48 748 268.25 M 748 268.25 C 747.2 275.75 750.61 281.65 748 298.25 M 748 268.25 C 745.68 280.43 747.6 291.14 748 298.25 M 748 298.25 C 719.64 301.16 695.94 302.12 638 298.25 M 748 298.25 C 707.03 299.82 666.39 302.16 638 298.25 M 638 298.25 C 638.85 288.87 635.03 280.75 638 268.25 M 638 298.25 C 639.37 288.46 640.19 282.89 638 268.25" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 283px; margin-left: 639px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Not Implemented</div></div></div></foreignObject><text x="693" y="287" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Not Implemented</text></switch></g></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>