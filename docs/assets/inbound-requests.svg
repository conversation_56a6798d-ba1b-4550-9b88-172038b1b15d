<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="986px" height="703px" viewBox="-0.5 -0.5 986 703" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.8 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36&quot; version=&quot;24.7.8&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;0eOtZr18ozBwHQ4XLqrI&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;3664&quot; dy=&quot;989&quot; grid=&quot;0&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;1920&quot; pageHeight=&quot;1200&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-38&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;strokeColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1920&quot; width=&quot;986&quot; height=&quot;703&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;uFo9A05xg_FpJxoeQmF--1&quot; value=&quot;AWS Account: &amp;lt;br&amp;gt;saas-ingestion&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1304&quot; y=&quot;264&quot; width=&quot;160&quot; height=&quot;114&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-1&quot; value=&quot;Applicace limited to &amp;lt;br&amp;gt;AWS and O365 Connectors Only&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fontSize=10;align=left;strokeColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1880&quot; y=&quot;412&quot; width=&quot;141&quot; height=&quot;36&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-2&quot; value=&quot;AWS Account: saas-dataeng&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1648&quot; y=&quot;480&quot; width=&quot;198&quot; height=&quot;131&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-3&quot; value=&quot;Data API&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;34&quot; y=&quot;38&quot; width=&quot;65&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-4&quot; value=&quot;Jetstream&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-2&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;34&quot; y=&quot;88&quot; width=&quot;71&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-5&quot; value=&quot;AWS Account: saas-app&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1727&quot; y=&quot;123&quot; width=&quot;277&quot; height=&quot;341&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-6&quot; value=&quot;Consent&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;flipV=1;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-5&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;57&quot; y=&quot;37&quot; width=&quot;63&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-7&quot; value=&quot;VUI Provisioning&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;flipV=1;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-5&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;13&quot; y=&quot;102&quot; width=&quot;107&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-8&quot; value=&quot;VUI cloud&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;flipV=1;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-5&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;50&quot; y=&quot;167&quot; width=&quot;70&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-9&quot; value=&quot;AuthGateway&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;flipV=1;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-5&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;30&quot; y=&quot;231&quot; width=&quot;90&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-10&quot; value=&quot;AWS&amp;amp;nbsp;&amp;lt;span style=&amp;quot;background-color: initial;&amp;quot;&amp;gt;Ingestion&amp;lt;/span&amp;gt;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;flipV=1;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-5&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;23&quot; y=&quot;296&quot; width=&quot;97&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-11&quot; value=&quot;AWS Account: saas-ingestion&quot; style=&quot;points=[[0,0],[0.25,0],[0.5,0],[0.75,0],[1,0],[1,0.25],[1,0.5],[1,0.75],[1,1],[0.75,1],[0.5,1],[0.25,1],[0,1],[0,0.75],[0,0.5],[0,0.25]];outlineConnect=0;html=1;whiteSpace=wrap;fontSize=12;fontStyle=0;container=1;pointerEvents=0;collapsible=0;recursiveResize=0;shape=mxgraph.aws4.group;grIcon=mxgraph.aws4.group_account;verticalAlign=top;align=left;spacingLeft=30;dashed=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1729&quot; y=&quot;19&quot; width=&quot;279&quot; height=&quot;90&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-12&quot; value=&quot;LogFlow&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;rUzoXmEEmOzYYHfq9tto-11&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;64&quot; y=&quot;42&quot; width=&quot;63&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-13&quot; value=&quot;&amp;lt;div&amp;gt;GET&amp;lt;br&amp;gt;POST&amp;lt;br&amp;gt;PATCH&amp;lt;br&amp;gt;DELETE&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-14&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-9&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.3636&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-14&quot; value=&quot;VUI appliance&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1906&quot; y=&quot;354&quot; width=&quot;93&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-15&quot; value=&quot;Nexus&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1285&quot; y=&quot;307&quot; width=&quot;120&quot; height=&quot;46&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-16&quot; value=&quot;&amp;lt;div style=&amp;quot;font-size: 10px;&amp;quot;&amp;gt;GET&amp;lt;br&amp;gt;POST&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.278;entryY=0.298;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-12&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-34&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-17&quot; value=&quot;GET&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.12;entryY=0.51;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-4&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-23&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.6257&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1416&quot; y=&quot;567&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-18&quot; value=&quot;Proxy&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1261&quot; y=&quot;473.25&quot; width=&quot;110&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-19&quot; value=&quot;Dependency&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1261&quot; y=&quot;513.25&quot; width=&quot;110&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-20&quot; value=&quot;GET&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=entityRelationEdgeStyle;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-21&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-15&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.8656&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1487&quot; y=&quot;575&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;-1345&quot; y=&quot;580&quot; /&gt;&#10;              &lt;mxPoint x=&quot;-1299&quot; y=&quot;526&quot; /&gt;&#10;              &lt;mxPoint x=&quot;-1308&quot; y=&quot;509&quot; /&gt;&#10;            &lt;/Array&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-21&quot; value=&quot;Updater&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1450&quot; y=&quot;642&quot; width=&quot;61&quot; height=&quot;26&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-22&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-23&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-15&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-23&quot; value=&quot;&quot; style=&quot;shape=waypoint;sketch=0;fillStyle=solid;size=6;pointerEvents=1;points=[];fillColor=none;resizable=0;rotatable=0;perimeter=centerPerimeter;snapToPoint=1;strokeWidth=5;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1445&quot; y=&quot;543.25&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-24&quot; value=&quot;GET&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=entityRelationEdgeStyle;elbow=vertical;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-3&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-23&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.625&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1398&quot; y=&quot;541&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-25&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-26&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-15&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-26&quot; value=&quot;&quot; style=&quot;shape=waypoint;sketch=0;fillStyle=solid;size=6;pointerEvents=1;points=[];fillColor=none;resizable=0;rotatable=0;perimeter=centerPerimeter;snapToPoint=1;strokeWidth=5;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1461&quot; y=&quot;320&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-27&quot; value=&quot;&amp;lt;div style=&amp;quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 10px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&amp;quot;&amp;gt;GET&amp;lt;br&amp;gt;POST&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=entityRelationEdgeStyle;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-6&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-26&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.7733&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-28&quot; value=&quot;&amp;lt;div style=&amp;quot;font-size: 10px;&amp;quot;&amp;gt;GET&amp;lt;br&amp;gt;DELETE&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=entityRelationEdgeStyle;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-7&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-26&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.7563&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1774.6666666666667&quot; y=&quot;384.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-29&quot; value=&quot;&amp;lt;div style=&amp;quot;font-size: 10px;&amp;quot;&amp;gt;&amp;lt;div&amp;gt;GET&amp;lt;br&amp;gt;POST&amp;lt;br&amp;gt;PATCH&amp;lt;br&amp;gt;DELETE&amp;lt;/div&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;&quot; parent=&quot;1&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.7496&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1446&quot; y=&quot;330.0000000000001&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-1601.999999999999&quot; y=&quot;303&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;-1646&quot; y=&quot;302&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-30&quot; value=&quot;&amp;lt;div style=&amp;quot;font-size: 10px;&amp;quot;&amp;gt;GET&amp;lt;br&amp;gt;POST&amp;lt;br&amp;gt;PATCH&amp;lt;br&amp;gt;DELETE&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=entityRelationEdgeStyle;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-9&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-26&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.6562&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;            &lt;mxPoint x=&quot;-1813&quot; y=&quot;384.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-31&quot; value=&quot;&amp;lt;div style=&amp;quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 10px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&amp;quot;&amp;gt;GET&amp;lt;br&amp;gt;POST&amp;lt;/div&amp;gt;&quot; style=&quot;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=entityRelationEdgeStyle;startSize=6;jumpSize=10;endSize=6;strokeWidth=1;jumpStyle=none;segment=30;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-10&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-26&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.7511&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-1513&quot; y=&quot;474.5&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;-1624&quot; y=&quot;413&quot; /&gt;&#10;            &lt;/Array&gt;&#10;            &lt;mxPoint as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-32&quot; value=&quot;&quot; style=&quot;shape=mxgraph.azure.azure_alert;fillColor=#FFAB00;strokeColor=none;fontColor=#172B4C;fontSize=18;fontStyle=1;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;html=1;spacingLeft=5;sketch=0;hachureGap=4;pointerEvents=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1904&quot; y=&quot;417.5&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-4&quot; y=&quot;-119&quot; as=&quot;offset&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-33&quot; style=&quot;edgeStyle=entityRelationEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;rUzoXmEEmOzYYHfq9tto-34&quot; target=&quot;rUzoXmEEmOzYYHfq9tto-15&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-34&quot; value=&quot;&quot; style=&quot;shape=waypoint;sketch=0;fillStyle=solid;size=6;pointerEvents=1;points=[];fillColor=none;resizable=0;rotatable=0;perimeter=centerPerimeter;snapToPoint=1;strokeWidth=5;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1461&quot; y=&quot;64&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-35&quot; value=&quot;&quot; style=&quot;shape=waypoint;sketch=0;fillStyle=solid;size=6;pointerEvents=1;points=[];fillColor=none;resizable=0;rotatable=0;perimeter=centerPerimeter;snapToPoint=1;strokeWidth=5;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1258&quot; y=&quot;606&quot; width=&quot;20&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-36&quot; value=&quot;Black dot does not represent any proxy/infra&amp;lt;br&amp;gt;Just used to declutter diagram&quot; style=&quot;text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1232&quot; y=&quot;595.5&quot; width=&quot;253&quot; height=&quot;41&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rUzoXmEEmOzYYHfq9tto-37&quot; value=&quot;&amp;lt;h1 style=&amp;quot;margin-top: 0px;&amp;quot;&amp;gt;Legend&amp;lt;/h1&amp;gt;&quot; style=&quot;text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1261&quot; y=&quot;430&quot; width=&quot;103&quot; height=&quot;57&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;PmWjxPVjIVMpeAsW6TeR-2&quot; value=&quot;Not Implemented&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;sketch=1;curveFitting=1;jiggle=2;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1261&quot; y=&quot;553.25&quot; width=&quot;110&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="rUzoXmEEmOzYYHfq9tto-38"><g><rect x="0" y="0" width="986" height="703" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/></g></g><g data-cell-id="uFo9A05xg_FpJxoeQmF--1"><g><path d="M 616 264 L 776 264 L 776 378 L 616 378 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 616 264 L 616 289 L 641 289 L 641 264 L 616 264 Z M 620.09 267.69 L 636.91 267.69 C 637.01 267.69 637.12 267.73 637.19 267.81 C 637.27 267.88 637.31 267.99 637.31 268.09 L 637.31 284.91 C 637.31 285.01 637.27 285.12 637.19 285.19 C 637.12 285.27 637.01 285.31 636.91 285.31 L 620.09 285.31 C 619.99 285.31 619.88 285.27 619.81 285.19 C 619.73 285.12 619.69 285.01 619.69 284.91 L 619.69 268.09 C 619.69 267.99 619.73 267.88 619.81 267.81 C 619.88 267.73 619.99 267.69 620.09 267.69 Z M 620.49 268.49 L 620.49 284.51 L 636.51 284.51 L 636.51 268.49 L 620.49 268.49 Z M 632.5 269.31 C 632.65 269.31 632.79 269.38 632.86 269.51 L 635.46 274.72 C 635.53 274.84 635.52 274.99 635.45 275.11 C 635.37 275.23 635.25 275.3 635.11 275.3 L 629.9 275.3 L 629.9 275.3 C 629.76 275.3 629.63 275.23 629.56 275.11 C 629.49 274.99 629.48 274.84 629.54 274.72 L 632.14 269.51 C 632.21 269.38 632.36 269.31 632.5 269.31 Z M 632.5 270.59 L 630.55 274.5 L 634.46 274.5 L 632.5 270.59 Z M 622.09 273.3 L 627.3 273.3 C 627.41 273.3 627.51 273.34 627.58 273.41 C 627.66 273.49 627.7 273.59 627.7 273.7 L 627.7 278.9 C 627.7 279.01 627.66 279.11 627.58 279.19 C 627.51 279.26 627.41 279.3 627.3 279.3 L 622.09 279.3 C 621.99 279.3 621.89 279.26 621.81 279.19 C 621.74 279.11 621.69 279.01 621.69 278.9 L 621.69 273.7 C 621.69 273.59 621.74 273.49 621.81 273.41 C 621.89 273.34 621.99 273.3 622.09 273.3 Z M 622.49 274.1 L 622.49 278.5 L 626.9 278.5 L 626.9 274.1 L 622.49 274.1 Z M 631.7 277.3 C 633.47 277.3 634.9 278.74 634.91 280.5 C 634.9 282.27 633.47 283.7 631.7 283.71 C 629.94 283.7 628.5 282.27 628.5 280.5 C 628.5 278.74 629.94 277.3 631.7 277.3 Z M 631.7 278.1 C 630.38 278.1 629.3 279.18 629.3 280.5 C 629.3 281.83 630.38 282.9 631.7 282.91 C 633.03 282.9 634.1 281.83 634.1 280.5 C 634.1 279.18 633.03 278.1 631.7 278.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 271px; margin-left: 648px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: <br />saas-ingestion</div></div></div></foreignObject><text x="648" y="283" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account:...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-1"><g><rect x="40" y="412" width="141" height="36" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 139px; height: 1px; padding-top: 430px; margin-left: 42px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Applicace limited to <br />AWS and O365 Connectors Only</div></div></div></foreignObject><text x="42" y="433" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="10px">Applicace limited to...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-2"><g><path d="M 272 480 L 470 480 L 470 611 L 272 611 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 272 480 L 272 505 L 297 505 L 297 480 L 272 480 Z M 276.09 483.69 L 292.91 483.69 C 293.01 483.69 293.12 483.73 293.19 483.81 C 293.27 483.88 293.31 483.99 293.31 484.09 L 293.31 500.91 C 293.31 501.01 293.27 501.12 293.19 501.19 C 293.12 501.27 293.01 501.31 292.91 501.31 L 276.09 501.31 C 275.99 501.31 275.88 501.27 275.81 501.19 C 275.73 501.12 275.69 501.01 275.69 500.91 L 275.69 484.09 C 275.69 483.99 275.73 483.88 275.81 483.81 C 275.88 483.73 275.99 483.69 276.09 483.69 Z M 276.49 484.49 L 276.49 500.51 L 292.51 500.51 L 292.51 484.49 L 276.49 484.49 Z M 288.5 485.31 C 288.65 485.31 288.79 485.38 288.86 485.51 L 291.46 490.72 C 291.53 490.84 291.52 490.99 291.45 491.11 C 291.37 491.23 291.25 491.3 291.11 491.3 L 285.9 491.3 L 285.9 491.3 C 285.76 491.3 285.63 491.23 285.56 491.11 C 285.49 490.99 285.48 490.84 285.54 490.72 L 288.14 485.51 C 288.21 485.38 288.36 485.31 288.5 485.31 Z M 288.5 486.59 L 286.55 490.5 L 290.46 490.5 L 288.5 486.59 Z M 278.09 489.3 L 283.3 489.3 C 283.41 489.3 283.51 489.34 283.58 489.41 C 283.66 489.49 283.7 489.59 283.7 489.7 L 283.7 494.9 C 283.7 495.01 283.66 495.11 283.58 495.19 C 283.51 495.26 283.41 495.3 283.3 495.3 L 278.09 495.3 C 277.99 495.3 277.89 495.26 277.81 495.19 C 277.74 495.11 277.69 495.01 277.69 494.9 L 277.69 489.7 C 277.69 489.59 277.74 489.49 277.81 489.41 C 277.89 489.34 277.99 489.3 278.09 489.3 Z M 278.49 490.1 L 278.49 494.5 L 282.9 494.5 L 282.9 490.1 L 278.49 490.1 Z M 287.7 493.3 C 289.47 493.3 290.9 494.74 290.91 496.5 C 290.9 498.27 289.47 499.7 287.7 499.71 C 285.94 499.7 284.5 498.27 284.5 496.5 C 284.5 494.74 285.94 493.3 287.7 493.3 Z M 287.7 494.1 C 286.38 494.1 285.3 495.18 285.3 496.5 C 285.3 497.83 286.38 498.9 287.7 498.91 C 289.03 498.9 290.1 497.83 290.1 496.5 C 290.1 495.18 289.03 494.1 287.7 494.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 166px; height: 1px; padding-top: 487px; margin-left: 304px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: saas-dataeng</div></div></div></foreignObject><text x="304" y="499" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account: saas-dataeng</text></switch></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-3"><g><rect x="306" y="518" width="65" height="26" fill="none" stroke="none" pointer-events="all"/><path d="M 306.28 517.68 C 306.28 517.68 306.28 517.68 306.28 517.68 M 306.28 517.68 C 306.28 517.68 306.28 517.68 306.28 517.68 M 306.02 524.08 C 308.1 522.59 307.85 520.57 311.27 518.04 M 306.02 524.08 C 307.63 522.14 307.79 520.81 311.27 518.04 M 305.76 530.47 C 309.86 526.45 315.06 524.1 316.91 517.64 M 305.76 530.47 C 307.39 527.87 313.41 523.43 316.91 517.64 M 306.15 536.12 C 310.55 531.42 318.64 527.97 321.9 518 M 306.15 536.12 C 308.93 531.49 314.26 525.89 321.9 518 M 305.89 542.51 C 308 539.55 316.63 531.29 326.88 518.36 M 305.89 542.51 C 309.8 537.07 315.4 530.44 326.88 518.36 M 309.56 544.38 C 320.63 536.7 325.63 525.95 332.53 517.97 M 309.56 544.38 C 315.06 535.88 324.01 529.44 332.53 517.97 M 314.55 544.74 C 320.26 539.61 326.68 527.32 337.51 518.33 M 314.55 544.74 C 318.31 540.07 325.63 530.6 337.51 518.33 M 320.19 544.35 C 322.3 534.74 329.87 534.29 343.16 517.93 M 320.19 544.35 C 328.26 534.45 334.28 525.25 343.16 517.93 M 325.18 544.71 C 336 534.21 342.94 530.53 348.14 518.29 M 325.18 544.71 C 332.74 535.28 338.99 526.04 348.14 518.29 M 330.83 544.32 C 338.82 538.9 350.01 523.35 353.79 517.9 M 330.83 544.32 C 335.81 539.87 341.88 531.32 353.79 517.9 M 335.81 544.68 C 345.29 541.41 347.61 529.69 358.77 518.26 M 335.81 544.68 C 343.96 534 351.18 526.39 358.77 518.26 M 341.46 544.28 C 345.06 536.39 350.55 526.7 364.42 517.87 M 341.46 544.28 C 348.9 535.95 354.14 528.95 364.42 517.87 M 346.44 544.64 C 351.47 536.57 360.78 525.26 369.4 518.23 M 346.44 544.64 C 353.02 536.44 362.53 523.24 369.4 518.23 M 352.09 544.25 C 356.05 540.52 358.16 532.16 371.77 521.6 M 352.09 544.25 C 358.81 537.93 365.36 529.9 371.77 521.6 M 357.07 544.61 C 358.12 544.72 362.04 537.7 371.51 528 M 357.07 544.61 C 361.82 538.99 363.8 535.57 371.51 528 M 362.06 544.97 C 364.59 540.07 370.48 539.79 371.9 533.65 M 362.06 544.97 C 363.34 543.23 365.2 539.48 371.9 533.65 M 367.7 544.57 C 369.86 543.04 371 540.85 371.64 540.04 M 367.7 544.57 C 368.7 543.41 369.67 542.31 371.64 540.04" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 306 518 C 325.05 521.73 342.29 517.68 371 518 M 306 518 C 324.68 518.78 346.67 519.48 371 518 M 371 518 C 374.51 530.28 369.66 540.6 371 544 M 371 518 C 371.08 522.12 369.6 528.27 371 544 M 371 544 C 349.87 544.9 335.3 542.06 306 544 M 371 544 C 349.97 544.38 326.77 542.56 306 544 M 306 544 C 307.75 536.9 306.85 527.24 306 518 M 306 544 C 305.07 534.39 306.95 523.29 306 518" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 531px; margin-left: 307px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Data API</div></div></div></foreignObject><text x="339" y="535" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Data API</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-4"><g><rect x="306" y="568" width="71" height="26" fill="none" stroke="none" pointer-events="all"/><path d="M 306.11 567.88 C 306.11 567.88 306.11 567.88 306.11 567.88 M 306.11 567.88 C 306.11 567.88 306.11 567.88 306.11 567.88 M 305.84 574.28 C 307.3 570.6 309.99 568.14 311.09 568.24 M 305.84 574.28 C 308.26 571.42 309.55 569.52 311.09 568.24 M 306.24 579.92 C 310.98 578.28 313.81 573.38 316.74 567.84 M 306.24 579.92 C 311.76 576.61 314.37 570.66 316.74 567.84 M 305.98 586.32 C 311.96 579.23 317.33 579.62 321.72 568.2 M 305.98 586.32 C 310.24 579.85 314.37 575.94 321.72 568.2 M 305.72 592.71 C 314.21 587.56 323.01 572.01 327.37 567.81 M 305.72 592.71 C 314.31 583.45 323.16 574.36 327.37 567.81 M 309.39 594.58 C 314.55 583.21 324.86 578.28 332.35 568.17 M 309.39 594.58 C 319.13 583.51 323.99 576.98 332.35 568.17 M 314.38 594.94 C 323.44 585.62 324.52 579.34 338 567.77 M 314.38 594.94 C 322.78 586.04 331.18 578 338 567.77 M 320.02 594.55 C 330.23 588.09 334.32 580.45 342.98 568.13 M 320.02 594.55 C 324.88 587.55 333.78 579.34 342.98 568.13 M 325.01 594.91 C 335.33 586.34 344.59 572.45 348.63 567.74 M 325.01 594.91 C 332.49 582.4 344.89 573.38 348.63 567.74 M 330.65 594.51 C 339.4 583.64 341.96 577.78 353.61 568.1 M 330.65 594.51 C 336.66 590.01 341.65 581.9 353.61 568.1 M 335.64 594.87 C 343.26 583.02 347.49 578.38 359.26 567.71 M 335.64 594.87 C 341.05 587.47 346.4 582.54 359.26 567.71 M 341.28 594.48 C 346.97 590.98 353.99 585.51 364.24 568.07 M 341.28 594.48 C 351.83 585.31 358.42 575.39 364.24 568.07 M 346.27 594.84 C 354.91 589.25 363.89 581.93 369.89 567.67 M 346.27 594.84 C 355.31 583.83 366.48 572.47 369.89 567.67 M 351.91 594.45 C 355.59 589.93 359.09 584.02 374.87 568.03 M 351.91 594.45 C 358.36 589.94 362.1 582.65 374.87 568.03 M 356.9 594.81 C 360.79 588.83 363.65 582.88 377.24 571.41 M 356.9 594.81 C 361.92 587 367.07 580.38 377.24 571.41 M 362.54 594.41 C 365.02 586.84 365.13 583.12 377.63 577.05 M 362.54 594.41 C 367.06 587.74 373.11 583.1 377.63 577.05 M 367.53 594.77 C 369.63 588.62 375.51 585.53 377.37 583.45 M 367.53 594.77 C 370.78 590.96 376.22 586.66 377.37 583.45 M 373.17 594.38 C 373.98 591.99 374.4 591.62 377.11 589.85 M 373.17 594.38 C 373.79 592.66 374.58 592.54 377.11 589.85" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 306 568 C 324.27 570.77 340.67 567.11 377 568 M 306 568 C 334.06 568.34 362.18 567.47 377 568 M 377 568 C 380.43 577.54 381.3 586.1 377 594 M 377 568 C 375.63 576.21 375.95 586.49 377 594 M 377 594 C 363.72 597.38 346.18 597.54 306 594 M 377 594 C 349.52 594.91 323.42 594.86 306 594 M 306 594 C 304.09 588.4 304.25 583.9 306 568 M 306 594 C 306.91 585.49 305.11 575.33 306 568" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 69px; height: 1px; padding-top: 581px; margin-left: 307px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Jetstream</div></div></div></foreignObject><text x="342" y="585" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Jetstream</text></switch></g></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-5"><g><path d="M 193 123 L 470 123 L 470 464 L 193 464 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 193 123 L 193 148 L 218 148 L 218 123 L 193 123 Z M 197.09 126.69 L 213.91 126.69 C 214.01 126.69 214.12 126.73 214.19 126.81 C 214.27 126.88 214.31 126.99 214.31 127.09 L 214.31 143.91 C 214.31 144.01 214.27 144.12 214.19 144.19 C 214.12 144.27 214.01 144.31 213.91 144.31 L 197.09 144.31 C 196.99 144.31 196.88 144.27 196.81 144.19 C 196.73 144.12 196.69 144.01 196.69 143.91 L 196.69 127.09 C 196.69 126.99 196.73 126.88 196.81 126.81 C 196.88 126.73 196.99 126.69 197.09 126.69 Z M 197.49 127.49 L 197.49 143.51 L 213.51 143.51 L 213.51 127.49 L 197.49 127.49 Z M 209.5 128.31 C 209.65 128.31 209.79 128.38 209.86 128.51 L 212.46 133.72 C 212.53 133.84 212.52 133.99 212.45 134.11 C 212.37 134.23 212.25 134.3 212.11 134.3 L 206.9 134.3 L 206.9 134.3 C 206.76 134.3 206.63 134.23 206.56 134.11 C 206.49 133.99 206.48 133.84 206.54 133.72 L 209.15 128.51 C 209.21 128.38 209.36 128.31 209.5 128.31 Z M 209.5 129.59 L 207.55 133.5 L 211.46 133.5 L 209.5 129.59 Z M 199.09 132.3 L 204.3 132.3 C 204.41 132.3 204.51 132.34 204.58 132.41 C 204.66 132.49 204.7 132.59 204.7 132.7 L 204.7 137.9 C 204.7 138.01 204.66 138.11 204.58 138.19 C 204.51 138.26 204.41 138.3 204.3 138.3 L 199.09 138.3 C 198.99 138.3 198.89 138.26 198.81 138.19 C 198.74 138.11 198.69 138.01 198.69 137.9 L 198.69 132.7 C 198.69 132.59 198.74 132.49 198.81 132.41 C 198.89 132.34 198.99 132.3 199.09 132.3 Z M 199.49 133.1 L 199.49 137.5 L 203.9 137.5 L 203.9 133.1 L 199.49 133.1 Z M 208.7 136.3 C 210.47 136.3 211.9 137.74 211.91 139.5 C 211.9 141.27 210.47 142.7 208.7 142.71 C 206.94 142.7 205.5 141.27 205.5 139.5 C 205.5 137.74 206.94 136.3 208.7 136.3 Z M 208.7 137.1 C 207.38 137.1 206.3 138.18 206.3 139.5 C 206.3 140.83 207.38 141.9 208.7 141.91 C 210.03 141.9 211.1 140.83 211.1 139.5 C 211.1 138.18 210.03 137.1 208.7 137.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 245px; height: 1px; padding-top: 130px; margin-left: 225px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: saas-app</div></div></div></foreignObject><text x="225" y="142" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account: saas-app</text></switch></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-6"><g><rect x="250" y="160" width="63" height="26" fill="none" stroke="none" transform="translate(0,173)scale(1,-1)translate(0,-173)" pointer-events="all"/><path d="M 249.99 160.02 C 249.99 160.02 249.99 160.02 249.99 160.02 M 249.99 160.02 C 249.99 160.02 249.99 160.02 249.99 160.02 M 249.73 166.41 C 251.26 164.18 254.55 163.26 254.97 160.38 M 249.73 166.41 C 251.66 164.33 253.1 162.11 254.97 160.38 M 250.12 172.06 C 256.14 168.34 254.85 164.44 260.62 159.98 M 250.12 172.06 C 253 168.25 254.91 166.78 260.62 159.98 M 249.86 178.45 C 252.73 170.96 255.79 167.41 265.6 160.34 M 249.86 178.45 C 257.09 171.67 262.81 163.16 265.6 160.34 M 250.25 184.1 C 257.64 172.18 269.89 163.65 271.25 159.95 M 250.25 184.1 C 255.05 174.59 261.28 167.09 271.25 159.95 M 253.27 186.72 C 264.73 175.06 269.67 165.78 276.23 160.31 M 253.27 186.72 C 259.97 179.79 264.06 174.38 276.23 160.31 M 258.91 186.33 C 269.75 180.8 272.31 165.74 281.88 159.91 M 258.91 186.33 C 267.38 178.07 276.69 166.95 281.88 159.91 M 263.9 186.69 C 268.66 182.89 277.21 174.15 286.86 160.27 M 263.9 186.69 C 272.01 176.2 279.26 168.67 286.86 160.27 M 269.54 186.29 C 273.55 177.85 282.1 173.69 292.51 159.88 M 269.54 186.29 C 276.57 178.72 283.94 168.3 292.51 159.88 M 274.53 186.65 C 280.31 175.56 294.22 167.82 297.49 160.24 M 274.53 186.65 C 278.59 182.01 285.41 174.79 297.49 160.24 M 280.18 186.26 C 285.8 179.24 288.61 170.79 303.14 159.84 M 280.18 186.26 C 284.92 180.2 291.3 170.53 303.14 159.84 M 285.16 186.62 C 289.5 176.78 296.17 174.46 308.12 160.2 M 285.16 186.62 C 291.68 181.41 295.78 172.21 308.12 160.2 M 290.15 186.98 C 298.49 178.1 306.21 171.44 313.11 160.56 M 290.15 186.98 C 298.29 175.19 308.02 168.38 313.11 160.56 M 295.79 186.58 C 296.33 183.51 299.91 177.6 313.51 166.21 M 295.79 186.58 C 302.36 179.05 309.23 172.4 313.51 166.21 M 300.78 186.94 C 304.7 181.29 308.78 182.52 313.24 172.6 M 300.78 186.94 C 304.03 183.72 309.85 177.21 313.24 172.6 M 306.42 186.55 C 309.42 183.43 311.97 179.54 313.64 178.25 M 306.42 186.55 C 307.06 185.27 309.69 181.48 313.64 178.25 M 311.41 186.91 C 312.42 186.4 312.03 186 313.38 184.64 M 311.41 186.91 C 311.79 186.31 312.48 185.28 313.38 184.64" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,173)scale(1,-1)translate(0,-173)" pointer-events="all"/><path d="M 250 160 C 269.69 158.02 279.37 163.16 313 160 M 250 160 C 272.24 158.3 292.07 158.31 313 160 M 313 160 C 314.17 166.86 310.48 174.72 313 186 M 313 160 C 311.69 170 311.59 178.11 313 186 M 313 186 C 291.44 186.37 267.96 184.52 250 186 M 313 186 C 296.97 187.99 281.45 187.49 250 186 M 250 186 C 250.85 178.2 253.14 170.81 250 160 M 250 186 C 249.54 179.68 248.41 171.41 250 160" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,173)scale(1,-1)translate(0,-173)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 173px; margin-left: 251px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Consent</div></div></div></foreignObject><text x="282" y="177" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Consent</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-7"><g><rect x="206" y="225" width="107" height="26" fill="none" stroke="none" transform="translate(0,238)scale(1,-1)translate(0,-238)" pointer-events="all"/><path d="M 205.94 225.07 C 205.94 225.07 205.94 225.07 205.94 225.07 M 205.94 225.07 C 205.94 225.07 205.94 225.07 205.94 225.07 M 205.67 231.47 C 209.12 227.85 207.75 227.69 211.58 224.68 M 205.67 231.47 C 207.84 229.69 209.55 227.9 211.58 224.68 M 206.07 237.11 C 212.97 235.55 216.01 228.95 216.57 225.04 M 206.07 237.11 C 209.73 232.46 211.79 229.21 216.57 225.04 M 205.81 243.51 C 210.42 241.48 210.89 233.62 222.21 224.64 M 205.81 243.51 C 207.51 238.34 213.02 234.99 222.21 224.64 M 206.2 249.16 C 214.57 239.3 214.56 236.57 227.2 225 M 206.2 249.16 C 212.2 240.95 217.24 236.11 227.2 225 M 209.22 251.78 C 215.83 243.51 226.08 232.06 232.18 225.36 M 209.22 251.78 C 216.17 242.28 224.16 236.78 232.18 225.36 M 214.86 251.39 C 220.9 242.76 225.97 233.88 237.83 224.97 M 214.86 251.39 C 219.16 244.43 224.75 236.28 237.83 224.97 M 219.85 251.75 C 223.66 243.09 236.73 235.16 242.81 225.33 M 219.85 251.75 C 228.76 244.16 234.88 233.62 242.81 225.33 M 225.49 251.35 C 236.75 245.12 239.38 231.03 248.46 224.94 M 225.49 251.35 C 232.19 240.99 241.46 231.09 248.46 224.94 M 230.48 251.71 C 235.96 243.16 241.24 237.12 253.44 225.3 M 230.48 251.71 C 235.56 247.01 241.31 240.22 253.44 225.3 M 236.12 251.32 C 239.67 243.96 252.18 234.83 259.09 224.9 M 236.12 251.32 C 242.6 243.39 251.56 235.28 259.09 224.9 M 241.11 251.68 C 246.48 245.31 254.68 239.21 264.07 225.26 M 241.11 251.68 C 249.68 241.63 259.13 233.5 264.07 225.26 M 246.75 251.28 C 250.51 244.19 265.71 233.74 269.72 224.87 M 246.75 251.28 C 251.46 246.24 258 242.85 269.72 224.87 M 251.74 251.64 C 261.46 247.26 266.93 243.22 274.7 225.23 M 251.74 251.64 C 258.7 244.37 263.39 237.74 274.7 225.23 M 257.38 251.25 C 265.29 245.27 270.22 237.7 280.35 224.83 M 257.38 251.25 C 265.64 244.15 272.25 236.58 280.35 224.83 M 262.37 251.61 C 269.96 242.41 277.01 237.48 285.33 225.19 M 262.37 251.61 C 268.95 242.06 275.79 236.66 285.33 225.19 M 267.36 251.97 C 273.11 241.7 285.85 238.42 290.98 224.8 M 267.36 251.97 C 275.11 242.04 287.76 232.5 290.98 224.8 M 273 251.57 C 279.01 241.53 291.67 229.38 295.96 225.16 M 273 251.57 C 280.75 239.94 289.51 232.96 295.96 225.16 M 277.99 251.93 C 285.39 245.9 288.13 241.26 301.61 224.76 M 277.99 251.93 C 286.73 242.27 292.74 233.74 301.61 224.76 M 283.63 251.54 C 294.24 240.59 299.13 229.08 306.59 225.12 M 283.63 251.54 C 290.45 245.11 294.7 238.51 306.59 225.12 M 288.62 251.9 C 296.69 249.59 301.7 243.62 312.24 224.73 M 288.62 251.9 C 296.09 241.48 306.04 230.82 312.24 224.73 M 294.26 251.5 C 296.25 248.7 302.61 236.16 313.29 229.62 M 294.26 251.5 C 297.88 249.15 301.22 243.49 313.29 229.62 M 299.25 251.86 C 300.93 244.28 305.24 241.23 313.03 236.01 M 299.25 251.86 C 303.99 245.52 309.25 239.31 313.03 236.01 M 304.89 251.47 C 310.1 245.3 313.43 245.34 313.42 241.66 M 304.89 251.47 C 309.31 247.83 311.81 245.05 313.42 241.66 M 309.88 251.83 C 311.49 250.02 311.68 250.04 313.16 248.06 M 309.88 251.83 C 310.65 250.47 311.6 248.88 313.16 248.06" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,238)scale(1,-1)translate(0,-238)" pointer-events="all"/><path d="M 206 225 C 234.48 226.56 260.88 224.09 313 225 M 206 225 C 227.37 222.55 247.09 224.99 313 225 M 313 225 C 314.08 229.12 316.12 243.22 313 251 M 313 225 C 314.23 233.9 311.93 240.93 313 251 M 313 251 C 278.42 254.27 247.11 255.42 206 251 M 313 251 C 279.56 253.96 250.19 251.24 206 251 M 206 251 C 203.19 244.69 206.54 234.47 206 225 M 206 251 C 206.33 245.78 205.53 242.45 206 225" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,238)scale(1,-1)translate(0,-238)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 105px; height: 1px; padding-top: 238px; margin-left: 207px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">VUI Provisioning</div></div></div></foreignObject><text x="260" y="242" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">VUI Provisioning</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-8"><g><rect x="243" y="290" width="70" height="26" fill="none" stroke="none" transform="translate(0,303)scale(1,-1)translate(0,-303)" pointer-events="all"/><path d="M 242.79 290.24 C 242.79 290.24 242.79 290.24 242.79 290.24 M 242.79 290.24 C 242.79 290.24 242.79 290.24 242.79 290.24 M 243.19 295.88 C 244.57 295.09 245.25 292.52 248.44 289.84 M 243.19 295.88 C 244.87 294.79 244.87 293.84 248.44 289.84 M 242.93 302.28 C 244.3 296.47 251.69 293.58 253.42 290.2 M 242.93 302.28 C 245.27 299.18 248.57 296.58 253.42 290.2 M 243.32 307.92 C 251.8 300.02 252.56 292.53 259.07 289.81 M 243.32 307.92 C 246.38 305.08 250.54 299.58 259.07 289.81 M 243.06 314.32 C 245.38 307.37 249.11 302.43 264.05 290.17 M 243.06 314.32 C 249.28 308.27 253.13 302.08 264.05 290.17 M 246.08 316.94 C 256.06 303.8 263.85 297.93 269.7 289.77 M 246.08 316.94 C 253.5 308.61 261.64 298.75 269.7 289.77 M 251.72 316.55 C 259.9 312.84 259.48 302.13 274.68 290.13 M 251.72 316.55 C 260.45 305.62 266.9 299.15 274.68 290.13 M 256.71 316.91 C 267.8 303.11 277.66 295.69 280.33 289.74 M 256.71 316.91 C 262.57 308.03 267.74 302.28 280.33 289.74 M 262.35 316.52 C 268.28 308.85 268.38 306.13 285.31 290.1 M 262.35 316.52 C 269.17 308.66 275.76 304.56 285.31 290.1 M 267.34 316.88 C 273.74 310.67 278.58 306.12 290.96 289.71 M 267.34 316.88 C 273.6 311.94 278.42 305.43 290.96 289.71 M 272.98 316.48 C 282.46 308.78 288.66 298.98 295.94 290.07 M 272.98 316.48 C 281.19 306.7 288.74 296.13 295.94 290.07 M 277.97 316.84 C 290.26 308.39 296.81 300.95 301.59 289.67 M 277.97 316.84 C 285.26 307.02 291.65 301.03 301.59 289.67 M 283.61 316.45 C 290.79 304.06 297.46 297.83 306.57 290.03 M 283.61 316.45 C 289.47 312.88 293.42 307.61 306.57 290.03 M 288.6 316.81 C 297.48 311.36 303.77 302.42 312.22 289.64 M 288.6 316.81 C 299.06 306.13 308.7 295.91 312.22 289.64 M 294.24 316.41 C 301.66 306.31 303.07 302.64 313.27 294.53 M 294.24 316.41 C 300.07 309.59 307.04 302.07 313.27 294.53 M 299.23 316.77 C 305.63 314.72 312.8 306.58 313.01 300.92 M 299.23 316.77 C 304.49 310.79 311.47 305.69 313.01 300.92 M 304.87 316.38 C 308.75 315.31 312.31 307.36 313.4 306.57 M 304.87 316.38 C 308.33 314.51 309.65 311.91 313.4 306.57 M 309.86 316.74 C 310.97 316.4 311.07 314.65 313.14 312.96 M 309.86 316.74 C 311.1 315.24 312.09 314.24 313.14 312.96" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,303)scale(1,-1)translate(0,-303)" pointer-events="all"/><path d="M 243 290 C 259.88 291.34 274.61 289.27 313 290 M 243 290 C 260.4 290.28 280.91 291.16 313 290 M 313 290 C 312.96 299.38 312.71 303.73 313 316 M 313 290 C 312.78 297.79 312.27 303.74 313 316 M 313 316 C 291.86 313.43 271.15 317.59 243 316 M 313 316 C 290.38 315.15 263.4 318.21 243 316 M 243 316 C 245.58 311.19 241.98 306.12 243 290 M 243 316 C 245.16 306.67 244.7 299.08 243 290" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,303)scale(1,-1)translate(0,-303)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 303px; margin-left: 244px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">VUI cloud</div></div></div></foreignObject><text x="278" y="307" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">VUI cloud</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-9"><g><rect x="223" y="354" width="90" height="26" fill="none" stroke="none" transform="translate(0,367)scale(1,-1)translate(0,-367)" pointer-events="all"/><path d="M 223.07 353.92 C 223.07 353.92 223.07 353.92 223.07 353.92 M 223.07 353.92 C 223.07 353.92 223.07 353.92 223.07 353.92 M 222.81 360.32 C 222.51 358.54 225.09 356.74 228.06 354.28 M 222.81 360.32 C 225.3 358.3 226.42 355.18 228.06 354.28 M 223.2 365.96 C 225.46 362.31 230.78 356.72 233.7 353.88 M 223.2 365.96 C 226.33 361.59 229.78 357.21 233.7 353.88 M 222.94 372.36 C 224.29 370.06 230.32 366.42 238.69 354.24 M 222.94 372.36 C 227.48 367.67 230.44 363.88 238.69 354.24 M 222.68 378.76 C 230.38 369.64 234.25 365.43 244.33 353.85 M 222.68 378.76 C 225.27 373.85 232.07 370.16 244.33 353.85 M 226.36 380.63 C 230.2 372.06 243.09 364.26 249.32 354.21 M 226.36 380.63 C 233.8 369.99 245.61 360.31 249.32 354.21 M 231.34 380.99 C 239.56 367.69 246.5 357.58 254.96 353.82 M 231.34 380.99 C 236.08 374.22 242.99 366.52 254.96 353.82 M 236.99 380.59 C 243.34 375.52 252.88 366.49 259.95 354.18 M 236.99 380.59 C 245.14 369.52 253.61 359.66 259.95 354.18 M 241.97 380.95 C 248.41 374.36 258.76 369.51 265.59 353.78 M 241.97 380.95 C 248.66 370.06 257.37 362.24 265.59 353.78 M 247.62 380.56 C 253.53 377.08 257.57 366.43 270.58 354.14 M 247.62 380.56 C 254.74 375.72 258.33 369.82 270.58 354.14 M 252.6 380.92 C 255.61 377.96 267.09 364.57 276.22 353.75 M 252.6 380.92 C 259.16 374.39 264.83 370.51 276.22 353.75 M 258.25 380.52 C 263.32 376.74 271.17 365.79 281.21 354.11 M 258.25 380.52 C 262.79 375.36 269.74 367.95 281.21 354.11 M 263.23 380.88 C 275.13 370 281.51 359.72 286.85 353.71 M 263.23 380.88 C 267.44 374.64 272.94 371.34 286.85 353.71 M 268.88 380.49 C 276.58 375.28 283.37 369.81 291.84 354.07 M 268.88 380.49 C 277.71 372.28 283.22 364.33 291.84 354.07 M 273.86 380.85 C 284.61 372.06 293.42 360.92 297.48 353.68 M 273.86 380.85 C 282.99 371.24 288.97 364.68 297.48 353.68 M 279.51 380.45 C 286.57 375.31 287.66 369.89 302.47 354.04 M 279.51 380.45 C 286.67 375.21 292.29 367.53 302.47 354.04 M 284.49 380.81 C 292.99 376.52 294.28 367.79 308.11 353.64 M 284.49 380.81 C 293.46 372.91 298.83 363 308.11 353.64 M 290.14 380.42 C 298.78 373.08 300.73 364.59 313.1 354 M 290.14 380.42 C 293.57 373.16 298.73 369.28 313.1 354 M 295.12 380.78 C 302.58 378.71 307.08 367.46 312.84 360.4 M 295.12 380.78 C 301.13 374.17 305.53 369.26 312.84 360.4 M 300.77 380.38 C 306.83 376.92 306.1 370.66 313.23 366.04 M 300.77 380.38 C 304.02 375.84 310.36 370.33 313.23 366.04 M 305.75 380.74 C 306.41 376.72 307.25 379.2 312.97 372.44 M 305.75 380.74 C 307.81 376.65 312.26 374.5 312.97 372.44 M 311.4 380.35 C 311.85 380.2 312.68 379.06 313.36 378.09 M 311.4 380.35 C 311.74 379.59 312.43 379.24 313.36 378.09" fill="none" stroke="#dae8fc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,367)scale(1,-1)translate(0,-367)" pointer-events="all"/><path d="M 223 354 C 242.63 354.62 268.04 352.95 313 354 M 223 354 C 254.72 353.99 286.29 353.31 313 354 M 313 354 C 312.87 360.63 310.34 371.23 313 380 M 313 354 C 311.33 360.69 312.61 369.56 313 380 M 313 380 C 288.41 380.28 269.42 379.43 223 380 M 313 380 C 278.31 380.07 242.82 380.9 223 380 M 223 380 C 222.96 371.49 220.42 358.38 223 354 M 223 380 C 221.96 371.77 221.83 365.12 223 354" fill="none" stroke="#6c8ebf" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,367)scale(1,-1)translate(0,-367)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 367px; margin-left: 224px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AuthGateway</div></div></div></foreignObject><text x="268" y="371" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AuthGateway</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-10"><g><rect x="216" y="419" width="97" height="26" fill="none" stroke="none" transform="translate(0,432)scale(1,-1)translate(0,-432)" pointer-events="all"/><path d="M 215.84 419.18 C 215.84 419.18 215.84 419.18 215.84 419.18 M 215.84 419.18 C 215.84 419.18 215.84 419.18 215.84 419.18 M 216.23 424.83 C 216.73 422.66 221.27 421.6 221.48 418.79 M 216.23 424.83 C 218.48 423.39 218.73 420.67 221.48 418.79 M 215.97 431.23 C 216.98 427.65 222.13 421.68 226.47 419.15 M 215.97 431.23 C 218.42 429.79 219.45 425.67 226.47 419.15 M 215.71 437.62 C 220.09 429.88 228.67 429.84 232.11 418.76 M 215.71 437.62 C 220.48 435.33 222.89 427.43 232.11 418.76 M 216.11 443.27 C 225.98 435.29 233.43 427.43 237.1 419.12 M 216.11 443.27 C 219.79 437.9 223.31 431.55 237.1 419.12 M 219.12 445.89 C 222.67 440.93 232.54 433.46 242.74 418.72 M 219.12 445.89 C 227.7 436.38 231.91 431.48 242.74 418.72 M 224.77 445.5 C 233.49 439.5 239.97 431.33 247.73 419.08 M 224.77 445.5 C 228.43 440.14 235.74 432.17 247.73 419.08 M 229.75 445.86 C 233.37 439.1 243.95 433.64 253.37 418.69 M 229.75 445.86 C 236.41 436.6 246.47 428.13 253.37 418.69 M 235.4 445.46 C 242.45 434.82 247.12 430.81 258.36 419.05 M 235.4 445.46 C 244.05 434.72 253.54 424.08 258.36 419.05 M 240.38 445.82 C 243.12 443.42 246.91 430.63 264 418.65 M 240.38 445.82 C 247.18 439.28 250.96 434.08 264 418.65 M 246.03 445.43 C 252.59 441.08 264.56 426.69 268.99 419.01 M 246.03 445.43 C 252.86 439.97 257.86 432.87 268.99 419.01 M 251.01 445.79 C 255.47 435.63 263.53 433.96 273.98 419.37 M 251.01 445.79 C 256.27 438.38 260.34 432.6 273.98 419.37 M 256.66 445.39 C 265.57 441.11 267.89 434.43 279.62 418.98 M 256.66 445.39 C 265.3 437.59 270.6 431.75 279.62 418.98 M 261.65 445.75 C 272.01 436.29 275.74 426.26 284.61 419.34 M 261.65 445.75 C 269.75 438.09 276.46 428.43 284.61 419.34 M 267.29 445.36 C 273.95 435.61 278.68 431.6 290.25 418.94 M 267.29 445.36 C 274.02 437.43 281.7 426.77 290.25 418.94 M 272.28 445.72 C 277.43 436.65 282.54 432.07 295.24 419.3 M 272.28 445.72 C 281.23 436.6 286.19 428.24 295.24 419.3 M 277.92 445.32 C 287.94 439.32 291.7 431.17 300.88 418.91 M 277.92 445.32 C 283.13 439.23 291.14 430.77 300.88 418.91 M 282.91 445.68 C 287.47 441.71 293.82 427.15 305.87 419.27 M 282.91 445.68 C 289.56 435.82 300.43 425.89 305.87 419.27 M 288.55 445.29 C 293.96 432.32 304.41 424.92 311.51 418.87 M 288.55 445.29 C 295.21 441.16 297.62 434.6 311.51 418.87 M 293.54 445.65 C 298.39 436.24 308.47 433.2 313.87 422.25 M 293.54 445.65 C 301.75 439 307.15 429.16 313.87 422.25 M 299.18 445.25 C 302.98 435.58 308.37 436.05 313.61 428.65 M 299.18 445.25 C 301.89 440.06 309.12 434.8 313.61 428.65 M 304.17 445.61 C 306.11 443.99 309.77 437.97 313.35 435.05 M 304.17 445.61 C 304.98 442.34 308.24 440 313.35 435.05 M 309.15 445.97 C 310.55 445.46 310.95 442.95 313.74 440.69 M 309.15 445.97 C 310.57 444.07 312.35 442.01 313.74 440.69" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,432)scale(1,-1)translate(0,-432)" pointer-events="all"/><path d="M 216 419 C 238.93 417.49 254.92 414.11 313 419 M 216 419 C 242.7 420.61 269.42 420.1 313 419 M 313 419 C 314.9 423.67 316.41 433.71 313 445 M 313 419 C 311.65 426.63 312.63 432.91 313 445 M 313 445 C 281.37 442.64 252.83 440.68 216 445 M 313 445 C 280.88 444.68 247.21 443.4 216 445 M 216 445 C 215.74 442.64 213.01 429.78 216 419 M 216 445 C 216.51 435.29 217.85 429.61 216 419" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" transform="translate(0,432)scale(1,-1)translate(0,-432)" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 95px; height: 1px; padding-top: 432px; margin-left: 217px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS <span style="background-color: initial;">Ingestion</span></div></div></div></foreignObject><text x="265" y="436" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">AWS Ingestion</text></switch></g></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-11"><g><path d="M 191 19 L 470 19 L 470 109 L 191 109 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/><path d="M 191 19 L 191 44 L 216 44 L 216 19 L 191 19 Z M 195.09 22.69 L 211.91 22.69 C 212.01 22.69 212.12 22.73 212.19 22.81 C 212.27 22.88 212.31 22.99 212.31 23.09 L 212.31 39.91 C 212.31 40.01 212.27 40.12 212.19 40.19 C 212.12 40.27 212.01 40.31 211.91 40.31 L 195.09 40.31 C 194.99 40.31 194.88 40.27 194.81 40.19 C 194.73 40.12 194.69 40.01 194.69 39.91 L 194.69 23.09 C 194.69 22.99 194.73 22.88 194.81 22.81 C 194.88 22.73 194.99 22.69 195.09 22.69 Z M 195.49 23.49 L 195.49 39.51 L 211.51 39.51 L 211.51 23.49 L 195.49 23.49 Z M 207.5 24.31 C 207.65 24.31 207.79 24.38 207.86 24.52 L 210.46 29.72 C 210.53 29.84 210.52 29.99 210.45 30.11 C 210.37 30.23 210.25 30.3 210.11 30.3 L 204.9 30.3 L 204.9 30.3 C 204.76 30.3 204.63 30.23 204.56 30.11 C 204.49 29.99 204.48 29.84 204.54 29.72 L 207.15 24.52 C 207.21 24.38 207.36 24.31 207.5 24.31 Z M 207.5 25.59 L 205.55 29.5 L 209.46 29.5 L 207.5 25.59 Z M 197.09 28.3 L 202.3 28.3 C 202.41 28.3 202.51 28.34 202.58 28.41 C 202.66 28.49 202.7 28.59 202.7 28.7 L 202.7 33.9 C 202.7 34.01 202.66 34.11 202.58 34.19 C 202.51 34.26 202.41 34.3 202.3 34.3 L 197.09 34.3 C 196.99 34.3 196.89 34.26 196.81 34.19 C 196.74 34.11 196.69 34.01 196.69 33.9 L 196.69 28.7 C 196.69 28.59 196.74 28.49 196.81 28.41 C 196.89 28.34 196.99 28.3 197.09 28.3 Z M 197.49 29.1 L 197.49 33.5 L 201.9 33.5 L 201.9 29.1 L 197.49 29.1 Z M 206.7 32.3 C 208.47 32.3 209.9 33.74 209.91 35.5 C 209.9 37.27 208.47 38.7 206.7 38.71 C 204.94 38.7 203.5 37.27 203.5 35.5 C 203.5 33.74 204.94 32.3 206.7 32.3 Z M 206.7 33.1 C 205.38 33.1 204.3 34.18 204.3 35.5 C 204.3 36.83 205.38 37.9 206.7 37.91 C 208.03 37.9 209.1 36.83 209.1 35.5 C 209.1 34.18 208.03 33.1 206.7 33.1 Z" fill="#666666" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 247px; height: 1px; padding-top: 26px; margin-left: 223px;"><div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">AWS Account: saas-ingestion</div></div></div></foreignObject><text x="223" y="38" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px">AWS Account: saas-ingestion</text></switch></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-12"><g><rect x="255" y="61" width="63" height="26" fill="none" stroke="none" pointer-events="all"/><path d="M 254.99 61.01 C 254.99 61.01 254.99 61.01 254.99 61.01 M 254.99 61.01 C 254.99 61.01 254.99 61.01 254.99 61.01 M 254.73 67.41 C 255.06 66.69 257 63.96 259.98 61.37 M 254.73 67.41 C 256.48 66.75 256.89 65.3 259.98 61.37 M 255.12 73.05 C 257.4 67.39 264.83 62.43 265.62 60.98 M 255.12 73.05 C 257.51 69.12 259.94 67.07 265.62 60.98 M 254.86 79.45 C 255.93 71.5 262.03 67.73 270.61 61.34 M 254.86 79.45 C 258.45 73.64 262.31 69.29 270.61 61.34 M 255.26 85.09 C 260.06 82.09 266.99 77.81 276.25 60.94 M 255.26 85.09 C 264.57 78.35 269.9 68.48 276.25 60.94 M 258.27 87.72 C 259.97 82.64 264.32 79 281.24 61.3 M 258.27 87.72 C 267.16 78.2 275.02 69.32 281.24 61.3 M 263.92 87.32 C 269.98 82.23 273.49 78.4 286.88 60.91 M 263.92 87.32 C 267.85 82.37 276.29 75.06 286.88 60.91 M 268.9 87.68 C 279.52 78.46 283.01 66.79 291.87 61.27 M 268.9 87.68 C 274 80.49 277.59 74.25 291.87 61.27 M 274.55 87.29 C 283.52 77.42 287.74 74.86 297.51 60.87 M 274.55 87.29 C 283 77.18 287.7 68.86 297.51 60.87 M 279.53 87.65 C 287.17 77.21 294.15 70.7 302.5 61.23 M 279.53 87.65 C 284.21 81.16 293.66 73.04 302.5 61.23 M 285.18 87.26 C 289.95 78.58 292.72 76.13 308.14 60.84 M 285.18 87.26 C 291.47 78.07 301.64 70.08 308.14 60.84 M 290.16 87.62 C 301.27 79.12 300.99 72.6 313.13 61.2 M 290.16 87.62 C 295.99 79.81 302.15 73.47 313.13 61.2 M 295.15 87.98 C 303.95 80.18 305.22 73.94 318.11 61.56 M 295.15 87.98 C 302.1 79.11 310.21 73.54 318.11 61.56 M 300.79 87.58 C 307.27 79.65 310.35 70.45 318.51 67.2 M 300.79 87.58 C 307.83 81.33 313.35 72.66 318.51 67.2 M 305.78 87.94 C 311.55 79.84 316.5 76.36 318.25 73.6 M 305.78 87.94 C 309.65 82.96 312.84 77.82 318.25 73.6 M 311.42 87.55 C 314.81 84.43 319.13 81.19 318.64 79.24 M 311.42 87.55 C 313.13 86.37 316.07 84.24 318.64 79.24 M 316.41 87.91 C 316.94 87.08 317.85 86.63 318.38 85.64 M 316.41 87.91 C 317.17 87.59 317.52 86.68 318.38 85.64" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 255 61 C 266.47 65.93 278.87 63.35 318 61 M 255 61 C 269.78 61.65 282.09 62.01 318 61 M 318 61 C 317.65 68.19 314.59 82.72 318 87 M 318 61 C 316.71 66.42 317.27 74.54 318 87 M 318 87 C 300 89.83 287.43 85.87 255 87 M 318 87 C 305.75 87.81 291.09 86.09 255 87 M 255 87 C 256.47 82.44 255.86 66.69 255 61 M 255 87 C 253.1 79.48 255.11 75.69 255 61" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 61px; height: 1px; padding-top: 74px; margin-left: 256px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">LogFlow</div></div></div></foreignObject><text x="287" y="78" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">LogFlow</text></switch></g></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-13"><g><path d="M 107 367 L 216.63 367" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 221.88 367 L 214.88 370.5 L 216.63 367 L 214.88 363.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 367px; margin-left: 144px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div>GET<br />POST<br />PATCH<br />DELETE<br /></div></div></div></div></foreignObject><text x="144" y="370" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-14"><g><rect x="14" y="354" width="93" height="26" fill="none" stroke="none" pointer-events="all"/><path d="M 14.15 353.83 C 14.15 353.83 14.15 353.83 14.15 353.83 M 14.15 353.83 C 14.15 353.83 14.15 353.83 14.15 353.83 M 13.88 360.23 C 15.11 356.54 18.7 354.15 19.13 354.19 M 13.88 360.23 C 16.45 357.54 18.05 356.16 19.13 354.19 M 14.28 365.87 C 16.22 360.54 17.42 359 24.78 353.8 M 14.28 365.87 C 16.13 362.23 19.95 359.04 24.78 353.8 M 14.02 372.27 C 20.7 363.91 24.49 356.2 29.76 354.16 M 14.02 372.27 C 19.86 366.15 25.3 357.19 29.76 354.16 M 13.76 378.67 C 17.84 376.45 20.61 362.75 35.41 353.76 M 13.76 378.67 C 19.71 370.31 26.86 365.53 35.41 353.76 M 17.43 380.54 C 30.02 368.96 33.59 355.69 40.39 354.12 M 17.43 380.54 C 22.22 375.16 29.3 367.39 40.39 354.12 M 22.42 380.9 C 30.61 371.17 35.94 366.16 46.04 353.73 M 22.42 380.9 C 26.79 372.13 36.55 365.26 46.04 353.73 M 28.06 380.5 C 33.23 374.86 41.18 362.09 51.02 354.09 M 28.06 380.5 C 35.35 373.82 41.29 364.27 51.02 354.09 M 33.05 380.86 C 41.85 374.22 50.45 359.53 56.67 353.69 M 33.05 380.86 C 37.47 374.27 45.56 368.07 56.67 353.69 M 38.69 380.47 C 46.77 375.1 44.51 371.99 61.65 354.05 M 38.69 380.47 C 44.35 373.85 51.64 366.61 61.65 354.05 M 43.68 380.83 C 51.5 371.16 57.91 359.08 67.3 353.66 M 43.68 380.83 C 53.69 368.54 61.29 359.36 67.3 353.66 M 49.32 380.43 C 60.13 374.52 67.5 363.15 72.28 354.02 M 49.32 380.43 C 55.71 372.23 63.97 365.33 72.28 354.02 M 54.31 380.79 C 62.12 369.28 70.52 363.26 77.93 353.62 M 54.31 380.79 C 60.81 370.64 67.94 365.09 77.93 353.62 M 59.95 380.4 C 67.35 370.21 81.72 357.32 82.91 353.98 M 59.95 380.4 C 66.16 373.07 69.92 366.05 82.91 353.98 M 64.94 380.76 C 76.8 374.85 80.81 364.27 87.9 354.34 M 64.94 380.76 C 73.01 370.14 82.99 362 87.9 354.34 M 70.58 380.37 C 80.34 367.04 86.11 364.21 93.54 353.95 M 70.58 380.37 C 81.3 370.58 88.42 360.36 93.54 353.95 M 75.57 380.73 C 80.89 373.25 90.45 364.79 98.53 354.31 M 75.57 380.73 C 86.03 372.37 92.18 361.78 98.53 354.31 M 81.21 380.33 C 87.65 375.57 93.18 363.04 104.17 353.92 M 81.21 380.33 C 86.77 373.92 91.04 368.92 104.17 353.92 M 86.2 380.69 C 89.86 375.83 94.18 365.16 107.85 355.79 M 86.2 380.69 C 95.36 373.76 102.37 365.44 107.85 355.79 M 91.84 380.3 C 98.75 374.92 98.31 371.63 107.59 362.18 M 91.84 380.3 C 97.83 372.5 104.36 367.95 107.59 362.18 M 96.83 380.66 C 98.65 374.75 106.94 369.06 107.33 368.58 M 96.83 380.66 C 97.46 377.74 100.61 377.04 107.33 368.58 M 102.47 380.26 C 105.78 376.5 105.39 375.41 107.72 374.22 M 102.47 380.26 C 104.19 378.4 104.79 376.98 107.72 374.22" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 14 354 C 34.98 354.48 48.75 352.69 107 354 M 14 354 C 45.37 352.51 79.8 353.75 107 354 M 107 354 C 105.43 358.51 105.82 364.33 107 380 M 107 354 C 106.8 362.41 106.95 373.57 107 380 M 107 380 C 75.53 380.05 35.84 382.08 14 380 M 107 380 C 77.54 382.3 52.79 380.12 14 380 M 14 380 C 18.23 378.44 11.74 373.01 14 354 M 14 380 C 14.73 373.48 13.4 362.37 14 354" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 91px; height: 1px; padding-top: 367px; margin-left: 15px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">VUI appliance</div></div></div></foreignObject><text x="61" y="371" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">VUI appliance</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-15"><g><rect x="635" y="307" width="120" height="46" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 330px; margin-left: 636px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Nexus</div></div></div></foreignObject><text x="695" y="334" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Nexus</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-16"><g><path d="M 318 74 L 462.63 74" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 467.88 74 L 460.88 77.5 L 462.63 74 L 460.88 70.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 74px; margin-left: 394px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div style="font-size: 10px;">GET<br />POST</div></div></div></div></foreignObject><text x="394" y="77" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-17"><g><path d="M 377 581 L 407 581 L 455 553.25 L 478.63 553.25" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 483.88 553.25 L 476.88 556.75 L 478.63 553.25 L 476.88 549.75 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 581px; margin-left: 399px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">GET</div></div></div></foreignObject><text x="399" y="584" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-18"><g><rect x="659" y="473.25" width="110" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 488px; margin-left: 660px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Proxy</div></div></div></foreignObject><text x="714" y="492" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Proxy</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-19"><g><rect x="659" y="513.25" width="110" height="30" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 528px; margin-left: 660px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Dependency</div></div></div></foreignObject><text x="714" y="532" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Dependency</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-20"><g><path d="M 531 655 L 561 655 L 605 330 L 628.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 633.88 330 L 626.88 333.5 L 628.63 330 L 626.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 655px; margin-left: 557px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">GET</div></div></div></foreignObject><text x="557" y="658" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-21"><g><rect x="470" y="642" width="61" height="26" fill="none" stroke="none" pointer-events="all"/><path d="M 470.27 641.69 C 470.27 641.69 470.27 641.69 470.27 641.69 M 470.27 641.69 C 470.27 641.69 470.27 641.69 470.27 641.69 M 470.01 648.08 C 471.82 645.13 472.97 645.46 475.26 642.05 M 470.01 648.08 C 471.99 646.07 472.98 645.3 475.26 642.05 M 469.75 654.48 C 471.23 653.9 476.93 649.39 480.9 641.65 M 469.75 654.48 C 473.36 652.47 473.3 647.57 480.9 641.65 M 470.15 660.12 C 473.67 652.55 480.63 652.18 485.89 642.01 M 470.15 660.12 C 477.1 653.38 481.11 646.07 485.89 642.01 M 469.88 666.52 C 472.19 659.6 477.86 654.4 490.88 642.37 M 469.88 666.52 C 476.88 659.26 483.5 648.85 490.88 642.37 M 472.25 669.9 C 475.66 662.66 489 654.8 496.52 641.98 M 472.25 669.9 C 482.16 662.56 487.6 651.54 496.52 641.98 M 477.23 670.26 C 481.09 666.26 488.41 658.31 501.51 642.34 M 477.23 670.26 C 485 659.78 494.31 651.16 501.51 642.34 M 482.88 669.87 C 491.45 657.9 500.7 648.04 507.15 641.94 M 482.88 669.87 C 488 662.56 495.61 653.83 507.15 641.94 M 487.86 670.23 C 493.82 660.27 503.13 655.24 512.14 642.3 M 487.86 670.23 C 494.55 662.45 502.92 649.95 512.14 642.3 M 493.51 669.83 C 500.15 657.06 509.6 650.73 517.78 641.91 M 493.51 669.83 C 500.01 660.91 509.46 653.59 517.78 641.91 M 498.49 670.19 C 506.11 660.77 514.19 650.58 522.77 642.27 M 498.49 670.19 C 507.75 659.66 516.29 649.48 522.77 642.27 M 504.14 669.8 C 511.78 662.59 515.79 649.23 528.41 641.87 M 504.14 669.8 C 513.11 658.72 521.51 651.23 528.41 641.87 M 509.12 670.16 C 516.91 668.11 517.34 662.81 532.74 642.99 M 509.12 670.16 C 514.87 662.5 520.76 657.91 532.74 642.99 M 514.77 669.76 C 523.84 665.28 526.4 655.65 532.48 649.39 M 514.77 669.76 C 521.91 664.08 525.11 657.7 532.48 649.39 M 519.75 670.12 C 520.94 667.25 529.67 660.16 532.22 655.78 M 519.75 670.12 C 523.55 665.96 528.98 657.65 532.22 655.78 M 525.4 669.73 C 527.45 666.33 531.71 662 532.61 661.43 M 525.4 669.73 C 526.41 667.65 529.52 665.64 532.61 661.43" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 470 642 C 484.51 640.64 497.89 642.01 531 642 M 470 642 C 493.87 639.37 514.04 640.83 531 642 M 531 642 C 533.3 647.76 533.89 658.48 531 668 M 531 642 C 532.04 651.04 530.44 659.54 531 668 M 531 668 C 514.17 665.93 497.98 663.9 470 668 M 531 668 C 516.05 669.22 501.57 668.74 470 668 M 470 668 C 471.55 661.61 470.77 649.82 470 642 M 470 668 C 470.47 659.79 470.47 650.66 470 642" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 59px; height: 1px; padding-top: 655px; margin-left: 471px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Updater</div></div></div></foreignObject><text x="501" y="659" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Updater</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-22"><g><path d="M 485 553.25 L 515 553.25 L 605 330 L 628.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 633.88 330 L 626.88 333.5 L 628.63 330 L 626.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-23"><g><ellipse cx="485" cy="553.25" rx="7" ry="7" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="475" y="543.25" width="20" height="20" fill="none" stroke="none" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-24"><g><path d="M 371 531 L 401 531 L 455 553.25 L 478.63 553.25" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 483.88 553.25 L 476.88 556.75 L 478.63 553.25 L 476.88 549.75 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 531px; margin-left: 393px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">GET</div></div></div></foreignObject><text x="393" y="534" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-25"><g><path d="M 469 330 L 499 330 L 605 330 L 628.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 633.88 330 L 626.88 333.5 L 628.63 330 L 626.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-26"><g><ellipse cx="469" cy="330" rx="7" ry="7" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="459" y="320" width="20" height="20" fill="none" stroke="none" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-27"><g><path d="M 313 173 L 343 173 L 439 330 L 462.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 467.88 330 L 460.88 333.5 L 462.63 330 L 460.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 173px; margin-left: 341px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 10px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;">GET<br />POST</div></div></div></div></foreignObject><text x="341" y="176" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-28"><g><path d="M 313 238 L 343 238 L 439 330 L 462.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 467.88 330 L 460.88 333.5 L 462.63 330 L 460.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 238px; margin-left: 337px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div style="font-size: 10px;">GET<br />DELETE</div></div></div></div></foreignObject><text x="337" y="241" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-29"><g><path d="M 318 303 L 348 303 L 444 330 L 467.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 472.88 330 L 465.88 333.5 L 467.63 330 L 465.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 303px; margin-left: 338px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div style="font-size: 10px;"><div>GET<br />POST<br />PATCH<br />DELETE</div></div></div></div></div></foreignObject><text x="338" y="306" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-30"><g><path d="M 313 367 L 343 367 L 439 330 L 462.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 467.88 330 L 460.88 333.5 L 462.63 330 L 460.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 367px; margin-left: 341px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div style="font-size: 10px;">GET<br />POST<br />PATCH<br />DELETE</div></div></div></div></foreignObject><text x="341" y="370" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-31"><g><path d="M 313 432 L 343 432 L 439 330 L 462.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 467.88 330 L 460.88 333.5 L 462.63 330 L 460.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 432px; margin-left: 338px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><div style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 10px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: nowrap; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;">GET<br />POST</div></div></div></div></foreignObject><text x="338" y="435" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">GET...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-32"><g><path d="M 24.66 418.4 C 24.93 417.84 25.43 417.5 25.98 417.5 C 26.52 417.5 27.02 417.84 27.29 418.4 L 35.8 435.27 C 36 435.76 35.96 436.35 35.69 436.8 C 35.42 437.25 34.97 437.5 34.5 437.44 L 17.23 437.44 C 16.78 437.36 16.4 437.02 16.2 436.54 C 16 436.06 16.01 435.5 16.23 435.03 Z M 24.82 423.2 L 25.18 430.27 L 26.81 430.27 L 27.11 423.2 Z M 24.72 433.04 C 24.72 433.44 24.85 433.83 25.1 434.12 C 25.34 434.4 25.67 434.56 26.01 434.56 C 26.73 434.56 27.31 433.88 27.31 433.04 C 27.31 432.2 26.73 431.52 26.01 431.52 C 25.67 431.52 25.34 431.68 25.1 431.96 C 24.85 432.25 24.72 432.64 24.72 433.04 Z" fill="#ffab00" stroke="none" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-33"><g><path d="M 469 74 L 499 74 L 605 330 L 628.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 633.88 330 L 626.88 333.5 L 628.63 330 L 626.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-34"><g><ellipse cx="469" cy="74" rx="7" ry="7" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="459" y="64" width="20" height="20" fill="none" stroke="none" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-35"><g><ellipse cx="672" cy="616" rx="7" ry="7" fill="rgb(0, 0, 0)" stroke="none" pointer-events="all"/><rect x="662" y="606" width="20" height="20" fill="none" stroke="none" pointer-events="all"/></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-36"><g><rect x="688" y="595.5" width="253" height="41" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 616px; margin-left: 690px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">Black dot does not represent any proxy/infra<br />Just used to declutter diagram</div></div></div></foreignObject><text x="690" y="620" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px">Black dot does not represent any proxy/inf...</text></switch></g></g></g><g data-cell-id="rUzoXmEEmOzYYHfq9tto-37"><g><rect x="659" y="430" width="103" height="57" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 101px; height: 1px; padding-top: 437px; margin-left: 661px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 53px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><h1 style="margin-top: 0px;">Legend</h1></div></div></div></foreignObject><text x="661" y="449" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px">Legend</text></switch></g></g></g><g data-cell-id="PmWjxPVjIVMpeAsW6TeR-2"><g><rect x="659" y="553.25" width="110" height="30" fill="none" stroke="none" pointer-events="all"/><path d="M 659.29 552.92 C 659.29 552.92 659.29 552.92 659.29 552.92 M 659.29 552.92 C 659.29 552.92 659.29 552.92 659.29 552.92 M 659.03 559.31 C 659.14 556.45 661.01 554.53 664.28 553.28 M 659.03 559.31 C 660.9 558.31 662.06 556.43 664.28 553.28 M 658.77 565.71 C 663.11 561.67 664.09 553.94 669.92 552.88 M 658.77 565.71 C 663.04 559.41 668.24 554.73 669.92 552.88 M 659.16 571.36 C 667.54 562.7 668.82 561.59 674.91 553.24 M 659.16 571.36 C 667.33 563.31 671.16 555.8 674.91 553.24 M 658.9 577.75 C 662.41 572.21 673.91 558.56 679.89 553.6 M 658.9 577.75 C 662.47 570.52 669.79 563.4 679.89 553.6 M 659.29 583.4 C 665.07 572.93 673.89 565.38 685.54 553.21 M 659.29 583.4 C 664.6 575.91 674.86 566.81 685.54 553.21 M 664.28 583.76 C 672.91 569.64 685.77 559.94 690.52 553.57 M 664.28 583.76 C 674.47 571.48 685.05 560.96 690.52 553.57 M 669.92 583.36 C 680.3 575.61 684.57 572.76 696.17 553.17 M 669.92 583.36 C 676.58 575.46 687.77 565.46 696.17 553.17 M 674.91 583.72 C 686.52 572.44 688.82 561.69 701.15 553.53 M 674.91 583.72 C 685.16 572.11 694.4 560.91 701.15 553.53 M 680.55 583.33 C 694.54 574.73 703.59 556.16 706.8 553.14 M 680.55 583.33 C 690.44 571.44 699.29 563.71 706.8 553.14 M 685.54 583.69 C 695.48 572.66 705.03 563.47 711.78 553.5 M 685.54 583.69 C 695.09 573.92 705.36 562.06 711.78 553.5 M 691.18 583.29 C 701.14 571.58 707.9 565.5 717.43 553.1 M 691.18 583.29 C 700.44 574.71 708.23 560.75 717.43 553.1 M 696.17 583.65 C 705.02 574.59 711.09 565.34 722.41 553.46 M 696.17 583.65 C 700.85 576.5 707.97 567.48 722.41 553.46 M 701.81 583.26 C 707.92 575.98 713.1 567.74 728.06 553.07 M 701.81 583.26 C 712.45 574.49 720.77 562.2 728.06 553.07 M 706.8 583.62 C 716.34 572.14 721.37 570.07 733.04 553.43 M 706.8 583.62 C 714.1 576.48 719.03 568.88 733.04 553.43 M 712.44 583.22 C 724.49 570.7 734.66 562.45 738.69 553.04 M 712.44 583.22 C 717.92 576.71 725.81 569.55 738.69 553.04 M 717.43 583.58 C 728.91 573.55 734.4 560.92 743.67 553.4 M 717.43 583.58 C 724.26 573.04 732.95 565.15 743.67 553.4 M 723.07 583.19 C 731.48 572.99 741.03 562.42 749.32 553 M 723.07 583.19 C 728.37 577.7 733.39 568.72 749.32 553 M 728.06 583.55 C 738.43 568.69 746.96 557.95 754.3 553.36 M 728.06 583.55 C 733.31 578.51 737.95 571.97 754.3 553.36 M 733.7 583.16 C 741.52 576.07 751.68 567.94 759.95 552.97 M 733.7 583.16 C 742.91 570.5 754.24 561.48 759.95 552.97 M 738.69 583.52 C 745.35 572.8 759.68 558.69 764.93 553.33 M 738.69 583.52 C 747.17 576.58 753.72 566.9 764.93 553.33 M 744.33 583.12 C 754.12 574.8 754.74 567.81 769.92 553.69 M 744.33 583.12 C 749.96 577.82 756.34 569.67 769.92 553.69 M 749.32 583.48 C 758.78 574.39 768.02 563.79 769.66 560.08 M 749.32 583.48 C 756.23 574.44 762.47 567 769.66 560.08 M 754.96 583.09 C 763.58 575.85 767.68 568.06 770.05 565.73 M 754.96 583.09 C 759.95 578.54 762.86 574.7 770.05 565.73 M 759.95 583.45 C 764.01 577.66 767.76 573.6 769.79 572.13 M 759.95 583.45 C 763.79 581.31 767.13 575.4 769.79 572.13 M 764.94 583.81 C 765.45 582.66 767.77 580.5 769.53 578.52 M 764.94 583.81 C 766.28 582.51 767.7 580.21 769.53 578.52" fill="none" stroke="#fff2cc" stroke-width="0.5" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/><path d="M 659 553.25 C 701.16 553.47 748.55 550.97 769 553.25 M 659 553.25 C 695.08 553.56 732.32 551.22 769 553.25 M 769 553.25 C 773.26 562.2 769.87 568.23 769 583.25 M 769 553.25 C 767.27 560.31 767.67 568.9 769 583.25 M 769 583.25 C 729.64 583.61 698.27 579.22 659 583.25 M 769 583.25 C 731.3 583.03 698.2 583.4 659 583.25 M 659 583.25 C 656.51 575.95 656.32 567.26 659 553.25 M 659 583.25 C 658.44 574.77 659.92 561.56 659 553.25" fill="none" stroke="#d6b656" stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="10" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 568px; margin-left: 660px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Not Implemented</div></div></div></foreignObject><text x="714" y="572" fill="rgb(0, 0, 0)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Not Implemented</text></switch></g></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>