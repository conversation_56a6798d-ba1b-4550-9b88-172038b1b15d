# Nexus Development

The doc details how to work on the Nexus repo. 

## Setup

### Prerequisites

Set up build-tools

```bash
make setup
```

Run `make` to see the list of available build tool commands

### Create Virtual Environment

It is recommended to use a virtual environment for local development

```bash
pyenv local 3.12.0 nexus        # if you use pyenv
pyenv virtualenv 3.12.0 nexus   # if you use pyenv-virtualenv
pyenv activate nexus

# or `virtualenv`

python3 -m venv venv            # if you use regular virtualenv
source venv/bin/activate
```

#### Package Setup

Run the following commands in order to install the required packages to your env.

```bash
make wheel_publish_pip               # doc: https://confluence.vectra.io/display/DUBOPS/Python+libraries%3A+Using+Vectra+wheels+in+Docker
pip install -r requirements.txt      # installs runtime dependencies
pip install -r requirements-dev.txt  # installs dev dependencies
````

## Running Locally

Nexus can be run locally using docker containers.

> ### External Dependencies
>
> Nexus contains dependencies on external services which are running on Lambdas behind an API Gateway instance. 
> The API GW is a private instance within a VPC which utilizes IAM authorization on incoming requests. 
> This makes full end to end local testing not possible
>
> The workaround is to set up a **mock server running on FastAPI** to return mock data for those services. 
> The implementation can be found at `tests/utils/mock_api`. 
> This service is deployed locally when running `make nexus` and its endpoints are exposed on port `8002`. 
>
> **Mock Server Steps**
>
> 1. Add custom logic within `tests/utils/mock_api` and expose new route 
> 2. Set the env var within `.env.test` for example `YOUR_DEPENDENCY_URL=http://mock-api:8002`
> 3. Execute `make nexus` to start nexus along with mock server

### Local Infrastructure

![local-architecture](assets/local-architecture.svg) 

- **Nexus** FastAPI is run inside a docker container at http://localhost:8000/.

- **mock_api** is a small FastAPI app used to mock Consent Service requests.

- **dynamodb** create a local dynamodb instance. The nexus table is created when the containers are spun up via the **ddb_setup container**.

- **localstack** is used to mock Nexus's SNS Topic as well as Logflow's SQS Queue

  - There is a python script that is run via the following make command 

  - ```bash
    make mock-logflow-validation
    ```

  - This does the following

    - Pulls message from the SQS Queue
    - `POST /v1/connectors/:id/setup/consent` to complete consent setup for each connector in the queue.
  
- Local environment variables are located in `.env.test`

### Run App Locally

You can deploy your own version of Nexus. 

  ```bash
make nexus

# runs 'docker-compose down' before 'nexus'
make restart
  ```

This will run an uvicorn instance on http://localhost:8000

You can view the docs on http://localhost:8000/docs which will also allow you to invoke endpoints directly from your
browser

The instance also has auto reload enabled so as you change the sourcecode the server will auto refresh with 
the latest changes.

#### Local Deployment in Debug

```bash
make nexus-debug

# runs 'docker-compose down' before 'nexus-debug'
make restart-debug
```

To run nexus locally with IDE **breakpoint debugging**, see the following doc: [debug-nexus](./debug-nexus.md)

### Local DynamoDB Instance

A local instance of DynamoDB will be running on http://localhost:8001. As part of this setup a single database file named 
shared-local-instance.db is crated within `docker/dynamodb`. Every instance that connects to DynamoDB accesses this file. 
If you delete the file, you lose any data that you have stored in it.

An additional script is executed to create the table for Nexus. The script can be found at `tests/nexus/utils`

You can interact with the table directly through the AWS CLI as follows: 

```bash
aws dynamodb list-tables --endpoint-url http://localhost:8001
```

Alternatively, an open-source UI can be used for interacting with the table: https://github.com/aaronshaf/dynamodb-admin.

```bash
npm install -g dynamodb-admin
dynamodb-admin --dynamo-endpoint=http://localhost:8001 --port 8005
```

The UI can be accessed in your browser at http://localhost:8005.

## Testing

Tests in nexus are written in `pytest`. Nexus also utilizes [moto](https://docs.getmoto.org/en/latest/index.html) to mock dynamodb when running tests.

Nexus follows a layered architecture, with each layer having its own responsibilities:

- **API Layer**: Handles incoming requests and validates input/output.
- **Controller Layer**: Manages the business logic and workflow.
- **Data Layer**: Handles interaction with the database and external services.

### Unit Tests

Each layer is tested independently in the unit tests, which ensures that the functionality of each individual component is isolated. 

**Unit tests should not cross between layers**; they should focus on the specific logic of the layer they are testing.

To run python unit tests locally use:

```bash
make nexus-unit # utilizes docker compose
```

Using pytest CLI

```bash
# Run tests
pytest tests/nexus/unit/
# Run tests: drop into the pdb debugger on test fail
pytest tests/nexus/unit/ --pdb
# Run tests: drop into the pdb debugger on test fail, using ipython shell
pytest tests/nexus/unit/ --pdb --pdbcls=IPython.terminal.debugger:TerminalPdb
```

### Integration Tests

Integration tests validate the interaction between the layers, ensuring that components from the **API** to the **Data** layer work together as expected.

For Nexus, integration tests span the following:

1. From API endpoint requests, through the controller layer, down to the data layer (e.g., database queries).
2. External service requests are mocked to simulate real interactions, ensuring the system behaves correctly with third-party services.
3. The database is mocked using `moto`, which simulates AWS services like DynamoDB.

These tests cover the full lifecycle of a request, allowing you to verify the end-to-end flow of the application in a controlled environment, while avoiding actual external calls.

To run python unit tests locally use:

```bash
make nexus-integration
```

Using pytest CLI

```bash
# Run tests
pytest tests/nexus/integration/
# Run tests: drop into the pdb debugger on test fail
pytest tests/nexus/integration/ --pdb
# Run tests: drop into the pdb debugger on test fail, using ipython shell
pytest tests/nexus/integration/ --pdb --pdbcls=IPython.terminal.debugger:TerminalPdb
```



## Developer Stack on AWS

It is not recommended to deploy you own full infrastructure stack for nexus.

Instead, a sandbox stack is used where you are able to deploy your own version of the FastAPI application.

To do this do the following

- Create a branch that has `-prgen` suffix

  - ```bash
    git checkout HYPERION-1234-description-prgen
    ```

- Run the following commands

  - ```bash
    make set_ingestion_cluster # sets eks cluster to ingestionv1
    make prgen_update
    ```

  - This does the following

    - build docker image and push to ECR
    - update prgen helm chat with updated image tag and update `hostnamePublic` and `hostnamePrivate` to use user email.

  - Make a commit, push the change to remote branch and raise a Pull Request

  - ArgoCD will see that a PR has been created with a `-prgen` branch and spin up an EKS pod with your FastAPI App

- This above steps can be shortened by the follow

  - ```bash
    make prgen_update_commit
    ```

    - This does the following
      - Add all unstaged changes, commit with generic message and push to sourcecode to update prgen stack



## Nexus Client

Nexus has an auto generated client located at `src/vectra/nexus_client`

This client is accompanied with auto generated docs located inside the client directory.

### Generate Client

A python client can be auto-generated through the use of https://openapi-generator.tech/

This make command should be executed if there are any changes in the API spec that need to be distributed to clients


```bash
make client
````

### Use Client Locally

- Install from `saas-artifacts-root`

  - ```bash
    make wheel_preview_pip
    pip install vectra-nexus-client==202409.0.0
    ```

- Publish custom client

  - https://confluence.vectra.io/pages/viewpage.action?pageId=113297731
  
    
  

### TODO

- MySQL Workbench for viewing database locally

