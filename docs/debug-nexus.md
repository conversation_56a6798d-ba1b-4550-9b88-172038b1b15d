# Nexus Debug

`nexus-debug` service config in the `docker-compose.yml` allows for running the service locally with breakpoint debugging using your IDE.

### How To

Run the following

- ```bash
  make nexus-debug
  ```

You should see the following in your IDE

- ![image-20240423110429744](assets/debug-nexus-1.png)

### VSCode Instructions

Add the following configuration to your `launch.json`

- ```json
  {
    "name": "Attach to FastAPI",
    "type": "debugpy",
    "request": "attach",
    "connect": {
      "host": "localhost",
      "port": 5678
    },
    "pathMappings": [
      {
        "localRoot": "${workspaceFolder}/src",
        "remoteRoot": "/var/task"
      }
    ],
    "justMyCode": true,
    "subProcess": true
  },
  ```

In the Run and Debug side bar run the above config

- ![image-20240423110757597](assets/debug-nexus-2.png) 

The terminal should update and the FastAPI app should be available at localhost



### Pycharm Instruction

TODO.