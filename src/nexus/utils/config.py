# pylint: disable=too-few-public-methods

"""Config file"""

import os


class Config:
    """Config Class"""

    NEXUS_TABLE_NAME = os.environ["NEXUS_TABLE_NAME"]
    NEXUS_SNS_TOPIC_ARN = os.environ["NEXUS_SNS_TOPIC_ARN"]
    DDB_ENDPOINT_URL = os.getenv("DDB_ENDPOINT_URL")
    SNS_ENDPOINT_URL = os.getenv("AWS_ENDPOINT")
    REGION = os.environ["AWS_REGION"]
    CONSENT_INTERNAL_URL = os.environ["CONSENT_INTERNAL_URL"]
    CONSENT_INTERNAL_DELETE_ROLE_ARN = os.environ["CONSENT_INTERNAL_DELETE_ROLE_ARN"]
    PAGINATION_LIMIT = int(os.environ["PAGINATION_LIMIT"])


config = Config()
