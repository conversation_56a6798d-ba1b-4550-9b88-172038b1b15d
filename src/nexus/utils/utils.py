"""Utility Methods"""

import asyncio
from typing import Any, Callable, TypeVar

from nexus.api.exceptions import ForbiddenException

T = TypeVar("T")


def validate_tenancy(x_internal_brain_id: str, connector_brain_id: str) -> None:
    """
    With the absence of service-to-service auth within Vectra, we are forced to perform
    as 'tenancy' check which simply check if the header in the incoming request matches the brain_id in the
    persisted object. The header check is not secure and is NOT a form of authorization
    """

    if x_internal_brain_id != connector_brain_id:
        raise ForbiddenException()


def mask_value(value: str, unmasked_start: int) -> str:
    """Masks a value, leaving the first few characters visible."""
    if not value or len(value) <= unmasked_start:
        return value
    masked_value = value[:unmasked_start] + "*" * (len(value) - unmasked_start)
    return masked_value


def mask_dict_values(data: dict, keys_to_mask: list[str], unmasked_start=3):
    """Masks specified keys in a dictionary."""
    masked_data = data.copy()
    for key in keys_to_mask:
        if key in masked_data:
            masked_data[key] = mask_value(masked_data[key], unmasked_start=unmasked_start)
    return masked_data


async def run_in_executor(func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
    """Run a synchronous function in the default executor"""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, lambda: func(*args, **kwargs))
