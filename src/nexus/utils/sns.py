"""Responsible for interactions with Nexus's AWS SNS topic"""

from typing import Any

import boto3

from nexus.models import NexusEvent
from nexus.utils.config import config
from nexus.utils.utils import run_in_executor


async def publish_nexus_event(event: NexusEvent, sns_client: Any = None) -> None:
    """Publish Nexus event to SNS topic"""
    if sns_client is None:
        sns_client = boto3.client("sns", endpoint_url=config.SNS_ENDPOINT_URL)

    publish_kwargs = {
        "TopicArn": config.NEXUS_SNS_TOPIC_ARN,
        "Message": event.model_dump_json(exclude={"request_context"}),
        "MessageAttributes": {
            "ConnectorId": {"DataType": "String", "StringValue": event.connector_id},
            "ConnectorType": {"DataType": "String", "StringValue": event.connector_type},
            "ConnectorState": {"DataType": "String", "StringValue": event.connector_state},
            "InternalBrainId": {"DataType": "String", "StringValue": event.internal_brain_id},
        },
    }
    await run_in_executor(sns_client.publish, **publish_kwargs)
