# pylint: disable=too-few-public-methods

"""
Contains all project related factories
"""

from nexus.context import request_context
from nexus.controllers import (
    AwsConnectorController,
    AzureCPConnectorController,
    ConnectorController,
    CrowdstrikeConnectorController,
    DefenderConnectorController,
    SentinelOneConnectorController,
)
from nexus.models import ConnectorType
from nexus.repositories.connector import ConnectorRepository
from nexus.utils.config import config


class ConnectorFactory:
    """
    This is the connector factory container that will instantiate all the controllers and
    repositories which can be accessed by the rest of the application.
    """

    connector_types = {
        None: ConnectorController,  # Default
        ConnectorType.AZURE_CP: AzureCPConnectorController,
        ConnectorType.AWS: AwsConnectorController,
        ConnectorType.CROWDSTRIKE: CrowdstrikeConnectorController,
        ConnectorType.DEFENDER: DefenderConnectorController,
        ConnectorType.SENTINELONE: SentinelOneConnectorController,
    }

    controller_repository = ConnectorRepository(config.NEXUS_TABLE_NAME)

    def get_connector_controller(self, connector_type: ConnectorType | None = None) -> ConnectorController:
        """
        Creates a controller based on the connector_type
        """
        request_context.get().update(connector_type=connector_type)
        connector_controller = self.connector_types[connector_type]
        return connector_controller(self.controller_repository)
