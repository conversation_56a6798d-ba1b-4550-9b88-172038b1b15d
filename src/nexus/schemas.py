"""Connector Schemas"""

import json
import re
from typing import Li<PERSON>, Optional, Union
from uuid import UUID

from botocore.paginate import To<PERSON><PERSON>ncoder
from fastapi import Header
from pydantic import AliasChoices, BaseModel, ConfigDict, Field, field_validator, model_validator
from starlette.datastructures import URL
from typing_extensions import Annotated, Self

from nexus.api.exceptions import BadRequestException
from nexus.models import (
    ConnectorError,
    ConnectorSize,
    ConnectorState,
    ConnectorType,
    CustomBaseModel,
    OperatingState,
    VectraService,
)


class VectraHeaders(CustomBaseModel):
    """Expected Vectra request headers"""

    x_vectra_service: Annotated[VectraService, Header()] = Field(Header())
    x_internal_brain_id: Annotated[str | None, Header()] = Field(Header(default=None))

    @model_validator(mode="after")
    def validate_headers(self) -> Self:
        """Check at least one header is present"""
        if self.x_vectra_service == VectraService.VUI and not self.x_internal_brain_id:
            raise BadRequestException("x-internal-brain_id header missing")
        return self


##############
# CONNECTORS #
##############


class ConnectorBase(CustomBaseModel):
    """Connector Base model"""

    name: str = Field(..., description="Connector Name", example="aws_connector")


class ConnectorCreate(ConnectorBase):
    """Request body model when creating a new connector"""

    connector_type: ConnectorType = Field(..., description="Connector Type", examples=list(ConnectorType))
    connector_id: UUID | None = Field(
        None,
        description="Connector ID when created on different connector manager (Sensible)",
        example="550e8400-e29b-41d4-a716-************",
    )


class ConnectorDeleteInput(CustomBaseModel):
    """Request body model when deleting an existing connector"""

    connector_type: ConnectorType = Field(..., description="Connector Type", examples=list(ConnectorType))


class ConnectorUpdate(CustomBaseModel):
    """Request body model when updating an existing connectors non-breaking properties"""

    name: str | None = Field(None, description="Connector Name", example="aws_connector")
    last_log_received: str | None = Field(
        None,
        description="The date of when the last log was seen for the connector",
        example="2024-02-26T10:37:11Z",
    )
    size: ConnectorSize | None = Field(None, description="Connector Size", examples=list(ConnectorSize))
    last_sequence_id: str | None = Field(
        None, description="The SequenceId from the last processed detection event in DEP", example="0000000m2f3fw9w"
    )
    model_config = ConfigDict(extra="forbid")

    @field_validator("last_log_received", mode="before")
    @classmethod
    def validate_last_log_received(cls, value):
        "Validate last log received is in the correct format"
        if value is None:
            return value
        if not re.fullmatch(r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$", value):
            raise BadRequestException(
                "The date must be in UTC ISO format with Zulu notation (e.g. 2024-02-26T10:37:11Z)"
            )
        return value


class ConnectorStateConnected(BaseModel):
    """Request body to update connector state to 'connected'"""

    connector_state: Literal[ConnectorState.CONNECTED] = Field(..., description="Connector is connected")


class ConnectorStateError(BaseModel):
    """Request body to update connector to 'error'"""

    connector_state: Literal[ConnectorState.ERROR] = Field(..., description="Connector is in error state")
    error: ConnectorError = Field(
        ...,
        description="The error message associated with the connector",
        example={"error": {"code": "AWS_INVALID_IAM_ROLE", "message": "aws error"}},
    )


ConnectorStateInput = Annotated[
    Union[ConnectorStateConnected, ConnectorStateError], Field(discriminator="connector_state")
]


class ConnectorQueryParams(CustomBaseModel):
    """Query params for getting connectors"""

    connector_type: ConnectorType | None = Field(None, description="Connector Type", examples=list(ConnectorType))
    connector_state: ConnectorState | None = Field(None, description="Connector State", examples=list(ConnectorState))
    properties: str | None = Field(
        None, description="Connectors internal properties", example='{"license_type": "premium"}'
    )
    next_token: str | None = Field(
        None, description="encoded value for the LastEvaluatedKey DDB query", example="3uh24uyuj43r..."
    )
    operating_state: OperatingState | None = Field(
        None, description="Filter by operating state (active/inactive)", examples=list(OperatingState)
    )

    @model_validator(mode="after")
    def post_creation_validation(self) -> Self:
        """
        Function to validate query params
        - check connector_type is given if searching with connector_state
        - check properties is valid json, check connector_type is given, and convert to python dict
        """
        if self.connector_state and not self.connector_type:
            raise BadRequestException("connector_type is required when filtering with connector_state")

        if self.properties:
            if not self.connector_type:
                raise BadRequestException("connector_type is required when filtering with properties")
            try:
                self.properties = json.loads(self.properties)
            except json.JSONDecodeError as e:
                raise BadRequestException(detail="Invalid JSON format for properties") from e

        return self


class ConnectorResponse(ConnectorBase):
    """Connector Response"""

    model_config = ConfigDict(from_attributes=True)

    connector_type: ConnectorType = Field(..., description="Connector Type", examples=list(ConnectorType))
    connector_id: str = Field(
        ...,
        validation_alias=AliasChoices("pk", "connector_id"),
        description="Connector ID",
        example="76d6d891-7e5d-47c8-a078-666044c0bbdb",
    )
    connector_state: ConnectorState = Field(..., description="Connector State", examples=list(ConnectorState))
    operating_state: OperatingState = Field(..., description="Connector State", examples=list(OperatingState))
    internal_brain_id: str = Field(
        ..., description="Brain ID of the tenant", example="saas99d4e37f493a4d37b83b09029f5c4dd7"
    )
    setup_records: dict = Field(
        ...,
        description="Setup records and their state associated with the connector",
        example={"setup_records": {"consent": "completed", "user_input": "awaiting"}},
    )
    created_at: str = Field(
        ..., description="The date the connector was first created", example="2024-02-26T10:37:11.750Z"
    )
    updated_at: str = Field(
        ..., description="The date the connector was last modified", example="2024-02-26T10:37:11.750Z"
    )
    properties: dict = Field(
        ...,
        description="The properties associated with the connector",
        example={"iam_role_arn": "arn:aws:iam::1234567891:role/foo"},
    )
    error: ConnectorError | None = Field(
        ...,
        description="The error message currently associated with a connector",
        example={"error": {"code": "AWS_INVALID_IAM_ROLE", "message": "aws error"}},
    )
    first_log_received: str | None = Field(
        default=None,
        description="The date of when the first log was seen for the connector",
        example="2024-02-14T10:37:11.750Z",
    )
    last_log_received: str | None = Field(
        ..., description="The date of when the last log was seen for the connector", example="2024-02-26T10:37:11.750Z"
    )
    size: ConnectorSize = Field(
        default=ConnectorSize.MEDIUM, description="Connector Size", examples=list(ConnectorSize)
    )
    last_sequence_id: str | None = Field(
        default=None,
        description="The SequenceId from the last processed detection event in DEP",
        example="0000000m2f3fw9w",
    )


class ConnectorListResponse(BaseModel):
    """
    Connectors response constructed from boto3 dynamodb query response
    """

    connectors: list[ConnectorResponse] = Field(..., validation_alias="Items", description="A list of connectors")
    last_evaluated_key: dict | None = Field(default=None, validation_alias="LastEvaluatedKey", exclude=True)
    next_link: str | None = Field(default=None, description="A url link which includes the next_link query param")

    def set_next_link(self, url: URL) -> None:
        """
        Sets the next_link attribute, given a fastapi Request object.
        - encodes the last_evaluated_key
        - adds the encoded values as a query param to the given url

        Note:
            LastEvaluatedKey is returned by the dynamodb query response when result is paginated.
        """
        if self.last_evaluated_key:
            next_token = TokenEncoder().encode(self.last_evaluated_key)
            self.next_link = str(url.include_query_params(next_token=next_token))


#################
# SETUP ACTIONS #
#################


class MicrosoftConsentGrant(CustomBaseModel):
    """Consent Grant Model representing a Microsoft type consent"""

    connector_type: Literal[ConnectorType.DEFENDER, ConnectorType.AZURE_CP]
    ms_tenant_id: str = Field(
        ..., description="Microsoft Tenant ID for the consenting tenant", example="dda75ba7-c192-4bf3-8139-2d4b1hqbc6cc"
    )


class ConsentGrantRecord(CustomBaseModel):
    """Request body model when completing a consent setup action"""

    properties: Union[MicrosoftConsentGrant] = Field(
        ..., discriminator="connector_type", title="ConsentGrantProperties"
    )


class ConnectorAwsProperties(CustomBaseModel):
    """Model to update AWS Connector Properties"""

    connector_type: Literal[ConnectorType.AWS]
    s3_bucket_name: str = Field(
        pattern=r"^[a-z0-9.-]{3,63}$",
        description="Cloudtrail S3 Bucket Name",
        example="mock-bucket",
    )
    iam_role_arn: str = Field(
        pattern=re.compile(r"^arn:aws:iam::\d{12}:role\/(?=.{0,512}$)(?:[\w+=,.@-]+\/)*[\w+=,.@-]{1,64}$"),
        description="S3 Bucket ARN",
        example="arn:aws:iam::123456789009:role/mock-bucket",
    )
    sns_topic_arn: str = Field(
        pattern=re.compile(
            r"^(arn:aws:sns:(us(-gov)?|ap|ca|cn|eu|il|me|sa)-(central|(north|south)?(east|west)?)"
            r"-\d:(\d{12}):([\w-]{1,256}$))$"
        ),
        description="SNS topic ARN",
        example="arn:aws:sns:us-west-2:123456789009:mock-bucket",
    )


class ConnectorAzureCPProperties(CustomBaseModel):
    """Model to update Azure CP Connector Properties"""

    connector_type: Literal[ConnectorType.AZURE_CP]
    resource_group_id: str = Field(
        ...,
        description="Location of the logs stored in the azure tenant",
        example="/subscriptions/2a43c28d-hd6-afa6-2f9741cadf44/resourceGroups/rg-vectra-cdr",
    )


class ConnectorPropertiesInput(CustomBaseModel):
    """Request body model when updating AWS or Azure connectors"""

    properties: Union[ConnectorAzureCPProperties, ConnectorAwsProperties] = Field(
        ..., discriminator="connector_type", title="PropertiesInput"
    )


class ConsentSecret(CustomBaseModel):
    """Consent secret model for creating a secret"""

    # Note: users dont have the option to name a secret in VUI currently
    # A default value of 'client_secret' is set. This field is required by Consent.
    secret_name: Optional[str] = Field(
        default="client-secrets",
        serialization_alias="secret-name",
        description="Secret Name",
        example="client-secrets",
    )
    client_id: str = Field(
        ..., serialization_alias="client-id", description="API Client Id", example="298342j382u34n83u2"
    )
    client_secret: str = Field(
        ...,
        serialization_alias="client-secret",
        description="API Secret Key",
        example="abcD1234EFghIjkLmNoPqRstUvWxYz567890",
    )


class ConsentSecretUpdate(CustomBaseModel):
    """Consent secret model for updating a secret"""

    secret_name: Optional[str] = Field(
        default="client-secrets",
        serialization_alias="secret-name",
        description="Secret Name",
        example="client-secrets",
    )
    client_id: Optional[str] = Field(
        default=None, serialization_alias="client-id", description="API Client Id", example="298342j382u34n83u2"
    )
    client_secret: Optional[str] = Field(
        default=None,
        serialization_alias="client-secret",
        description="API Secret Key",
        example="abcD1234EFghIjkLmNoPqRstUvWxYz567890",
    )


class SentinelOneSecret(CustomBaseModel):
    """Consent secret model for creating and updating a secret for sentinelone"""

    secret_name: Optional[str] = Field(
        default="token-secret",
        serialization_alias="secret-name",
        description="Secret Name",
        example="token-secret",
    )
    api_token: str = Field(
        ...,
        serialization_alias="api-token",
        description="Api token",
        example="token-secret",
    )


class SentinelOneSecretUpdate(CustomBaseModel):
    """Consent secret model for creating and updating a secret for sentinelone"""

    secret_name: Optional[str] = Field(
        default="token-secret",
        serialization_alias="secret-name",
        description="Secret Name",
        example="token-secret",
    )
    api_token: Optional[str] = Field(
        default="token-secret",
        serialization_alias="api-token",
        description="Api token",
        example="token-secret",
    )


class CrowdstrikeProperties(CustomBaseModel):
    """Crowdstrike specific consent properties"""

    crowdstrike_url: str = Field(
        ..., description="Crowdstrike Base URL used for API access", example="https://api.crowdstrike.com"
    )


class SentinelOneProperties(CustomBaseModel):
    """sentinelOne specific consent properties"""

    sentinelone_url: str = Field(
        ..., description="SentinelOne Base URL used for API access", example="https://usea1-partners.sentinelone.net"
    )


class ConnectorSecretCreate(CustomBaseModel):
    """
    Request body for connectors that consent using an api secret
    Note:
        properties has a default=None as it may not always be required
    """

    connector_type: Literal[ConnectorType.CROWDSTRIKE, ConnectorType.SENTINELONE]
    secret: Union[ConsentSecret, SentinelOneSecret] = Field(..., description="Common secret fields")
    properties: Union[CrowdstrikeProperties, SentinelOneProperties] = Field(
        default=None, description="Connector-specific properties"
    )

    @model_validator(mode="after")
    def validate_properties(self) -> Self:
        """Validate that the properties match the connector type and secret type"""
        if not self.properties:
            raise BadRequestException("Properties are required for connector setup")

        if self.connector_type == ConnectorType.CROWDSTRIKE:
            if not isinstance(self.properties, CrowdstrikeProperties):
                raise BadRequestException(
                    f"Crowdstrike connector requires CrowdstrikeProperties, got {type(self.properties)}"
                )
            if not isinstance(self.secret, ConsentSecret):
                raise BadRequestException(f"Crowdstrike connector requires ConsentSecret, got {type(self.secret)}")

        elif self.connector_type == ConnectorType.SENTINELONE:
            if not isinstance(self.properties, SentinelOneProperties):
                raise BadRequestException(
                    f"SentinelOne connector requires SentinelOneProperties, got {type(self.properties)}"
                )
            if not isinstance(self.secret, SentinelOneSecret):
                raise BadRequestException(f"SentinelOne connector requires SentinelOneSecret, got {type(self.secret)}")

        return self


class ConnectorSecretUpdate(CustomBaseModel):
    """Request body to update the values of a consent secret or consent properties"""

    connector_type: Literal[ConnectorType.CROWDSTRIKE, ConnectorType.SENTINELONE]
    secret: Union[ConsentSecretUpdate, SentinelOneSecretUpdate] = Field(
        default=None, description="Common secret fields", title="SecretUpdate"
    )
    properties: Union[CrowdstrikeProperties, SentinelOneProperties] = Field(
        default=None, description="Connector-specific properties", title="PropertiesUpdate"
    )

    @model_validator(mode="after")
    def validate_properties(self) -> Self:
        """Validate that the properties and secret types match the connector type"""
        if not self.properties and not self.secret:
            return self

        if self.connector_type == ConnectorType.CROWDSTRIKE:
            if self.properties and not isinstance(self.properties, CrowdstrikeProperties):
                raise BadRequestException(
                    f"Crowdstrike connector requires CrowdstrikeProperties, got {type(self.properties)}"
                )
            if self.secret and not isinstance(self.secret, ConsentSecretUpdate):
                raise BadRequestException(
                    f"Crowdstrike connector requires ConsentSecretUpdate, got {type(self.secret)}"
                )

        elif self.connector_type == ConnectorType.SENTINELONE:
            if self.properties and not isinstance(self.properties, SentinelOneProperties):
                raise BadRequestException(
                    f"SentinelOne connector requires SentinelOneProperties, got {type(self.properties)}"
                )
            if self.secret and not isinstance(self.secret, SentinelOneSecretUpdate):
                raise BadRequestException(
                    f"SentinelOne connector requires SentinelOneSecretUpdate, got {type(self.secret)}"
                )

        return self
