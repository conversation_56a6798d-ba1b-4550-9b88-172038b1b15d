"""Nexus Connector CRUD Routes"""

from typing import Annotated, Literal

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Header, Request, status

from nexus.controllers import Connector<PERSON>ontroller
from nexus.models import VectraService
from nexus.schemas import (
    ConnectorCreate,
    ConnectorDeleteInput,
    ConnectorListResponse,
    ConnectorQueryParams,
    ConnectorResponse,
    ConnectorStateInput,
    ConnectorUpdate,
    VectraHeaders,
)
from nexus.utils.factories import ConnectorFactory

router = APIRouter()


@router.post("/connectors", status_code=status.HTTP_201_CREATED, response_model=ConnectorResponse)
async def create_connector(
    connector_input: ConnectorCreate,
    x_internal_brain_id: Annotated[str, Header()],
    x_vectra_service: Annotated[Literal[VectraService.VUI], Header()],
):  # pylint: disable=unused-argument
    """Creates a new connector in the system of the specified type"""
    controller: ConnectorController = ConnectorFactory().get_connector_controller(connector_input.connector_type)
    connector = await controller.create_connector(connector_input, x_internal_brain_id)
    return connector


@router.get("/connectors")
async def get_connectors(
    request: Request,
    query_params: ConnectorQueryParams = Depends(),
    headers: VectraHeaders = Depends(),
) -> ConnectorListResponse:
    """Fetches a list of connectors"""
    controller = ConnectorFactory().get_connector_controller()
    if headers.x_internal_brain_id:
        paginated_connectors = await controller.get_connectors_by_internal_brain_id(headers.x_internal_brain_id)
    else:
        paginated_connectors = await controller.get_connectors_by_params(query_params)
    paginated_connectors.set_next_link(request.url)
    return paginated_connectors


@router.get("/connectors/{connector_id}", response_model=ConnectorResponse)
async def get_connector(connector_id: str, headers: VectraHeaders = Depends()):  # pylint: disable=unused-argument
    """Retrieves a connector"""
    controller: ConnectorController = ConnectorFactory().get_connector_controller()
    connector = await controller.get_connector_by_id(connector_id)
    return connector


@router.patch("/connectors/{connector_id}", response_model=ConnectorResponse)
async def patch_connector(connector_update: ConnectorUpdate, connector_id: str, headers: VectraHeaders = Depends()):
    """Updates connector properties"""
    controller: ConnectorController = ConnectorFactory().get_connector_controller()
    connector = await controller.update_connector(
        connector_update, connector_id, internal_brain_id=headers.x_internal_brain_id
    )
    return connector


@router.post("/connectors/{connector_id}/state", response_model=ConnectorResponse)
async def update_connector_state(
    connector_state_input: ConnectorStateInput,
    connector_id: str,
    x_vectra_service: Annotated[Literal[VectraService.LOGFLOW], Header()],
):  # pylint: disable=unused-argument
    """Updates connector state"""
    controller: ConnectorController = ConnectorFactory().get_connector_controller()
    connector = await controller.update_connector_state(connector_state_input, connector_id)
    return connector


@router.delete("/connectors/{connector_id}", response_model=ConnectorResponse)
async def delete_connector(
    connector_delete: ConnectorDeleteInput,
    connector_id: str,
    x_internal_brain_id: Annotated[str, Header()],
    x_vectra_service: Annotated[Literal[VectraService.VUI], Header()],
):  # pylint: disable=unused-argument
    """Deletes a connector"""
    controller: ConnectorController = ConnectorFactory().get_connector_controller(connector_delete.connector_type)
    connector = await controller.soft_delete_connector(connector_id, internal_brain_id=x_internal_brain_id)
    return connector
