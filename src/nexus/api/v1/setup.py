"""Nexus Setup Routes"""

from typing import Annotated, Literal

from fastapi import API<PERSON><PERSON>er, Header

from nexus.controllers import ConnectorController
from nexus.models import ConnectorType, VectraService
from nexus.schemas import (
    ConnectorPropertiesInput,
    ConnectorResponse,
    ConnectorSecretCreate,
    ConnectorSecretUpdate,
    ConsentGrantRecord,
)
from nexus.services.notifications import NotificationManager
from nexus.utils.factories import ConnectorFactory

router = APIRouter()


@router.post("/connectors/{connector_id}/setup/consent", response_model=ConnectorResponse)
async def setup_consent(
    consent_input: ConsentGrantRecord,
    connector_id: str,
    x_vectra_service: Annotated[Literal[VectraService.CONSENT], Header()],
):
    """Completes the consent setup action on a connector"""
    controller: ConnectorController = ConnectorFactory().get_connector_controller()
    connector = await controller.setup_consent(connector_id, consent_input)
    await NotificationManager().create_event(connector)
    return connector


@router.post("/connectors/{connector_id}/setup/properties", response_model=ConnectorResponse)
async def setup_properties(
    properties_input: ConnectorPropertiesInput,
    connector_id: str,
    x_internal_brain_id: Annotated[str, Header()],
    x_vectra_service: Annotated[Literal[VectraService.VUI], Header()],
):
    """Completes the properties setup action on a connector"""
    controller: ConnectorController = ConnectorFactory().get_connector_controller(
        properties_input.properties.connector_type
    )
    connector = await controller.setup_properties(connector_id, x_internal_brain_id, properties_input)
    await NotificationManager().create_event(connector)
    return connector


@router.post("/connectors/{connector_id}/setup/secrets", response_model=ConnectorResponse)
async def setup_secrets(
    create_secret_body: ConnectorSecretCreate,
    connector_id: str,
    x_internal_brain_id: Annotated[str, Header()],
    x_vectra_service: Annotated[Literal[VectraService.VUI], Header()],
):
    """
    Completes the consent setup action on a connector
    As part of this setup, Nexus will make a request to Consent to create a named secret record for a connector
    """
    controller: ConnectorController = ConnectorFactory().get_connector_controller(create_secret_body.connector_type)
    connector = await controller.setup_secrets(connector_id, x_internal_brain_id, create_secret_body)
    await NotificationManager().create_event(connector)
    return connector


@router.patch("/connectors/{connector_id}/setup/secrets", response_model=ConnectorResponse)
async def update_secret(
    update_secret_body: ConnectorSecretUpdate,
    connector_id: str,
    x_internal_brain_id: Annotated[str, Header()],
    x_vectra_service: Annotated[Literal[VectraService.VUI], Header()],
):
    """Update the consent values of a connector."""
    controller: ConnectorController = ConnectorFactory().get_connector_controller(update_secret_body.connector_type)
    connector = await controller.update_secret(connector_id, x_internal_brain_id, update_secret_body)
    await NotificationManager().create_event(connector)
    return connector
