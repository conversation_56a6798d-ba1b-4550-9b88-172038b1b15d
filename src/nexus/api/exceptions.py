"""API Exceptions"""

from http import HTTPStatus

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONR<PERSON>ponse
from vectra.vui_logger import logger

from nexus.context import request_context


class CustomException(HTTPException):
    """Custom Exception Base Class"""

    status_code: HTTPStatus
    detail: str

    def __init__(self, detail=None):
        super().__init__(self.status_code, self.detail)
        if detail:
            self.detail = detail


class BadRequestException(CustomException):
    """Exception representing 400 Bad Request"""

    status_code: HTTPStatus = HTTPStatus.BAD_REQUEST
    detail: str = "Invalid request data or syntax"


class UnauthorizedException(CustomException):
    """Exception representing 401 Unauthorized"""

    status_code: HTTPStatus = HTTPStatus.UNAUTHORIZED
    detail: str = "Invalid authentication credentials"


class ForbiddenException(CustomException):
    """Exception representing 403 Forbidden"""

    status_code: HTTPStatus = HTTPStatus.FORBIDDEN
    detail: str = "You are forbidden from modifying this resource"


class NotFoundException(CustomException):
    """Exception representing 404 Not Found"""

    status_code: HTTPStatus = HTTPStatus.NOT_FOUND
    detail: str = "This resource cannot be found"


class BadGatewayException(CustomException):
    """Exception representing 502 Bad Gateway"""

    status_code: HTTPStatus = HTTPStatus.BAD_GATEWAY
    detail: str = "Invalid response from a downstream service. Please try again later"


class ConflictException(CustomException):
    """Exception representing 409 Conflict"""

    status_code: HTTPStatus = HTTPStatus.CONFLICT
    detail: str = "A resource of the same name already exists."


class ConsentNotCompletedException(CustomException):
    """Exception representing 400 Bad Request for datasource authorization issues"""

    status_code: HTTPStatus = HTTPStatus.BAD_REQUEST
    detail: str = "Consent setup must be completed before properties setup."


async def custom_exception_handler(request: Request, exc: CustomException) -> JSONResponse:
    """Exception Handler listener for CustomException"""
    error_class = f"{exc.__class__.__name__}"
    request_context.get().error_class = error_class
    extra = {
        **request_context.get().model_dump(),
        "errors": exc.detail,
        "query_params": dict(request.query_params),
        "status_code": exc.status_code,
    }

    if str(exc.status_code).startswith("4"):
        logger.error(error_class, extra=extra)
    elif str(exc.status_code).startswith("5"):
        logger.exception(error_class, extra=extra)

    return JSONResponse(status_code=exc.status_code, content={"error_code": exc.status_code, "message": exc.detail})


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Exception Handler listener for RequestValidationError"""
    error_class = f"{exc.__class__.__name__}"
    request_context.get().error_class = error_class
    extra = {
        **request_context.get().model_dump(),
        "errors": exc.errors(),
        "query_params": dict(request.query_params),
        "status_code": 422,
    }

    logger.error(msg=error_class, extra=extra)

    return JSONResponse(status_code=422, content={"detail": exc.errors()})
