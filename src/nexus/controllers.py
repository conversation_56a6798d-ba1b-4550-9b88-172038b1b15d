"""Connector Controller"""

from typing import Any, cast

from nexus.api.exceptions import ConflictException, ConsentNotCompletedException, NotFoundException
from nexus.context import request_context
from nexus.dependencies.consent import create_consent, create_secret, delete_secret, update_secret
from nexus.models import (
    Connector,
    ConnectorState,
    ConnectorStateUpdate,
    ConnectorType,
    OperatingState,
    SetupAction,
    SetupActionState,
    get_current_time,
)
from nexus.repositories.connector import ConnectorRepository
from nexus.schemas import (
    ConnectorCreate,
    ConnectorListResponse,
    ConnectorPropertiesInput,
    ConnectorQueryParams,
    ConnectorSecretCreate,
    ConnectorSecretUpdate,
    ConnectorStateInput,
    ConnectorUpdate,
    ConsentGrantRecord,
    CrowdstrikeProperties,
    SentinelOneProperties,
)
from nexus.utils.utils import validate_tenancy


class ConnectorController:
    """Base Connector Controller"""

    connector: Connector

    def __init__(self, repository: ConnectorRepository):
        self.connector_repository = repository

    @staticmethod
    def _is_setup_complete(setup_record: dict) -> bool:
        """
        Given a list of setup records this function will check if each setup_record
        is in a completed state and return TRUE if all records are completed
        """
        return all((state == SetupActionState.COMPLETED for state in setup_record.values()))

    def _update_to_connecting_if_setup_complete(self) -> None:
        """Update connector state to CONNECTING if setup is complete"""
        if self._is_setup_complete(self.connector.setup_records):
            self.connector.connector_state = ConnectorState.CONNECTING
            self.connector.error = None

    async def _set_and_validate_connector(self, connector_id: str):
        """Set the connector object and validate that it is not deleted"""
        if not getattr(self, "connector", None):
            self.connector = await self.get_connector_by_id(connector_id)
            request_context.get().update(connector_type=self.connector.connector_type)
        if self.connector.connector_state == ConnectorState.DELETED:
            raise NotFoundException(detail="Connector has been deleted")

    async def _validate_unique_name(self, name: str, internal_brain_id: str, connector_id: str | None = None) -> None:
        """Validate connector name is unique within brain ID"""
        response = await self.connector_repository.get_non_deleted_connector_by_name_and_brain(
            name, internal_brain_id, exclude_connector_id=connector_id
        )

        if response.connectors:
            raise ConflictException(detail=f"Connector with name '{name}' already exists for this brain")

    async def _save(self) -> Connector:
        """Save a connector to the database"""
        self.connector.updated_at = get_current_time()
        return await self.connector_repository.persist_connector(self.connector)

    async def get_connector_by_id(self, connector_id: str) -> Connector:
        """Retrieve a connector by its id"""
        return await self.connector_repository.get_connector(connector_id)

    async def get_connectors_by_internal_brain_id(self, internal_brain_id: str) -> ConnectorListResponse:
        """Retrieve all connectors by internal brain ID."""
        return await self.connector_repository.get_connectors_for_vui(internal_brain_id)

    async def get_connectors_by_params(self, query_params: ConnectorQueryParams) -> ConnectorListResponse:
        """Retrieve connectors by query params"""
        return await self.connector_repository.get_connectors_by_params(query_params)

    async def update_connector(
        self, connector_update: ConnectorUpdate, connector_id: str, internal_brain_id: str | None
    ) -> Connector | None:
        """Asynchronously updates a connector object"""
        update: dict = connector_update.model_dump(exclude_none=True)
        await self._set_and_validate_connector(connector_id)

        if internal_brain_id:
            validate_tenancy(internal_brain_id, self.connector.internal_brain_id)
            # Validate unique name only if name is being updated
            # (there could be an edge-case where the existing name is already a duplicate).
            if "name" in update and update["name"] != self.connector.name:
                await self._validate_unique_name(update["name"], internal_brain_id, connector_id)

        return await self.connector_repository.update_connector(connector_id, internal_brain_id, update)

    async def update_connector_state(
        self, connector_update: ConnectorStateInput, connector_id: str
    ) -> Connector | None:
        """Updates a connector state"""
        await self._set_and_validate_connector(connector_id)

        update = ConnectorStateUpdate(**connector_update.model_dump())
        self.connector.connector_state = update.connector_state
        self.connector.error = update.error

        if (
            update.connector_state == ConnectorState.CONNECTED
            and self.connector.operating_state == OperatingState.INACTIVE
        ):
            self.connector.operating_state = OperatingState.ACTIVE
            self.connector.first_log_received = update.updated_at

        return await self._save()

    async def setup_consent(self, connector_id: str, consent_record: ConsentGrantRecord) -> Connector:
        """Perform Consent Setup Action"""
        await self._set_and_validate_connector(connector_id)

        self.connector.setup_records[SetupAction.CONSENT] = SetupActionState.COMPLETED
        self.connector.properties.update({"ms_tenant_id": consent_record.properties.ms_tenant_id})

        self._update_to_connecting_if_setup_complete()
        return await self._save()

    async def setup_properties(
        self, connector_id: str, internal_brain_id: str, properties: ConnectorPropertiesInput
    ) -> Connector:
        """Perform Properties Setup Action"""
        await self._set_and_validate_connector(connector_id)
        validate_tenancy(internal_brain_id, self.connector.internal_brain_id)

        property_dict = properties.properties.model_dump(exclude={"connector_type"})
        self.connector.properties.update(property_dict)

        self.connector.setup_records[SetupAction.PROPERTIES] = SetupActionState.COMPLETED

        self._update_to_connecting_if_setup_complete()
        return await self._save()

    async def setup_secrets(
        self, connector_id: str, internal_brain_id: str, create_secret_body: ConnectorSecretCreate
    ) -> Connector:
        """Perform Secret Setup Action"""
        await self._set_and_validate_connector(connector_id)
        validate_tenancy(internal_brain_id, self.connector.internal_brain_id)

        await create_secret(connector_id, internal_brain_id, create_secret_body)
        self.connector.setup_records[SetupAction.SECRETS] = SetupActionState.COMPLETED

        self._update_to_connecting_if_setup_complete()
        return await self._save()

    async def update_secret(self, connector_id: str, internal_brain_id: str, secret_body: ConnectorSecretUpdate):
        """Update the values of a Consent secret"""
        await self._set_and_validate_connector(connector_id)
        validate_tenancy(internal_brain_id, self.connector.internal_brain_id)

        if secret_body.secret:
            await update_secret(self.connector.connector_id, internal_brain_id, secret_body)
            self.connector.setup_records[SetupAction.SECRETS] = SetupActionState.COMPLETED

        self._update_to_connecting_if_setup_complete()
        return await self._save()

    async def create_connector(self, connector_input: ConnectorCreate, internal_brain_id: str) -> Connector:
        """Create a new connector object"""
        raise NotImplementedError("This method should be overridden by subclasses.")

    async def soft_delete_connector(self, connector_id: str, internal_brain_id: str):
        """
        Sets a connector to deleted.
        Also does the following:
        - set ms_tenant_id to None if it exists
        - make request to consent to delete connector secret
        """
        await self._set_and_validate_connector(connector_id)
        validate_tenancy(internal_brain_id, self.connector.internal_brain_id)

        self.connector.connector_state = ConnectorState.DELETED
        self.connector.operating_state = OperatingState.INACTIVE

        # remove ms_tenant_id from connector record
        if "ms_tenant_id" in self.connector.properties:
            self.connector.properties["ms_tenant_id"] = None

        # remove resource_group_id from connector record
        if "resource_group_id" in self.connector.properties:
            self.connector.properties["resource_group_id"] = None

        # delete consent service record
        if self.connector.setup_records.get(SetupAction.SECRETS) == SetupActionState.COMPLETED:
            await delete_secret(connector_id, internal_brain_id)

        return await self._save()


class DefenderConnectorController(ConnectorController):
    """MS Defender Controller"""

    connector_type = ConnectorType.DEFENDER

    async def create_connector(self, connector_input: ConnectorCreate, internal_brain_id: str) -> Connector:
        await super()._validate_unique_name(connector_input.name, internal_brain_id)
        additional_args: dict[Any, Any] = (
            {"connector_id": connector_input.connector_id} if connector_input.connector_id else {}
        )
        self.connector = Connector(
            name=connector_input.name,
            connector_type=self.connector_type,
            internal_brain_id=internal_brain_id,
            connector_state=ConnectorState.SETUP,
            operating_state=OperatingState.INACTIVE,
            setup_records={SetupAction.CONSENT: SetupActionState.AWAITING},
            **additional_args,
        )

        consent = await create_consent(self.connector.connector_id, self.connector_type, internal_brain_id)
        self.connector.properties.update({"consent_url": consent.consent_url})  # pylint: disable=no-member

        return await self.connector_repository.persist_connector(self.connector)


class AzureCPConnectorController(ConnectorController):
    """Azure CP Connector Controller"""

    connector_type = ConnectorType.AZURE_CP

    async def create_connector(self, connector_input: ConnectorCreate, internal_brain_id: str) -> Connector:
        await super()._validate_unique_name(connector_input.name, internal_brain_id)
        additional_args: dict[Any, Any] = (
            {"connector_id": connector_input.connector_id} if connector_input.connector_id else {}
        )
        self.connector = Connector(
            name=connector_input.name,
            connector_type=self.connector_type,
            internal_brain_id=internal_brain_id,
            connector_state=ConnectorState.SETUP,
            operating_state=OperatingState.INACTIVE,
            setup_records={
                SetupAction.CONSENT: SetupActionState.AWAITING,
                SetupAction.PROPERTIES: SetupActionState.AWAITING,
            },
            **additional_args,
        )

        consent = await create_consent(self.connector.connector_id, self.connector_type, internal_brain_id)
        self.connector.properties.update({"consent_url": consent.consent_url})  # pylint: disable=no-member

        return await self.connector_repository.persist_connector(self.connector)

    async def setup_properties(
        self, connector_id: str, internal_brain_id: str, properties: ConnectorPropertiesInput
    ) -> Connector:
        """
        Perform Properties Setup Action for Azure CP Connectors.
        """
        await self._set_and_validate_connector(connector_id)

        if self.connector.setup_records.get(SetupAction.CONSENT) != SetupActionState.COMPLETED:
            raise ConsentNotCompletedException()

        return await super().setup_properties(connector_id, internal_brain_id, properties)


class AwsConnectorController(ConnectorController):
    """
    AWS Connector Controller
    redacted connector properties:
        s3_bucket_name=connector_input.s3_bucket_name,
        iam_role_arn=connector_input.iam_arn,
        sns_topic_arn=connector_input.sns_topic_arn,
    """

    connector_type = ConnectorType.AWS

    async def create_connector(self, connector_input: ConnectorCreate, internal_brain_id: str) -> Connector:
        await super()._validate_unique_name(connector_input.name, internal_brain_id)
        additional_args: dict[Any, Any] = (
            {"connector_id": connector_input.connector_id} if connector_input.connector_id else {}
        )
        self.connector = Connector(
            name=connector_input.name,
            connector_type=self.connector_type,
            internal_brain_id=internal_brain_id,
            connector_state=ConnectorState.SETUP,
            operating_state=OperatingState.INACTIVE,
            setup_records={
                SetupAction.PROPERTIES: SetupActionState.AWAITING,
            },
            **additional_args,
        )
        return await self.connector_repository.persist_connector(self.connector)


class CrowdstrikeConnectorController(ConnectorController):
    """Crowdstrike Connector Controller"""

    connector_type = ConnectorType.CROWDSTRIKE

    async def create_connector(self, connector_input: ConnectorCreate, internal_brain_id: str) -> Connector:
        await super()._validate_unique_name(connector_input.name, internal_brain_id)
        additional_args: dict[Any, Any] = (
            {"connector_id": connector_input.connector_id} if connector_input.connector_id else {}
        )
        self.connector = Connector(
            name=connector_input.name,
            connector_type=self.connector_type,
            internal_brain_id=internal_brain_id,
            connector_state=ConnectorState.SETUP,
            operating_state=OperatingState.INACTIVE,
            setup_records={SetupAction.SECRETS: SetupActionState.AWAITING},
            **additional_args,
        )

        return await self.connector_repository.persist_connector(self.connector)

    async def setup_secrets(
        self, connector_id: str, internal_brain_id: str, create_secret_body: ConnectorSecretCreate
    ) -> Connector:
        """Perform Secret Setup Action and persist crowdstrike_url to connector properties"""
        await self._set_and_validate_connector(connector_id)
        crowdstrike_endpoint = cast(CrowdstrikeProperties, create_secret_body.properties)
        self.connector.properties.update({"crowdstrike_url": crowdstrike_endpoint.crowdstrike_url})
        return await super().setup_secrets(connector_id, internal_brain_id, create_secret_body)

    async def update_secret(
        self, connector_id: str, internal_brain_id: str, secret_body: ConnectorSecretUpdate
    ) -> Connector:
        """Update crowdstrike_url if given and call super function update the values of a Consent secret"""
        await self._set_and_validate_connector(connector_id)
        if secret_body.properties:
            crowdstrike_endpoint = cast(CrowdstrikeProperties, secret_body.properties)
            self.connector.properties.update({"crowdstrike_url": crowdstrike_endpoint.crowdstrike_url})
        return await super().update_secret(connector_id, internal_brain_id, secret_body)


class SentinelOneConnectorController(ConnectorController):
    """SentinelOne Connector Controller"""

    connector_type = ConnectorType.SENTINELONE

    async def create_connector(self, connector_input: ConnectorCreate, internal_brain_id: str) -> Connector:
        await super()._validate_unique_name(connector_input.name, internal_brain_id)
        additional_args: dict[Any, Any] = (
            {"connector_id": connector_input.connector_id} if connector_input.connector_id else {}
        )
        self.connector = Connector(
            name=connector_input.name,
            connector_type=self.connector_type,
            internal_brain_id=internal_brain_id,
            connector_state=ConnectorState.SETUP,
            operating_state=OperatingState.INACTIVE,
            setup_records={SetupAction.SECRETS: SetupActionState.AWAITING},
            **additional_args,
        )

        return await self.connector_repository.persist_connector(self.connector)

    async def setup_secrets(
        self, connector_id: str, internal_brain_id: str, create_secret_body: ConnectorSecretCreate
    ) -> Connector:
        """Perform Secret Setup Action and persist sentinelone_url to connector properties"""
        await self._set_and_validate_connector(connector_id)
        sentinel_endpoint = cast(SentinelOneProperties, create_secret_body.properties)
        self.connector.properties.update({"sentinelone_url": sentinel_endpoint.sentinelone_url})
        return await super().setup_secrets(connector_id, internal_brain_id, create_secret_body)

    async def update_secret(
        self, connector_id: str, internal_brain_id: str, secret_body: ConnectorSecretUpdate
    ) -> Connector:
        """Update sentinelOne_url given and call super function update the values of a Consent secret"""
        await self._set_and_validate_connector(connector_id)
        if secret_body.properties:
            sentinel_endpoint = cast(SentinelOneProperties, secret_body.properties)
            self.connector.properties.update({"sentinelone_url": sentinel_endpoint.sentinelone_url})
        return await super().update_secret(connector_id, internal_brain_id, secret_body)
