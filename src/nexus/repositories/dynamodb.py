"""This module defines the persistence layer functionality for interacting with the Nexus Database.

Classes:
- GlobalIndex: Enumerates Global Secondary Index names.
- DynamoDBAdapter: Facilitates CRUD operations and GSI queries on a DynamoDB table.

Link:
- boto3 DynamoDB Table:
    https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/dynamodb/table/index.html
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, cast

import boto3
from boto3.dynamodb.conditions import ConditionBase
from botocore.paginate import TokenDecoder

from nexus.utils.config import config
from nexus.utils.utils import run_in_executor


@dataclass
class DynamoDBUpdateComponents:
    """
    Represents the components needed to perform an update operation in DynamoDB.
    """

    update_expression: str
    expression_attribute_values: dict[str, Any]
    expression_attribute_names: dict[str, str]


@dataclass
class ConditionalExpressionComponents:
    """Components representing a conditional expression"""

    condition_expression: str
    expression_attribute_values: dict[str, Any]


class GlobalIndex(str, Enum):
    """Global index names for Nexus table"""

    OPERATING_STATE_GSI = "operating_state_gsi"
    INTERNAL_BRAIN_ID_GSI = "internal_brain_id_gsi"


class DynamoDBBaseRepository:
    """A DynamoDB adapter class for interacting with a specific DynamoDB table."""

    def __init__(self, table_name: str, page_limit=config.PAGINATION_LIMIT):
        """
        Initializes the adapter for a specified DynamoDB table.

        Parameters:
            table_name: Name of the DynamoDB table.

        Raises:
            DynamoDB.Client.exceptions.TableNotFoundException
        """
        self.client = boto3.resource("dynamodb", endpoint_url=config.DDB_ENDPOINT_URL)
        self.table = self.client.Table(table_name)
        self.page_limit = page_limit

    @staticmethod
    def _generate_dynamodb_update_components(updates: dict) -> DynamoDBUpdateComponents:
        """
        Converts the 'updates' dict into DynamoDB update operation components.
        - update expression: Attributes to be updated, the action to be performed on them, and new values for them
        - expression attribute values: Values that can be substituted in an expression
        - expression attribute names: Substitution tokens for attribute names in an expression

        Example:
            Input:
                updates = {"state": "connected", "setup_records.consent": "completed"}
            Output DynamoDBUpdateComponents values:
                update_expression_parts='SET #setup_records.#consent = :setup_recordsconsent, #state=:state'
                expression_attribute_values={':setup_recordsconsent': 'completed', ':state':'connected'}
                expression_attribute_names={'#state': 'state', '#setup_records':'setup_records', '#consent': 'consent'}
        """
        update_expression_parts = []
        expression_attribute_values = {}
        expression_attribute_names = {}

        for key, value in updates.items():
            placeholder = f":{key.replace('.', '')}"
            update_expression_parts.append(f"#{key.replace('.', '.#')} = {placeholder}")
            expression_attribute_values[placeholder] = value

            nested_keys = key.split(".")
            for part in nested_keys:
                expression_attribute_names[f"#{part}"] = part

        update_expression = "SET " + ", ".join(update_expression_parts)
        return DynamoDBUpdateComponents(update_expression, expression_attribute_values, expression_attribute_names)

    async def put_item(self, item: dict) -> dict:
        """Insert or replace an item in the table asynchronously.

        Parameters:
            item: Item to be inserted or replaced.

        Raises:
            DynamoDB.Client.exceptions.ConditionalCheckFailedException
            DynamoDB.Client.exceptions.ProvisionedThroughputExceededException
            DynamoDB.Client.exceptions.ResourceNotFoundException
            DynamoDB.Client.exceptions.ItemCollectionSizeLimitExceededException
            DynamoDB.Client.exceptions.TransactionConflictException
            DynamoDB.Client.exceptions.RequestLimitExceeded
            DynamoDB.Client.exceptions.InternalServerError
        """
        return await run_in_executor(self.table.put_item, Item=item)

    async def get_item(self, pk: str, sk: str) -> dict:
        """Get item from table asynchronously.

        Parameters:
            pk: The partition key of the item to retrieve.
            sk: The sort key of the item to retrieve.

        Returns:
            The retrieved item, or an empty dict if not found.

        Raises:
            DynamoDB.Client.exceptions.ProvisionedThroughputExceededException
            DynamoDB.Client.exceptions.ResourceNotFoundException
            DynamoDB.Client.exceptions.RequestLimitExceeded
            DynamoDB.Client.exceptions.InternalServerError
        """
        response = await run_in_executor(self.table.get_item, Key={"pk": pk, "sk": sk})
        return response.get("Item", {})

    async def update_item(self, pk: str, sk: str, updates: dict, conditions: ConditionalExpressionComponents | None):
        """
        Updates attributes of an item.
        Note: To update a single attribute in a map object, the dot('.') separation notation is used
        for traversing down the levels of the map. See 'Update map subfield value' example below.

        Parameters:
            pk: The partition key of the item to update.
            sk: The sort key of the item to update.
            updates: A dictionary of values to update
            conditions: An optional conditional expression to include in the request

        Example Inputs for 'updates' dict:
            Update string value: {"state": "connected"}
            Update map value: {"setup_records": {"consent": "awaiting", "user_input": "awaiting"}}
            Update map subfield value: {"setup_records.consent": "completed"}
            Combined: {"state": "connected", "setup_records.consent": "completed"}

        Raises:
            DynamoDB.Client.exceptions.ConditionalCheckFailedException
            DynamoDB.Client.exceptions.ProvisionedThroughputExceededException
            DynamoDB.Client.exceptions.ResourceNotFoundException
            DynamoDB.Client.exceptions.ItemCollectionSizeLimitExceededException
            DynamoDB.Client.exceptions.TransactionConflictException
            DynamoDB.Client.exceptions.RequestLimitExceeded
            DynamoDB.Client.exceptions.InternalServerError
        """
        update_components = self._generate_dynamodb_update_components(updates)

        update_params = {
            "Key": {"pk": pk, "sk": sk},
            "UpdateExpression": update_components.update_expression,
            "ExpressionAttributeNames": update_components.expression_attribute_names,
            "ExpressionAttributeValues": update_components.expression_attribute_values,
            "ReturnValues": "ALL_NEW",
        }
        if conditions:
            update_params["ExpressionAttributeValues"] = {
                **cast(Dict[str, Any], update_params["ExpressionAttributeValues"]),
                **conditions.expression_attribute_values,
            }
            update_params["ConditionExpression"] = conditions.condition_expression
            update_params["ReturnValuesOnConditionCheckFailure"] = "ALL_OLD"

        response = await run_in_executor(self.table.update_item, **update_params)
        return response["Attributes"]

    async def query_by_gsi(
        self,
        index_name: str,
        key_condition_expression: ConditionBase,
        filter_expression: ConditionBase | None = None,
        encoded_start_key: str | None = None,
    ) -> dict:
        """
        Queries items from the table using a specified Global Secondary Index.

        Allows for querying items based on a key condition expression, with an optional
        filter expression to further refine the results.

        Parameters:
            index_name: The name of the Global Secondary Index to query.
            key_condition_expression: A condition that specifies the key values for items to be retrieved by the query.
            filter_expression: An additional condition to filter the results of the query.
            encoded_start_key: Encoded value of the ExclusiveStartKey to be used when retrieving next page results.

        Returns:
            A list of items that match the query conditions.

        Raises:
            DynamoDB.Client.exceptions.ProvisionedThroughputExceededException
            DynamoDB.Client.exceptions.ResourceNotFoundException
            DynamoDB.Client.exceptions.RequestLimitExceeded
            DynamoDB.Client.exceptions.InternalServerError
        """
        params = {"IndexName": index_name, "KeyConditionExpression": key_condition_expression, "Limit": self.page_limit}

        if filter_expression:
            params["FilterExpression"] = filter_expression

        if encoded_start_key:
            params["ExclusiveStartKey"] = TokenDecoder().decode(encoded_start_key)

        return await run_in_executor(self.table.query, **params)

    async def scan_table(
        self,
        filter_expression: ConditionBase | None = None,
        encoded_start_key: str | None = None,
    ) -> dict:
        """
        Scans the table with an optional filter expression.

        Parameters:
            filter_expression: An optional condition to filter the results
            encoded_start_key: Encoded value of ExclusiveStartKey for pagination

        Returns:
            A dict containing Items and LastEvaluatedKey if more results exist
        """
        params = {"Limit": self.page_limit}

        if filter_expression:
            params["FilterExpression"] = filter_expression

        if encoded_start_key:
            params["ExclusiveStartKey"] = TokenDecoder().decode(encoded_start_key)

        return await run_in_executor(self.table.scan, **params)
