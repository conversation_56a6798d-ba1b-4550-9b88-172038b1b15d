"""Connector Repository"""
from functools import reduce

from boto3.dynamodb.conditions import And, Attr, Key
from botocore.exceptions import ClientError

from nexus.api.exceptions import ForbiddenException, NotFoundException
from nexus.models import Connector, OperatingState
from nexus.repositories.dynamodb import ConditionalExpressionCom<PERSON>, DynamoDBBaseRepository, GlobalIndex
from nexus.schemas import ConnectorListResponse, ConnectorQueryParams

CONNECTOR_SK = "connector"


class ConnectorRepository(DynamoDBBaseRepository):
    """Connector Repository Class"""

    @staticmethod
    def _get_key_condition_expression(query_params: ConnectorQueryParams) -> And | Key:
        """Builds the key condition expression for DynamoDB queries."""
        operating_state = query_params.operating_state or OperatingState.ACTIVE
        primary_key = Key("operating_state").eq(operating_state)

        if query_params.connector_type and query_params.connector_state:
            sort_key = Key("data").eq(f"{query_params.connector_type}#{query_params.connector_state}")
        elif query_params.connector_type:
            sort_key = Key("data").begins_with(query_params.connector_type)
        else:
            sort_key = None
        return primary_key & sort_key if sort_key else primary_key

    @staticmethod
    def _get_filter_expression(query_params: ConnectorQueryParams, include_base_filters: bool = True) -> And:
        """
        Builds the filter expression for DynamoDB queries.

        Args:
            query_params: The query parameters to build filters from
            include_base_filters: Whether to include connector_type and connector_state filters
        """
        filters = [Attr("sk").eq(CONNECTOR_SK)]

        if query_params.properties:
            property_filters = [
                Attr(f"properties.{key}").eq(value)
                for key, value in query_params.properties.items()  # type: ignore[attr-defined]
            ]
            filters.extend(property_filters)

        if include_base_filters and not query_params.operating_state:
            if query_params.connector_type:
                filters.append(Attr("connector_type").eq(query_params.connector_type))
            if query_params.connector_state:
                filters.append(Attr("connector_state").eq(query_params.connector_state))

        return reduce(lambda x, y: x & y, filters)

    async def persist_connector(self, connector: Connector) -> Connector:
        """Persist a connector object asynchronously"""
        await self.put_item(connector.model_dump(by_alias=True))
        return connector

    async def get_connector(self, connector_id: str) -> Connector:
        """Get a connector asynchronously"""
        connector = await self.get_item(pk=connector_id, sk=CONNECTOR_SK)
        if not connector:
            raise NotFoundException(detail=f"Connector {connector_id} cannot be found")
        return Connector(**connector)

    async def get_connectors_for_vui(self, internal_brain_id: str) -> ConnectorListResponse:
        """Get all non-deleted connectors asynchronously"""
        response = await self.query_by_gsi(
            index_name=GlobalIndex.INTERNAL_BRAIN_ID_GSI,
            key_condition_expression=Key("internal_brain_id").eq(internal_brain_id),
            filter_expression=Attr("connector_state").ne("deleted"),
        )
        return ConnectorListResponse(**response)

    async def get_connectors_by_params(self, query_params: ConnectorQueryParams) -> ConnectorListResponse:
        """Get connectors based on provided query params asynchronously"""
        if query_params.operating_state:
            response = await self.query_by_gsi(
                index_name=GlobalIndex.OPERATING_STATE_GSI,
                key_condition_expression=self._get_key_condition_expression(query_params),
                filter_expression=self._get_filter_expression(
                    query_params, include_base_filters=False
                ),  # type: ignore[arg-type]
                encoded_start_key=query_params.next_token,
            )
            return ConnectorListResponse(**response)

        response = await self.scan_table(
            filter_expression=self._get_filter_expression(query_params),  # type: ignore[arg-type]
            encoded_start_key=query_params.next_token,
        )

        return ConnectorListResponse(**response)

    async def update_connector(
        self,
        connector_id: str,
        internal_brain_id: str | None,
        updates: dict,
    ):
        """Update a connector object asynchronously"""

        conditions = None
        if internal_brain_id:
            conditions = ConditionalExpressionComponents(
                condition_expression="attribute_exists(pk) AND internal_brain_id = :internal_brain_id",
                expression_attribute_values={":internal_brain_id": f"{internal_brain_id}"},
            )

        try:
            connector = await self.update_item(pk=connector_id, sk=CONNECTOR_SK, updates=updates, conditions=conditions)
            return Connector(**connector)
        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                if not e.response.get("Item"):  # If Item never existed, throw NotFoundException
                    raise NotFoundException() from e
                raise ForbiddenException() from e
        return None

    async def get_non_deleted_connector_by_name_and_brain(
        self, name: str, internal_brain_id: str, exclude_connector_id: str | None = None
    ) -> ConnectorListResponse:
        """Query active (non-deleted) connector by name and brain ID asynchronously"""
        filter_expression = Attr("name").eq(name) & Attr("connector_state").ne("deleted")

        if exclude_connector_id:
            filter_expression = filter_expression & Attr("pk").ne(exclude_connector_id)

        response = await self.query_by_gsi(
            index_name=GlobalIndex.INTERNAL_BRAIN_ID_GSI,
            key_condition_expression=Key("internal_brain_id").eq(internal_brain_id),
            filter_expression=filter_expression,
        )
        return ConnectorListResponse(**response)
