"""Context management module"""

from contextvars import ContextVar
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class RequestContext(BaseModel):
    """
    Class to manage context throughout the lifecycle of a single request.
    """

    request_id: UUID = Field(default_factory=uuid4)
    path: str | None = None
    method: str | None = None
    status_code: int | None = None
    vectra_service: str | None = None
    internal_brain_id: str | None = None
    connector_id: str | None = None
    connector_type: str | None = None
    error_class: str | None = None

    def update(self, **kwargs) -> None:
        """
        Updates attributes with the provided kwargs.
        """
        for key, value in kwargs.items():
            setattr(self, key, value)


request_context: ContextVar["RequestContext"] = ContextVar("request_context", default=RequestContext())
