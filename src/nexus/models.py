# pylint: disable= too-many-instance-attributes

"""
Application Models for Nexus
"""
from datetime import datetime, timezone
from enum import Enum
from typing import Literal
from uuid import UUID, uuid4

from pydantic import BaseModel, ConfigDict, Field, computed_field, field_validator

from nexus.context import request_context


def get_current_time() -> str:
    """
    Returns current datetime in UTC.
    Formatted to ISO8601 with Zulu notation.
    """
    return datetime.now(timezone.utc).isoformat(timespec="seconds").replace("+00:00", "Z")


class CustomBaseModel(BaseModel):
    """Custom Base model placeholder"""


class BaseEnum(str, Enum):
    """Base methods for all Enums"""

    def __str__(self):
        return str(self.value)

    def __repr__(self):
        return str(self)


class VectraService(BaseEnum):
    """Enum model for values expected in the x-vectra-service header"""

    LOGFLOW = "logflow"
    CONSENT = "consent"
    VUI = "vui"


class ConnectorState(BaseEnum):
    """Enum model for connector state"""

    SETUP = "setup"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    DELETED = "deleted"


class ConnectorType(BaseEnum):
    """Connector Type model"""

    AZURE_CP = "azure-cp"
    AWS = "aws"
    # O365 = "o365"
    CROWDSTRIKE = "crowdstrike"
    DEFENDER = "defender"
    SENTINELONE = "sentinelone"


class OperatingState(BaseEnum):
    """Operating State model"""

    ACTIVE = "active"
    INACTIVE = "inactive"


class SetupAction(BaseEnum):
    """Setup Action Enums"""

    CONSENT = "consent"
    PROPERTIES = "properties"
    SECRETS = "secrets"


class ConnectorSize(BaseEnum):
    """Connector 'T-shirt size' Enums"""

    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"


class SetupActionState(BaseEnum):
    """Setup Action State Enums"""

    AWAITING = "awaiting"
    COMPLETED = "completed"


class ConnectorError(CustomBaseModel):
    """Error Model for error messages and codes"""

    error_code: str
    error_message: str


class Connector(CustomBaseModel):
    """Connector Application Model"""

    model_config = ConfigDict(populate_by_name=True)

    name: str
    connector_type: ConnectorType
    connector_id: str = Field(alias="pk", default_factory=lambda: str(uuid4()))
    connector_state: ConnectorState
    operating_state: OperatingState
    internal_brain_id: str
    setup_records: dict
    created_at: str = Field(default_factory=get_current_time)
    updated_at: str = Field(default_factory=get_current_time)
    properties: dict = Field(default_factory=dict)
    error: ConnectorError | None = Field(default=None)
    first_log_received: str | None = Field(default=None)
    last_log_received: str | None = Field(default=None)
    size: ConnectorSize = Field(default=ConnectorSize.MEDIUM)
    last_sequence_id: str | None = Field(default=None)

    attribute_type: str = Field(default="connector", alias="sk")

    @field_validator("connector_id", mode="before")
    @classmethod
    def convert_uuid_to_str(cls, connector_id: UUID | str) -> str:
        """Convert UUID to string if needed"""
        if isinstance(connector_id, UUID):
            return str(connector_id)
        return connector_id

    @computed_field()
    def data(self) -> str:
        """Computes the data compound field used within the persistence layer"""

        return f"{self.connector_type}#{self.connector_state}"


class ConnectorStateUpdate(BaseModel):
    """Connector Update Model"""

    connector_state: Literal[ConnectorState.CONNECTED, ConnectorState.ERROR]
    error: ConnectorError | None = Field(default=None)
    updated_at: str = Field(default_factory=get_current_time)


class ConnectorDelete(BaseModel):
    """Connector Delete Model"""

    updated_at: str = Field(default_factory=get_current_time)
    connector_state: ConnectorState = Field(default=ConnectorState.DELETED)
    operating_state: OperatingState = Field(default=OperatingState.INACTIVE)


class NexusEvent(BaseModel):
    """Nexus Event model"""

    created_at: str = Field(default_factory=get_current_time)
    connector_id: str
    connector_state: ConnectorState
    connector_type: ConnectorType
    internal_brain_id: str
    request_context: dict = Field(default_factory=request_context.get().model_dump)
