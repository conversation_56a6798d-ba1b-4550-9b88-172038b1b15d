"""
Nexus App Entrypoint
"""

import asyncio
import re
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.exceptions import RequestValidationError
from fastapi.openapi.utils import get_openapi
from fastapi.routing import APIRoute

from nexus.api.exceptions import CustomException, custom_exception_handler, validation_exception_handler
from nexus.api.v1 import connectors, setup
from nexus.middleware.metrics import MetricsMiddleware
from nexus.services.notifications import Notification<PERSON>anager


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """
    Defines this startup and shutdown logic of the app.
    https://fastapi.tiangolo.com/advanced/events/#lifespan
    """
    notification_manager = NotificationManager()
    asyncio.create_task(notification_manager.run())
    yield
    await notification_manager.queue.join()


app = FastAPI(title="Nexus", description="Connector Management Service", lifespan=lifespan)

# add routes
app.include_router(connectors.router, prefix="/v1", tags=["connectors"])
app.include_router(setup.router, prefix="/v1", tags=["setup"])

# add exception handlers
app.add_exception_handler(RequestValidationError, validation_exception_handler)  # type: ignore[arg-type]
app.add_exception_handler(CustomException, custom_exception_handler)  # type: ignore[arg-type]

# add custom middleware
app.add_middleware(MetricsMiddleware)


def custom_openapi():
    """Set custom Open API definition.
    Used to set the open_api version to be compatible with the Open API Client generator
    """
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        openapi_version="3.0.1",
        title="Nexus",
        description="Connector Management Service",
        routes=app.routes,
        version="1.0.0",
    )
    app.openapi_schema = openapi_schema

    return app.openapi_schema


app.openapi = custom_openapi  # type: ignore[method-assign]


@app.get("/")
def root():
    """Root Endpoint"""
    return {"Health": "OK"}


def use_route_names_as_operation_ids(_app: FastAPI) -> None:
    """
    Simplify operation IDs so that generated API clients have simpler function names.
    https://fastapi.tiangolo.com/advanced/path-operation-advanced-configuration
    """

    operation_ids = set()

    for route in _app.routes:
        if isinstance(route, APIRoute):
            # Check if there is a version in the route path like 'v1'
            version_match = re.search(r"v\d+\.?\d*", route.path)
            if version_match:
                version = version_match.group()
                operation_id = f"{route.name}_{version}"
            else:
                operation_id = route.name

            if operation_id in operation_ids:
                raise ValueError(f"Duplicate operation ID found: {operation_id}")

            route.operation_id = operation_id
            operation_ids.add(operation_id)


use_route_names_as_operation_ids(app)
