"""Nexus internal notification manager and event queue"""
import asyncio

from botocore.exceptions import ClientError
from vectra.opentelemetry_connector import Metrics
from vectra.vui_logger import logger

from nexus.middleware.metrics import MetricName, normalize
from nexus.models import Connector, ConnectorState, NexusEvent
from nexus.utils.sns import publish_nexus_event


class NotificationManager:
    """Nexus internal notification manager with singleton async queue"""

    queue: asyncio.Queue = asyncio.Queue()

    @classmethod
    async def create_event(cls, connector: Connector):
        """Create event and add to internal async queue"""
        if connector.connector_state == ConnectorState.CONNECTING:
            event = NexusEvent(**connector.model_dump())
            await cls.queue.put(event)

    @classmethod
    async def run(cls):
        """Get next event from internal queue and create task to process event"""
        while True:
            event = await cls.queue.get()
            asyncio.create_task(cls.process_event(event))

    @classmethod
    async def process_event(cls, event: NexusEvent):
        """
        Attempt to publish nexus event to sns topic.
        If exception, wait temporarily and then put back in the queue.
        """
        success = True
        try:
            await publish_nexus_event(event)
        except ClientError as e:
            success = False
            event.request_context.update(error_class=e.__class__.__name__)
            logger.exception("Failed to publish Nexus event to SNS topic", extra={**event.model_dump(), "error": e})
            await asyncio.sleep(5)
            await cls.queue.put(event)
        finally:
            Metrics.timing(name=MetricName.NEXUS_EVENT, value=float(success), labels=normalize(event.request_context))
            cls.queue.task_done()
