"""Responsible for any interactions with the consent service"""

from http import H<PERSON><PERSON>tat<PERSON>

from vectra.consent_service_client import ConsentClient
from vectra.consent_service_client.client import get_role_based_credentials
from vectra.consent_service_client.models import (
    ConsentUrlRequest,
    ConsentUrlResponse,
    NamedSecretModel,
    NamedSecretUpdateBody,
    SecretsCreateBody,
)
from vectra.vui_logger import logger

from nexus.api.exceptions import BadGatewayException
from nexus.context import request_context
from nexus.models import ConnectorType
from nexus.schemas import ConnectorSecretCreate, ConnectorSecretUpdate
from nexus.utils.config import config
from nexus.utils.utils import mask_dict_values, run_in_executor

_client = ConsentClient(
    url=config.CONSENT_INTERNAL_URL,
    region=config.REGION,
    creds=get_role_based_credentials(
        role_arn=config.CONSENT_INTERNAL_DELETE_ROLE_ARN, role_session_name="nexus_auth_session"
    ),
)


async def create_consent(
    connector_id: str, connector_type: ConnectorType, internal_brain_id: str
) -> ConsentUrlResponse:
    """Create a new consent"""

    body = ConsentUrlRequest(consent_type=connector_type)
    logger.info(
        "Consent request to create url",
        extra={
            **request_context.get().model_dump(),
            "consent_request": body.model_dump(),
        },
    )
    try:
        return await run_in_executor(_client.post_consent_url, body, internal_brain_id, connector_id)
    except RuntimeError as e:
        raise BadGatewayException() from e


def get_fields_for_secret_type(connector_type: str) -> list[str]:
    """Get the required secret fields based on connector type"""
    if connector_type == ConnectorType.SENTINELONE.value:
        return ["api_token"]
    return ["client_id", "client_secret"]


async def create_secret(connector_id: str, internal_brain_id: str, create_secret_body: ConnectorSecretCreate) -> None:
    """Create a new secret"""
    named_secret = NamedSecretModel(**create_secret_body.secret.model_dump(by_alias=True))
    secret_dict = {
        "external-id": connector_id,
        "secret-type": create_secret_body.connector_type,
        "secrets": [named_secret],
    }
    logger.info(
        "Consent request to create secret",
        extra={
            **request_context.get().model_dump(),
            "named_secret": mask_dict_values(
                named_secret.model_dump(), keys_to_mask=get_fields_for_secret_type(create_secret_body.connector_type)
            ),
            "secret_type": create_secret_body.connector_type,
        },
    )
    try:
        return await run_in_executor(_client.create_secret, SecretsCreateBody(**secret_dict), internal_brain_id)
    except RuntimeError as e:
        cause = getattr(e, "__cause__", None)
        response = getattr(cause, "response", None)
        status_code = getattr(response, "status_code", None)

        if status_code == HTTPStatus.CONFLICT:
            logger.info(
                "Secret already exists",
                extra={
                    **request_context.get().model_dump(),
                    "connector_id": connector_id,
                    "connector_type": create_secret_body.connector_type,
                },
            )
            return None
        raise BadGatewayException() from e


async def update_secret(connector_id: str, internal_brain_id: str, update_secret_body: ConnectorSecretUpdate) -> None:
    """Update the value(s) of an existing Consent secret"""
    secret_values = update_secret_body.secret.model_dump(by_alias=True)
    named_secret = NamedSecretUpdateBody(**secret_values)
    logger.info(
        "Consent request to update secret",
        extra={
            **request_context.get().model_dump(),
            "named_secret": mask_dict_values(
                named_secret.model_dump(), keys_to_mask=get_fields_for_secret_type(update_secret_body.connector_type)
            ),
        },
    )
    try:
        return await run_in_executor(
            _client.patch_named_secret,
            named_secret,
            connector_id,
            update_secret_body.secret.secret_name,
            internal_brain_id,
        )
    except RuntimeError as e:
        raise BadGatewayException() from e


async def delete_secret(connector_id: str, internal_brain_id: str) -> None:
    """Delete connector secrets"""
    logger.info("Consent request to delete secret", extra={**request_context.get().model_dump()})
    try:
        return await run_in_executor(_client.delete_secret, connector_id, internal_brain_id)
    except RuntimeError as e:
        if "resource_not_found" in str(e):
            request_context.get().update(error_class="ConsentResourceNotFound")
            logger.warning(
                "Consent request to delete secret failed, secret already deleted or does not exist",
                extra={
                    **request_context.get().model_dump(),
                },
            )
            return None

        raise e
