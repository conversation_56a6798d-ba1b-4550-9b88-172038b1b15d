"""Metrics module"""

import re
import time
from enum import Enum
from typing import Any

from starlette.middleware.base import BaseHTTPMiddleware
from vectra.opentelemetry_connector import Metrics
from vectra.vui_logger import logger

from nexus.context import RequestContext, request_context

Metrics.configure(logger=logger)

# 76d6d891-7e5d-47c8-a078-666044c0bbdb
CONNECTOR_SID_MATCHER = r"connectors/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})(?:/|$)"


class MetricName(Enum):
    """Metric type names"""

    RUNTIME = "nexus_runtime"
    NEXUS_EVENT = "nexus_event"


def extract_connector_id(url: str) -> str | None:
    """
    Extract the connector_id from a given URL.

    Example:
           input: /v1/connectors/76d6d891-7e5d-47c8-a078-666044c0bbdb/setup/secrets
           output: 76d6d891-7e5d-47c8-a078-666044c0bbdb
    """
    source_id = None
    regex = re.compile(CONNECTOR_SID_MATCHER, re.I)
    raw_source_id = re.search(regex, url)
    if raw_source_id:
        source_id = raw_source_id.group(1)
    return source_id


def normalize_url(url: str) -> str:
    """
    Normalize a URL by replacing any connector_id with '***'.

    Example:
        input: /v1/connectors/76d6d891-7e5d-47c8-a078-666044c0bbdb/setup/secrets
        output: /v1/connectors/***/setup/secrets
    """
    connector_source_id = extract_connector_id(url)
    if connector_source_id:
        url = re.sub(connector_source_id, r"***", url)
    return url


def normalize(obj: Any) -> str | int | float | dict | None:
    """
    Normalize any object to a string, number, None or dict value.
    For str:
     - if a only consists of -`'" and spaces then return None
     - delete all `'" characters
     - replace all ,|: with spaces
     - reduces the length of the string to 64 characters
    For dict
     - all the values are normalized recursively
    For int/float
     - return the value as is
    Anything else
     - cast the object to a string
    """
    if isinstance(obj, str):
        if re.match(r"[-`'\"|:\s]+", obj) or obj == "":  # pylint: disable=anomalous-backslash-in-string
            return None
        obj = re.sub(r"[`'\"]+", "", obj)
        obj = re.sub(r"[,|:]+", " ", obj)
        return obj[:64]
    if isinstance(obj, dict):
        for key, value in obj.items():
            obj[key] = normalize(value)
        return obj
    if isinstance(obj, (int, float)):
        return obj
    return normalize(str(obj))


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware for capturing and logging request metrics.
    """

    # pylint: disable=too-few-public-methods
    async def dispatch(self, request, call_next):
        token = request_context.set(RequestContext())

        start_time = time.perf_counter()
        ctx_values = {
            "path": normalize_url(request.url.path),
            "method": request.method,
            "vectra_service": request.headers.get("x-vectra-service"),
            "internal_brain_id": request.headers.get("x-internal-brain-id"),
            "connector_id": extract_connector_id(request.url.path),
        }
        request_context.get().update(**ctx_values)
        try:
            response = await call_next(request)
            request_context.get().update(status_code=response.status_code)
            return response
        except Exception as e:
            msg = e.__class__.__name__
            logger.exception(msg)
            request_context.get().update(error_class=msg)
            if not request_context.get().status_code:
                request_context.get().update(status_code=500)
            raise e
        finally:
            run_time = round(time.perf_counter() - start_time, 4)
            Metrics.timing(
                name=MetricName.RUNTIME, value=run_time, labels=normalize(request_context.get().model_dump())
            )
            request_context.reset(token)
