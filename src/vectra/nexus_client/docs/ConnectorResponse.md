# ConnectorResponse

Connector Response

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | Connector Name | 
**connector_type** | [**ConnectorType**](ConnectorType.md) | Connector Type | 
**connector_id** | **str** | Connector ID | 
**connector_state** | [**ConnectorState**](ConnectorState.md) | Connector State | 
**operating_state** | [**OperatingState**](OperatingState.md) | Connector State | 
**internal_brain_id** | **str** | Brain ID of the tenant | 
**setup_records** | **object** | Setup records and their state associated with the connector | 
**created_at** | **str** | The date the connector was first created | 
**updated_at** | **str** | The date the connector was last modified | 
**properties** | **object** | The properties associated with the connector | 
**error** | [**ConnectorError**](ConnectorError.md) |  | 
**first_log_received** | **str** |  | [optional] 
**last_log_received** | **str** |  | 
**size** | [**ConnectorSize**](ConnectorSize.md) | Connector Size | [optional] 
**last_sequence_id** | **str** |  | [optional] 

## Example

```python
from vectra.nexus_client.models.connector_response import ConnectorResponse

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorResponse from a JSON string
connector_response_instance = ConnectorResponse.from_json(json)
# print the JSON string representation of the object
print(ConnectorResponse.to_json())

# convert the object into a dict
connector_response_dict = connector_response_instance.to_dict()
# create an instance of ConnectorResponse from a dict
connector_response_from_dict = ConnectorResponse.from_dict(connector_response_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


