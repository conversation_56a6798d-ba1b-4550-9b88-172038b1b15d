# ConnectorSecretCreate

Request body for connectors that consent using an api secret Note:     properties has a default=None as it may not always be required

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | **str** |  | 
**secret** | [**Secret**](Secret.md) |  | 
**properties** | [**Properties**](Properties.md) |  | [optional] 

## Example

```python
from vectra.nexus_client.models.connector_secret_create import ConnectorSecretCreate

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorSecretCreate from a JSON string
connector_secret_create_instance = ConnectorSecretCreate.from_json(json)
# print the JSON string representation of the object
print(ConnectorSecretCreate.to_json())

# convert the object into a dict
connector_secret_create_dict = connector_secret_create_instance.to_dict()
# create an instance of ConnectorSecretCreate from a dict
connector_secret_create_from_dict = ConnectorSecretCreate.from_dict(connector_secret_create_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


