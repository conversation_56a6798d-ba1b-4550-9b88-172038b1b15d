# ConnectorDeleteInput

Request body model when deleting an existing connector

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | [**ConnectorType**](ConnectorType.md) | Connector Type | 

## Example

```python
from vectra.nexus_client.models.connector_delete_input import ConnectorDeleteInput

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorDeleteInput from a JSON string
connector_delete_input_instance = ConnectorDeleteInput.from_json(json)
# print the JSON string representation of the object
print(ConnectorDeleteInput.to_json())

# convert the object into a dict
connector_delete_input_dict = connector_delete_input_instance.to_dict()
# create an instance of ConnectorDeleteInput from a dict
connector_delete_input_from_dict = ConnectorDeleteInput.from_dict(connector_delete_input_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


