# ConnectorStateError

Request body to update connector to 'error'

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_state** | **str** | Connector is in error state | 
**error** | [**ConnectorError**](ConnectorError.md) | The error message associated with the connector | 

## Example

```python
from vectra.nexus_client.models.connector_state_error import ConnectorStateError

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorStateError from a JSON string
connector_state_error_instance = ConnectorStateError.from_json(json)
# print the JSON string representation of the object
print(ConnectorStateError.to_json())

# convert the object into a dict
connector_state_error_dict = connector_state_error_instance.to_dict()
# create an instance of ConnectorStateError from a dict
connector_state_error_from_dict = ConnectorStateError.from_dict(connector_state_error_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


