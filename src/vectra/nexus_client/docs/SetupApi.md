# vectra.nexus_client.SetupApi

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**setup_consent_v1**](SetupApi.md#setup_consent_v1) | **POST** /v1/connectors/{connector_id}/setup/consent | Setup Consent
[**setup_properties_v1**](SetupApi.md#setup_properties_v1) | **POST** /v1/connectors/{connector_id}/setup/properties | Setup Properties
[**setup_secrets_v1**](SetupApi.md#setup_secrets_v1) | **POST** /v1/connectors/{connector_id}/setup/secrets | Setup Secrets
[**update_secret_v1**](SetupApi.md#update_secret_v1) | **PATCH** /v1/connectors/{connector_id}/setup/secrets | Update Secret


# **setup_consent_v1**
> ConnectorResponse setup_consent_v1(connector_id, x_vectra_service, consent_grant_record)

Setup Consent

Completes the consent setup action on a connector

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.models.consent_grant_record import ConsentGrantRecord
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.SetupApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    consent_grant_record = vectra.nexus_client.ConsentGrantRecord() # ConsentGrantRecord | 

    try:
        # Setup Consent
        api_response = api_instance.setup_consent_v1(connector_id, x_vectra_service, consent_grant_record)
        print("The response of SetupApi->setup_consent_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SetupApi->setup_consent_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_vectra_service** | **str**|  | 
 **consent_grant_record** | [**ConsentGrantRecord**](ConsentGrantRecord.md)|  | 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **setup_properties_v1**
> ConnectorResponse setup_properties_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_properties_input)

Setup Properties

Completes the properties setup action on a connector

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_properties_input import ConnectorPropertiesInput
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.SetupApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_internal_brain_id = 'x_internal_brain_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    connector_properties_input = vectra.nexus_client.ConnectorPropertiesInput() # ConnectorPropertiesInput | 

    try:
        # Setup Properties
        api_response = api_instance.setup_properties_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_properties_input)
        print("The response of SetupApi->setup_properties_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SetupApi->setup_properties_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_internal_brain_id** | **str**|  | 
 **x_vectra_service** | **str**|  | 
 **connector_properties_input** | [**ConnectorPropertiesInput**](ConnectorPropertiesInput.md)|  | 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **setup_secrets_v1**
> ConnectorResponse setup_secrets_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_secret_create)

Setup Secrets

Completes the consent setup action on a connector As part of this setup, Nexus will make a request to Consent to create a named secret record for a connector

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.models.connector_secret_create import ConnectorSecretCreate
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.SetupApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_internal_brain_id = 'x_internal_brain_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    connector_secret_create = vectra.nexus_client.ConnectorSecretCreate() # ConnectorSecretCreate | 

    try:
        # Setup Secrets
        api_response = api_instance.setup_secrets_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_secret_create)
        print("The response of SetupApi->setup_secrets_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SetupApi->setup_secrets_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_internal_brain_id** | **str**|  | 
 **x_vectra_service** | **str**|  | 
 **connector_secret_create** | [**ConnectorSecretCreate**](ConnectorSecretCreate.md)|  | 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **update_secret_v1**
> ConnectorResponse update_secret_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_secret_update)

Update Secret

Update the consent values of a connector.

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.models.connector_secret_update import ConnectorSecretUpdate
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.SetupApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_internal_brain_id = 'x_internal_brain_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    connector_secret_update = vectra.nexus_client.ConnectorSecretUpdate() # ConnectorSecretUpdate | 

    try:
        # Update Secret
        api_response = api_instance.update_secret_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_secret_update)
        print("The response of SetupApi->update_secret_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SetupApi->update_secret_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_internal_brain_id** | **str**|  | 
 **x_vectra_service** | **str**|  | 
 **connector_secret_update** | [**ConnectorSecretUpdate**](ConnectorSecretUpdate.md)|  | 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

