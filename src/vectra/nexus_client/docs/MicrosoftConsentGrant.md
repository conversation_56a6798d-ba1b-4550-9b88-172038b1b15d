# MicrosoftConsentGrant

Consent Grant Model representing a Microsoft type consent

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | **str** |  | 
**ms_tenant_id** | **str** | Microsoft Tenant ID for the consenting tenant | 

## Example

```python
from vectra.nexus_client.models.microsoft_consent_grant import MicrosoftConsentGrant

# TODO update the JSON string below
json = "{}"
# create an instance of MicrosoftConsentGrant from a JSON string
microsoft_consent_grant_instance = MicrosoftConsentGrant.from_json(json)
# print the JSON string representation of the object
print(MicrosoftConsentGrant.to_json())

# convert the object into a dict
microsoft_consent_grant_dict = microsoft_consent_grant_instance.to_dict()
# create an instance of MicrosoftConsentGrant from a dict
microsoft_consent_grant_from_dict = MicrosoftConsentGrant.from_dict(microsoft_consent_grant_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


