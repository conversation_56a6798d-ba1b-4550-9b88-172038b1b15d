# ConnectorCreate

Request body model when creating a new connector

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** | Connector Name | 
**connector_type** | [**ConnectorType**](ConnectorType.md) | Connector Type | 
**connector_id** | **str** |  | [optional] 

## Example

```python
from vectra.nexus_client.models.connector_create import ConnectorCreate

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorCreate from a JSON string
connector_create_instance = ConnectorCreate.from_json(json)
# print the JSON string representation of the object
print(ConnectorCreate.to_json())

# convert the object into a dict
connector_create_dict = connector_create_instance.to_dict()
# create an instance of ConnectorCreate from a dict
connector_create_from_dict = ConnectorCreate.from_dict(connector_create_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


