# ConsentGrantRecord

Request body model when completing a consent setup action

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**properties** | [**ConsentGrantProperties**](ConsentGrantProperties.md) |  | 

## Example

```python
from vectra.nexus_client.models.consent_grant_record import ConsentGrantRecord

# TODO update the JSON string below
json = "{}"
# create an instance of ConsentGrantRecord from a JSON string
consent_grant_record_instance = ConsentGrantRecord.from_json(json)
# print the JSON string representation of the object
print(ConsentGrantRecord.to_json())

# convert the object into a dict
consent_grant_record_dict = consent_grant_record_instance.to_dict()
# create an instance of ConsentGrantRecord from a dict
consent_grant_record_from_dict = ConsentGrantRecord.from_dict(consent_grant_record_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


