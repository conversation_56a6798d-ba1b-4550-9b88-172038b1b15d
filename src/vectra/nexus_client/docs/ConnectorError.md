# ConnectorError

Error Model for error messages and codes

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**error_code** | **str** |  | 
**error_message** | **str** |  | 

## Example

```python
from vectra.nexus_client.models.connector_error import ConnectorError

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorError from a JSON string
connector_error_instance = ConnectorError.from_json(json)
# print the JSON string representation of the object
print(ConnectorError.to_json())

# convert the object into a dict
connector_error_dict = connector_error_instance.to_dict()
# create an instance of ConnectorError from a dict
connector_error_from_dict = ConnectorError.from_dict(connector_error_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


