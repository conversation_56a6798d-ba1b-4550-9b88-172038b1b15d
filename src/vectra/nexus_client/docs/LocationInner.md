# LocationInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------

## Example

```python
from vectra.nexus_client.models.location_inner import LocationInner

# TODO update the JSON string below
json = "{}"
# create an instance of LocationInner from a JSON string
location_inner_instance = LocationInner.from_json(json)
# print the JSON string representation of the object
print(LocationInner.to_json())

# convert the object into a dict
location_inner_dict = location_inner_instance.to_dict()
# create an instance of LocationInner from a dict
location_inner_from_dict = LocationInner.from_dict(location_inner_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


