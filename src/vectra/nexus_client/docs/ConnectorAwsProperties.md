# ConnectorAwsProperties

Model to update AWS Connector Properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | **str** |  | 
**s3_bucket_name** | **str** | Cloudtrail S3 Bucket Name | 
**iam_role_arn** | **str** | S3 Bucket ARN | 
**sns_topic_arn** | **str** | SNS topic ARN | 

## Example

```python
from vectra.nexus_client.models.connector_aws_properties import ConnectorAwsProperties

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorAwsProperties from a JSON string
connector_aws_properties_instance = ConnectorAwsProperties.from_json(json)
# print the JSON string representation of the object
print(ConnectorAwsProperties.to_json())

# convert the object into a dict
connector_aws_properties_dict = connector_aws_properties_instance.to_dict()
# create an instance of ConnectorAwsProperties from a dict
connector_aws_properties_from_dict = ConnectorAwsProperties.from_dict(connector_aws_properties_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


