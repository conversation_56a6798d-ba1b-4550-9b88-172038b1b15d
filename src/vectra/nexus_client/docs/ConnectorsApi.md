# vectra.nexus_client.ConnectorsApi

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**create_connector_v1**](ConnectorsApi.md#create_connector_v1) | **POST** /v1/connectors | Create Connector
[**delete_connector_v1**](ConnectorsApi.md#delete_connector_v1) | **DELETE** /v1/connectors/{connector_id} | Delete Connector
[**get_connector_v1**](ConnectorsApi.md#get_connector_v1) | **GET** /v1/connectors/{connector_id} | Get Connector
[**get_connectors_v1**](ConnectorsApi.md#get_connectors_v1) | **GET** /v1/connectors | Get Connectors
[**patch_connector_v1**](ConnectorsApi.md#patch_connector_v1) | **PATCH** /v1/connectors/{connector_id} | Patch Connector
[**update_connector_state_v1**](ConnectorsApi.md#update_connector_state_v1) | **POST** /v1/connectors/{connector_id}/state | Update Connector State


# **create_connector_v1**
> ConnectorResponse create_connector_v1(x_internal_brain_id, x_vectra_service, connector_create)

Create Connector

Creates a new connector in the system of the specified type

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_create import ConnectorCreate
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.ConnectorsApi(api_client)
    x_internal_brain_id = 'x_internal_brain_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    connector_create = vectra.nexus_client.ConnectorCreate() # ConnectorCreate | 

    try:
        # Create Connector
        api_response = api_instance.create_connector_v1(x_internal_brain_id, x_vectra_service, connector_create)
        print("The response of ConnectorsApi->create_connector_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ConnectorsApi->create_connector_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_internal_brain_id** | **str**|  | 
 **x_vectra_service** | **str**|  | 
 **connector_create** | [**ConnectorCreate**](ConnectorCreate.md)|  | 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **delete_connector_v1**
> ConnectorResponse delete_connector_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_delete_input)

Delete Connector

Deletes a connector

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_delete_input import ConnectorDeleteInput
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.ConnectorsApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_internal_brain_id = 'x_internal_brain_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    connector_delete_input = vectra.nexus_client.ConnectorDeleteInput() # ConnectorDeleteInput | 

    try:
        # Delete Connector
        api_response = api_instance.delete_connector_v1(connector_id, x_internal_brain_id, x_vectra_service, connector_delete_input)
        print("The response of ConnectorsApi->delete_connector_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ConnectorsApi->delete_connector_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_internal_brain_id** | **str**|  | 
 **x_vectra_service** | **str**|  | 
 **connector_delete_input** | [**ConnectorDeleteInput**](ConnectorDeleteInput.md)|  | 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **get_connector_v1**
> ConnectorResponse get_connector_v1(connector_id, x_vectra_service, x_internal_brain_id=x_internal_brain_id)

Get Connector

Retrieves a connector

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.models.vectra_service import VectraService
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.ConnectorsApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_vectra_service = vectra.nexus_client.VectraService() # VectraService | 
    x_internal_brain_id = 'x_internal_brain_id_example' # str |  (optional)

    try:
        # Get Connector
        api_response = api_instance.get_connector_v1(connector_id, x_vectra_service, x_internal_brain_id=x_internal_brain_id)
        print("The response of ConnectorsApi->get_connector_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ConnectorsApi->get_connector_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_vectra_service** | [**VectraService**](.md)|  | 
 **x_internal_brain_id** | **str**|  | [optional] 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **get_connectors_v1**
> ConnectorListResponse get_connectors_v1(x_vectra_service, connector_type=connector_type, connector_state=connector_state, properties=properties, next_token=next_token, operating_state=operating_state, x_internal_brain_id=x_internal_brain_id)

Get Connectors

Fetches a list of connectors

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_list_response import ConnectorListResponse
from vectra.nexus_client.models.connector_state import ConnectorState
from vectra.nexus_client.models.connector_type import ConnectorType
from vectra.nexus_client.models.operating_state import OperatingState
from vectra.nexus_client.models.vectra_service import VectraService
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.ConnectorsApi(api_client)
    x_vectra_service = vectra.nexus_client.VectraService() # VectraService | 
    connector_type = vectra.nexus_client.ConnectorType() # ConnectorType |  (optional)
    connector_state = vectra.nexus_client.ConnectorState() # ConnectorState |  (optional)
    properties = 'properties_example' # str |  (optional)
    next_token = 'next_token_example' # str |  (optional)
    operating_state = vectra.nexus_client.OperatingState() # OperatingState |  (optional)
    x_internal_brain_id = 'x_internal_brain_id_example' # str |  (optional)

    try:
        # Get Connectors
        api_response = api_instance.get_connectors_v1(x_vectra_service, connector_type=connector_type, connector_state=connector_state, properties=properties, next_token=next_token, operating_state=operating_state, x_internal_brain_id=x_internal_brain_id)
        print("The response of ConnectorsApi->get_connectors_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ConnectorsApi->get_connectors_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_vectra_service** | [**VectraService**](.md)|  | 
 **connector_type** | [**ConnectorType**](.md)|  | [optional] 
 **connector_state** | [**ConnectorState**](.md)|  | [optional] 
 **properties** | **str**|  | [optional] 
 **next_token** | **str**|  | [optional] 
 **operating_state** | [**OperatingState**](.md)|  | [optional] 
 **x_internal_brain_id** | **str**|  | [optional] 

### Return type

[**ConnectorListResponse**](ConnectorListResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **patch_connector_v1**
> ConnectorResponse patch_connector_v1(connector_id, x_vectra_service, connector_update, x_internal_brain_id=x_internal_brain_id)

Patch Connector

Updates connector properties

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.models.connector_update import ConnectorUpdate
from vectra.nexus_client.models.vectra_service import VectraService
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.ConnectorsApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_vectra_service = vectra.nexus_client.VectraService() # VectraService | 
    connector_update = vectra.nexus_client.ConnectorUpdate() # ConnectorUpdate | 
    x_internal_brain_id = 'x_internal_brain_id_example' # str |  (optional)

    try:
        # Patch Connector
        api_response = api_instance.patch_connector_v1(connector_id, x_vectra_service, connector_update, x_internal_brain_id=x_internal_brain_id)
        print("The response of ConnectorsApi->patch_connector_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ConnectorsApi->patch_connector_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_vectra_service** | [**VectraService**](.md)|  | 
 **connector_update** | [**ConnectorUpdate**](ConnectorUpdate.md)|  | 
 **x_internal_brain_id** | **str**|  | [optional] 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

# **update_connector_state_v1**
> ConnectorResponse update_connector_state_v1(connector_id, x_vectra_service, connector_state_input)

Update Connector State

Updates connector state

### Example


```python
import vectra.nexus_client
from vectra.nexus_client.models.connector_response import ConnectorResponse
from vectra.nexus_client.models.connector_state_input import ConnectorStateInput
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)


# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.ConnectorsApi(api_client)
    connector_id = 'connector_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    connector_state_input = vectra.nexus_client.ConnectorStateInput() # ConnectorStateInput | 

    try:
        # Update Connector State
        api_response = api_instance.update_connector_state_v1(connector_id, x_vectra_service, connector_state_input)
        print("The response of ConnectorsApi->update_connector_state_v1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ConnectorsApi->update_connector_state_v1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **connector_id** | **str**|  | 
 **x_vectra_service** | **str**|  | 
 **connector_state_input** | [**ConnectorStateInput**](ConnectorStateInput.md)|  | 

### Return type

[**ConnectorResponse**](ConnectorResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successful Response |  -  |
**422** | Validation Error |  -  |

[[Back to top]](#) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to README]](../nexus_client_README.md)

