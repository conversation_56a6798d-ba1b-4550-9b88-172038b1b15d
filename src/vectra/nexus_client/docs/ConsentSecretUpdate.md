# ConsentSecretUpdate

Consent secret model for updating a secret

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**secret_name** | **str** |  | [optional] 
**client_id** | **str** |  | [optional] 
**client_secret** | **str** |  | [optional] 

## Example

```python
from vectra.nexus_client.models.consent_secret_update import ConsentSecretUpdate

# TODO update the JSON string below
json = "{}"
# create an instance of ConsentSecretUpdate from a JSON string
consent_secret_update_instance = ConsentSecretUpdate.from_json(json)
# print the JSON string representation of the object
print(ConsentSecretUpdate.to_json())

# convert the object into a dict
consent_secret_update_dict = consent_secret_update_instance.to_dict()
# create an instance of ConsentSecretUpdate from a dict
consent_secret_update_from_dict = ConsentSecretUpdate.from_dict(consent_secret_update_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


