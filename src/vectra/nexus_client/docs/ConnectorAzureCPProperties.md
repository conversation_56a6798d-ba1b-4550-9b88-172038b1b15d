# ConnectorAzureCPProperties

Model to update Azure CP Connector Properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | **str** |  | 
**resource_group_id** | **str** | Location of the logs stored in the azure tenant | 

## Example

```python
from vectra.nexus_client.models.connector_azure_cp_properties import ConnectorAzureCPProperties

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorAzureCPProperties from a JSON string
connector_azure_cp_properties_instance = ConnectorAzureCPProperties.from_json(json)
# print the JSON string representation of the object
print(ConnectorAzureCPProperties.to_json())

# convert the object into a dict
connector_azure_cp_properties_dict = connector_azure_cp_properties_instance.to_dict()
# create an instance of ConnectorAzureCPProperties from a dict
connector_azure_cp_properties_from_dict = ConnectorAzureCPProperties.from_dict(connector_azure_cp_properties_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


