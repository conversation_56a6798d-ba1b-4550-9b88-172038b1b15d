# Properties

Connector-specific properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**crowdstrike_url** | **str** | Crowdstrike Base URL used for API access | 
**sentinelone_url** | **str** | SentinelOne Base URL used for API access | 

## Example

```python
from vectra.nexus_client.models.properties import Properties

# TODO update the JSON string below
json = "{}"
# create an instance of Properties from a JSON string
properties_instance = Properties.from_json(json)
# print the JSON string representation of the object
print(Properties.to_json())

# convert the object into a dict
properties_dict = properties_instance.to_dict()
# create an instance of Properties from a dict
properties_from_dict = Properties.from_dict(properties_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


