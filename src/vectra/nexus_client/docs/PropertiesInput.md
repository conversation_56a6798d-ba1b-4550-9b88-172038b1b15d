# PropertiesInput


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | **str** |  | 
**resource_group_id** | **str** | Location of the logs stored in the azure tenant | 
**s3_bucket_name** | **str** | Cloudtrail S3 Bucket Name | 
**iam_role_arn** | **str** | S3 Bucket ARN | 
**sns_topic_arn** | **str** | SNS topic ARN | 

## Example

```python
from vectra.nexus_client.models.properties_input import PropertiesInput

# TODO update the JSON string below
json = "{}"
# create an instance of PropertiesInput from a JSON string
properties_input_instance = PropertiesInput.from_json(json)
# print the JSON string representation of the object
print(PropertiesInput.to_json())

# convert the object into a dict
properties_input_dict = properties_input_instance.to_dict()
# create an instance of PropertiesInput from a dict
properties_input_from_dict = PropertiesInput.from_dict(properties_input_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


