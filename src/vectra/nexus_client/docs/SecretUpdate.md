# SecretUpdate

Common secret fields

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**secret_name** | **str** |  | [optional] 
**client_id** | **str** |  | [optional] 
**client_secret** | **str** |  | [optional] 
**api_token** | **str** |  | [optional] 

## Example

```python
from vectra.nexus_client.models.secret_update import SecretUpdate

# TODO update the JSON string below
json = "{}"
# create an instance of SecretUpdate from a JSON string
secret_update_instance = SecretUpdate.from_json(json)
# print the JSON string representation of the object
print(SecretUpdate.to_json())

# convert the object into a dict
secret_update_dict = secret_update_instance.to_dict()
# create an instance of SecretUpdate from a dict
secret_update_from_dict = SecretUpdate.from_dict(secret_update_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


