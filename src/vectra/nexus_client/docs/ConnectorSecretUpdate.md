# ConnectorSecretUpdate

Request body to update the values of a consent secret or consent properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | **str** |  | 
**secret** | [**SecretUpdate**](SecretUpdate.md) |  | [optional] 
**properties** | [**PropertiesUpdate**](PropertiesUpdate.md) |  | [optional] 

## Example

```python
from vectra.nexus_client.models.connector_secret_update import ConnectorSecretUpdate

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorSecretUpdate from a JSON string
connector_secret_update_instance = ConnectorSecretUpdate.from_json(json)
# print the JSON string representation of the object
print(ConnectorSecretUpdate.to_json())

# convert the object into a dict
connector_secret_update_dict = connector_secret_update_instance.to_dict()
# create an instance of ConnectorSecretUpdate from a dict
connector_secret_update_from_dict = ConnectorSecretUpdate.from_dict(connector_secret_update_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


