# Secret

Common secret fields

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**secret_name** | **str** |  | [optional] 
**client_id** | **str** | API Client Id | 
**client_secret** | **str** | API Secret Key | 
**api_token** | **str** | Api token | 

## Example

```python
from vectra.nexus_client.models.secret import Secret

# TODO update the JSON string below
json = "{}"
# create an instance of Secret from a JSON string
secret_instance = Secret.from_json(json)
# print the JSON string representation of the object
print(Secret.to_json())

# convert the object into a dict
secret_dict = secret_instance.to_dict()
# create an instance of Secret from a dict
secret_from_dict = Secret.from_dict(secret_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


