# PropertiesUpdate

Connector-specific properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**crowdstrike_url** | **str** | Crowdstrike Base URL used for API access | 
**sentinelone_url** | **str** | SentinelOne Base URL used for API access | 

## Example

```python
from vectra.nexus_client.models.properties_update import PropertiesUpdate

# TODO update the JSON string below
json = "{}"
# create an instance of PropertiesUpdate from a JSON string
properties_update_instance = PropertiesUpdate.from_json(json)
# print the JSON string representation of the object
print(PropertiesUpdate.to_json())

# convert the object into a dict
properties_update_dict = properties_update_instance.to_dict()
# create an instance of PropertiesUpdate from a dict
properties_update_from_dict = PropertiesUpdate.from_dict(properties_update_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


