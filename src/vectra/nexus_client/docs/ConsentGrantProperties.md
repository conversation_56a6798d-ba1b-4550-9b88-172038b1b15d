# ConsentGrantProperties


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_type** | **str** |  | 
**ms_tenant_id** | **str** | Microsoft Tenant ID for the consenting tenant | 

## Example

```python
from vectra.nexus_client.models.consent_grant_properties import ConsentGrantProperties

# TODO update the JSON string below
json = "{}"
# create an instance of ConsentGrantProperties from a JSON string
consent_grant_properties_instance = ConsentGrantProperties.from_json(json)
# print the JSON string representation of the object
print(ConsentGrantProperties.to_json())

# convert the object into a dict
consent_grant_properties_dict = consent_grant_properties_instance.to_dict()
# create an instance of ConsentGrantProperties from a dict
consent_grant_properties_from_dict = ConsentGrantProperties.from_dict(consent_grant_properties_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


