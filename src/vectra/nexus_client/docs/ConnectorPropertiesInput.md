# ConnectorPropertiesInput

Request body model when updating AWS or Azure connectors

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**properties** | [**PropertiesInput**](PropertiesInput.md) |  | 

## Example

```python
from vectra.nexus_client.models.connector_properties_input import ConnectorPropertiesInput

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorPropertiesInput from a JSON string
connector_properties_input_instance = ConnectorPropertiesInput.from_json(json)
# print the JSON string representation of the object
print(ConnectorPropertiesInput.to_json())

# convert the object into a dict
connector_properties_input_dict = connector_properties_input_instance.to_dict()
# create an instance of ConnectorPropertiesInput from a dict
connector_properties_input_from_dict = ConnectorPropertiesInput.from_dict(connector_properties_input_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


