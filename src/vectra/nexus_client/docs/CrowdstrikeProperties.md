# CrowdstrikeProperties

Crowdstrike specific consent properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**crowdstrike_url** | **str** | Crowdstrike Base URL used for API access | 

## Example

```python
from vectra.nexus_client.models.crowdstrike_properties import CrowdstrikeProperties

# TODO update the JSON string below
json = "{}"
# create an instance of CrowdstrikeProperties from a JSON string
crowdstrike_properties_instance = CrowdstrikeProperties.from_json(json)
# print the JSON string representation of the object
print(CrowdstrikeProperties.to_json())

# convert the object into a dict
crowdstrike_properties_dict = crowdstrike_properties_instance.to_dict()
# create an instance of CrowdstrikeProperties from a dict
crowdstrike_properties_from_dict = CrowdstrikeProperties.from_dict(crowdstrike_properties_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


