# SentinelOneSecretUpdate

Consent secret model for creating and updating a secret for sentinelone

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**secret_name** | **str** |  | [optional] 
**api_token** | **str** |  | [optional] 

## Example

```python
from vectra.nexus_client.models.sentinel_one_secret_update import SentinelOneSecretUpdate

# TODO update the JSON string below
json = "{}"
# create an instance of SentinelOneSecretUpdate from a JSON string
sentinel_one_secret_update_instance = SentinelOneSecretUpdate.from_json(json)
# print the JSON string representation of the object
print(SentinelOneSecretUpdate.to_json())

# convert the object into a dict
sentinel_one_secret_update_dict = sentinel_one_secret_update_instance.to_dict()
# create an instance of SentinelOneSecretUpdate from a dict
sentinel_one_secret_update_from_dict = SentinelOneSecretUpdate.from_dict(sentinel_one_secret_update_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


