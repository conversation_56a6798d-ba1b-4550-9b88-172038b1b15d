# ConsentSecret

Consent secret model for creating a secret

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**secret_name** | **str** |  | [optional] 
**client_id** | **str** | API Client Id | 
**client_secret** | **str** | API Secret Key | 

## Example

```python
from vectra.nexus_client.models.consent_secret import ConsentSecret

# TODO update the JSON string below
json = "{}"
# create an instance of ConsentSecret from a JSON string
consent_secret_instance = ConsentSecret.from_json(json)
# print the JSON string representation of the object
print(ConsentSecret.to_json())

# convert the object into a dict
consent_secret_dict = consent_secret_instance.to_dict()
# create an instance of ConsentSecret from a dict
consent_secret_from_dict = ConsentSecret.from_dict(consent_secret_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


