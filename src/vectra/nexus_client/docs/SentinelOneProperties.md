# SentinelOneProperties

sentinelOne specific consent properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**sentinelone_url** | **str** | SentinelOne Base URL used for API access | 

## Example

```python
from vectra.nexus_client.models.sentinel_one_properties import SentinelOneProperties

# TODO update the JSON string below
json = "{}"
# create an instance of SentinelOneProperties from a JSON string
sentinel_one_properties_instance = SentinelOneProperties.from_json(json)
# print the JSON string representation of the object
print(SentinelOneProperties.to_json())

# convert the object into a dict
sentinel_one_properties_dict = sentinel_one_properties_instance.to_dict()
# create an instance of SentinelOneProperties from a dict
sentinel_one_properties_from_dict = SentinelOneProperties.from_dict(sentinel_one_properties_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


