# ConnectorStateConnected

Request body to update connector state to 'connected'

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_state** | **str** | Connector is connected | 

## Example

```python
from vectra.nexus_client.models.connector_state_connected import ConnectorStateConnected

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorStateConnected from a JSON string
connector_state_connected_instance = ConnectorStateConnected.from_json(json)
# print the JSON string representation of the object
print(ConnectorStateConnected.to_json())

# convert the object into a dict
connector_state_connected_dict = connector_state_connected_instance.to_dict()
# create an instance of ConnectorStateConnected from a dict
connector_state_connected_from_dict = ConnectorStateConnected.from_dict(connector_state_connected_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


