# ConnectorUpdate

Request body model when updating an existing connectors non-breaking properties

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** |  | [optional] 
**last_log_received** | **str** |  | [optional] 
**size** | [**ConnectorSize**](ConnectorSize.md) |  | [optional] 
**last_sequence_id** | **str** |  | [optional] 

## Example

```python
from vectra.nexus_client.models.connector_update import ConnectorUpdate

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorUpdate from a JSON string
connector_update_instance = ConnectorUpdate.from_json(json)
# print the JSON string representation of the object
print(ConnectorUpdate.to_json())

# convert the object into a dict
connector_update_dict = connector_update_instance.to_dict()
# create an instance of ConnectorUpdate from a dict
connector_update_from_dict = ConnectorUpdate.from_dict(connector_update_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


