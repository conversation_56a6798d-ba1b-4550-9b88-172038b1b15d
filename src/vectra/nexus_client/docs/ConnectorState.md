# ConnectorState

Enum model for connector state

## Enum

* `SETUP` (value: `'setup'`)

* `CONNECTING` (value: `'connecting'`)

* `CONNECTED` (value: `'connected'`)

* `ERROR` (value: `'error'`)

* `DELETED` (value: `'deleted'`)

[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


