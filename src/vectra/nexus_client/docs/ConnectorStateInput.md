# ConnectorStateInput


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connector_state** | **str** | Connector is in error state | 
**error** | [**ConnectorError**](ConnectorError.md) | The error message associated with the connector | 

## Example

```python
from vectra.nexus_client.models.connector_state_input import ConnectorStateInput

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorStateInput from a JSON string
connector_state_input_instance = ConnectorStateInput.from_json(json)
# print the JSON string representation of the object
print(ConnectorStateInput.to_json())

# convert the object into a dict
connector_state_input_dict = connector_state_input_instance.to_dict()
# create an instance of ConnectorStateInput from a dict
connector_state_input_from_dict = ConnectorStateInput.from_dict(connector_state_input_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


