# ConnectorListResponse

Connectors response constructed from boto3 dynamodb query response

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connectors** | [**List[ConnectorResponse]**](ConnectorResponse.md) | A list of connectors | 
**next_link** | **str** |  | [optional] 

## Example

```python
from vectra.nexus_client.models.connector_list_response import ConnectorListResponse

# TODO update the JSON string below
json = "{}"
# create an instance of ConnectorListResponse from a JSON string
connector_list_response_instance = ConnectorListResponse.from_json(json)
# print the JSON string representation of the object
print(ConnectorListResponse.to_json())

# convert the object into a dict
connector_list_response_dict = connector_list_response_instance.to_dict()
# create an instance of ConnectorListResponse from a dict
connector_list_response_from_dict = ConnectorListResponse.from_dict(connector_list_response_dict)
```
[[Back to Model list]](../nexus_client_README.md#documentation-for-models) [[Back to API list]](../nexus_client_README.md#documentation-for-api-endpoints) [[Back to README]](../nexus_client_README.md)


