# nexus-sdk
Connector Management Service

The `vectra.nexus_client` package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0.0
- Package version: 1.0.0
- Generator version: 7.11.0
- Build package: org.openapitools.codegen.languages.PythonClientCodegen

## Requirements.

Python 3.8+

## Installation & Usage

This python library package is generated without supporting files like setup.py or requirements files

To be able to use it, you will need these dependencies in your own package that uses this library:

* urllib3 >= 1.25.3, < 3.0.0
* python-dateutil >= 2.8.2
* pydantic >= 2
* typing-extensions >= 4.7.1

## Getting Started

In your own code, to use this library to connect and interact with nexus-sdk,
you can run the following:

```python

import vectra.nexus_client
from vectra.nexus_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = vectra.nexus_client.Configuration(
    host = "http://localhost"
)



# Enter a context with an instance of the API client
with vectra.nexus_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = vectra.nexus_client.ConnectorsApi(api_client)
    x_internal_brain_id = 'x_internal_brain_id_example' # str | 
    x_vectra_service = 'x_vectra_service_example' # str | 
    connector_create = vectra.nexus_client.ConnectorCreate() # ConnectorCreate | 

    try:
        # Create Connector
        api_response = api_instance.create_connector_v1(x_internal_brain_id, x_vectra_service, connector_create)
        print("The response of ConnectorsApi->create_connector_v1:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling ConnectorsApi->create_connector_v1: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*ConnectorsApi* | [**create_connector_v1**](docs/ConnectorsApi.md#create_connector_v1) | **POST** /v1/connectors | Create Connector
*ConnectorsApi* | [**delete_connector_v1**](docs/ConnectorsApi.md#delete_connector_v1) | **DELETE** /v1/connectors/{connector_id} | Delete Connector
*ConnectorsApi* | [**get_connector_v1**](docs/ConnectorsApi.md#get_connector_v1) | **GET** /v1/connectors/{connector_id} | Get Connector
*ConnectorsApi* | [**get_connectors_v1**](docs/ConnectorsApi.md#get_connectors_v1) | **GET** /v1/connectors | Get Connectors
*ConnectorsApi* | [**patch_connector_v1**](docs/ConnectorsApi.md#patch_connector_v1) | **PATCH** /v1/connectors/{connector_id} | Patch Connector
*ConnectorsApi* | [**update_connector_state_v1**](docs/ConnectorsApi.md#update_connector_state_v1) | **POST** /v1/connectors/{connector_id}/state | Update Connector State
*DefaultApi* | [**root**](docs/DefaultApi.md#root) | **GET** / | Root
*SetupApi* | [**setup_consent_v1**](docs/SetupApi.md#setup_consent_v1) | **POST** /v1/connectors/{connector_id}/setup/consent | Setup Consent
*SetupApi* | [**setup_properties_v1**](docs/SetupApi.md#setup_properties_v1) | **POST** /v1/connectors/{connector_id}/setup/properties | Setup Properties
*SetupApi* | [**setup_secrets_v1**](docs/SetupApi.md#setup_secrets_v1) | **POST** /v1/connectors/{connector_id}/setup/secrets | Setup Secrets
*SetupApi* | [**update_secret_v1**](docs/SetupApi.md#update_secret_v1) | **PATCH** /v1/connectors/{connector_id}/setup/secrets | Update Secret


## Documentation For Models

 - [ConnectorAwsProperties](docs/ConnectorAwsProperties.md)
 - [ConnectorAzureCPProperties](docs/ConnectorAzureCPProperties.md)
 - [ConnectorCreate](docs/ConnectorCreate.md)
 - [ConnectorDeleteInput](docs/ConnectorDeleteInput.md)
 - [ConnectorError](docs/ConnectorError.md)
 - [ConnectorListResponse](docs/ConnectorListResponse.md)
 - [ConnectorPropertiesInput](docs/ConnectorPropertiesInput.md)
 - [ConnectorResponse](docs/ConnectorResponse.md)
 - [ConnectorSecretCreate](docs/ConnectorSecretCreate.md)
 - [ConnectorSecretUpdate](docs/ConnectorSecretUpdate.md)
 - [ConnectorSize](docs/ConnectorSize.md)
 - [ConnectorState](docs/ConnectorState.md)
 - [ConnectorStateConnected](docs/ConnectorStateConnected.md)
 - [ConnectorStateError](docs/ConnectorStateError.md)
 - [ConnectorStateInput](docs/ConnectorStateInput.md)
 - [ConnectorType](docs/ConnectorType.md)
 - [ConnectorUpdate](docs/ConnectorUpdate.md)
 - [ConsentGrantProperties](docs/ConsentGrantProperties.md)
 - [ConsentGrantRecord](docs/ConsentGrantRecord.md)
 - [ConsentSecret](docs/ConsentSecret.md)
 - [ConsentSecretUpdate](docs/ConsentSecretUpdate.md)
 - [CrowdstrikeProperties](docs/CrowdstrikeProperties.md)
 - [HTTPValidationError](docs/HTTPValidationError.md)
 - [LocationInner](docs/LocationInner.md)
 - [MicrosoftConsentGrant](docs/MicrosoftConsentGrant.md)
 - [OperatingState](docs/OperatingState.md)
 - [Properties](docs/Properties.md)
 - [PropertiesInput](docs/PropertiesInput.md)
 - [PropertiesUpdate](docs/PropertiesUpdate.md)
 - [Secret](docs/Secret.md)
 - [SecretUpdate](docs/SecretUpdate.md)
 - [SentinelOneProperties](docs/SentinelOneProperties.md)
 - [SentinelOneSecret](docs/SentinelOneSecret.md)
 - [SentinelOneSecretUpdate](docs/SentinelOneSecretUpdate.md)
 - [ValidationError](docs/ValidationError.md)
 - [VectraService](docs/VectraService.md)


<a id="documentation-for-authorization"></a>
## Documentation For Authorization

Endpoints do not require authorization.


## Author




