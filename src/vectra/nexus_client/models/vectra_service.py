# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import json
from enum import Enum
from typing_extensions import Self


class VectraService(str, Enum):
    """
    Enum model for values expected in the x-vectra-service header
    """

    """
    allowed enum values
    """
    LOGFLOW = 'logflow'
    CONSENT = 'consent'
    VUI = 'vui'

    @classmethod
    def from_json(cls, json_str: str) -> Self:
        """Create an instance of VectraService from a JSON string"""
        return cls(json.loads(json_str))


