# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List
from typing_extensions import Annotated
from typing import Optional, Set
from typing_extensions import Self

class ConnectorAwsProperties(BaseModel):
    """
    Model to update AWS Connector Properties
    """ # noqa: E501
    connector_type: StrictStr
    s3_bucket_name: Annotated[str, Field(strict=True)] = Field(description="Cloudtrail S3 Bucket Name")
    iam_role_arn: Annotated[str, Field(strict=True)] = Field(description="S3 Bucket ARN")
    sns_topic_arn: Annotated[str, Field(strict=True)] = Field(description="SNS topic ARN")
    __properties: ClassVar[List[str]] = ["connector_type", "s3_bucket_name", "iam_role_arn", "sns_topic_arn"]

    @field_validator('connector_type')
    def connector_type_validate_enum(cls, value):
        """Validates the enum"""
        if value not in set(['aws']):
            raise ValueError("must be one of enum values ('aws')")
        return value

    @field_validator('s3_bucket_name')
    def s3_bucket_name_validate_regular_expression(cls, value):
        """Validates the regular expression"""
        if not re.match(r"^[a-z0-9.-]{3,63}$", value):
            raise ValueError(r"must validate the regular expression /^[a-z0-9.-]{3,63}$/")
        return value

    @field_validator('iam_role_arn')
    def iam_role_arn_validate_regular_expression(cls, value):
        """Validates the regular expression"""
        if not re.match(r"^arn:aws:iam::\d{12}:role\/(?=.{0,512}$)(?:[\w+=,.@-]+\/)*[\w+=,.@-]{1,64}$", value):
            raise ValueError(r"must validate the regular expression /^arn:aws:iam::\d{12}:role\/(?=.{0,512}$)(?:[\w+=,.@-]+\/)*[\w+=,.@-]{1,64}$/")
        return value

    @field_validator('sns_topic_arn')
    def sns_topic_arn_validate_regular_expression(cls, value):
        """Validates the regular expression"""
        if not re.match(r"^(arn:aws:sns:(us(-gov)?|ap|ca|cn|eu|il|me|sa)-(central|(north|south)?(east|west)?)-\d:(\d{12}):([\w-]{1,256}$))$", value):
            raise ValueError(r"must validate the regular expression /^(arn:aws:sns:(us(-gov)?|ap|ca|cn|eu|il|me|sa)-(central|(north|south)?(east|west)?)-\d:(\d{12}):([\w-]{1,256}$))$/")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ConnectorAwsProperties from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ConnectorAwsProperties from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "connector_type": obj.get("connector_type"),
            "s3_bucket_name": obj.get("s3_bucket_name"),
            "iam_role_arn": obj.get("iam_role_arn"),
            "sns_topic_arn": obj.get("sns_topic_arn")
        })
        return _obj


