# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import json
import pprint
from pydantic import BaseModel, ConfigDict, Field, StrictStr, ValidationError, field_validator
from typing import Any, List, Optional
from vectra.nexus_client.models.connector_aws_properties import ConnectorAwsProperties
from vectra.nexus_client.models.connector_azure_cp_properties import ConnectorAzureCPProperties
from pydantic import StrictStr, Field
from typing import Union, List, Set, Optional, Dict
from typing_extensions import Literal, Self

PROPERTIESINPUT_ONE_OF_SCHEMAS = ["ConnectorAwsProperties", "ConnectorAzureCPProperties"]

class PropertiesInput(BaseModel):
    """
    PropertiesInput
    """
    # data type: ConnectorAzureCPProperties
    oneof_schema_1_validator: Optional[ConnectorAzureCPProperties] = None
    # data type: ConnectorAwsProperties
    oneof_schema_2_validator: Optional[ConnectorAwsProperties] = None
    actual_instance: Optional[Union[ConnectorAwsProperties, ConnectorAzureCPProperties]] = None
    one_of_schemas: Set[str] = { "ConnectorAwsProperties", "ConnectorAzureCPProperties" }

    model_config = ConfigDict(
        validate_assignment=True,
        protected_namespaces=(),
    )


    discriminator_value_class_map: Dict[str, str] = {
    }

    def __init__(self, *args, **kwargs) -> None:
        if args:
            if len(args) > 1:
                raise ValueError("If a position argument is used, only 1 is allowed to set `actual_instance`")
            if kwargs:
                raise ValueError("If a position argument is used, keyword arguments cannot be used.")
            super().__init__(actual_instance=args[0])
        else:
            super().__init__(**kwargs)

    @field_validator('actual_instance')
    def actual_instance_must_validate_oneof(cls, v):
        instance = PropertiesInput.model_construct()
        error_messages = []
        match = 0
        # validate data type: ConnectorAzureCPProperties
        if not isinstance(v, ConnectorAzureCPProperties):
            error_messages.append(f"Error! Input type `{type(v)}` is not `ConnectorAzureCPProperties`")
        else:
            match += 1
        # validate data type: ConnectorAwsProperties
        if not isinstance(v, ConnectorAwsProperties):
            error_messages.append(f"Error! Input type `{type(v)}` is not `ConnectorAwsProperties`")
        else:
            match += 1
        if match > 1:
            # more than 1 match
            raise ValueError("Multiple matches found when setting `actual_instance` in PropertiesInput with oneOf schemas: ConnectorAwsProperties, ConnectorAzureCPProperties. Details: " + ", ".join(error_messages))
        elif match == 0:
            # no match
            raise ValueError("No match found when setting `actual_instance` in PropertiesInput with oneOf schemas: ConnectorAwsProperties, ConnectorAzureCPProperties. Details: " + ", ".join(error_messages))
        else:
            return v

    @classmethod
    def from_dict(cls, obj: Union[str, Dict[str, Any]]) -> Self:
        return cls.from_json(json.dumps(obj))

    @classmethod
    def from_json(cls, json_str: str) -> Self:
        """Returns the object represented by the json string"""
        instance = cls.model_construct()
        error_messages = []
        match = 0

        # deserialize data into ConnectorAzureCPProperties
        try:
            instance.actual_instance = ConnectorAzureCPProperties.from_json(json_str)
            match += 1
        except (ValidationError, ValueError) as e:
            error_messages.append(str(e))
        # deserialize data into ConnectorAwsProperties
        try:
            instance.actual_instance = ConnectorAwsProperties.from_json(json_str)
            match += 1
        except (ValidationError, ValueError) as e:
            error_messages.append(str(e))

        if match > 1:
            # more than 1 match
            raise ValueError("Multiple matches found when deserializing the JSON string into PropertiesInput with oneOf schemas: ConnectorAwsProperties, ConnectorAzureCPProperties. Details: " + ", ".join(error_messages))
        elif match == 0:
            # no match
            raise ValueError("No match found when deserializing the JSON string into PropertiesInput with oneOf schemas: ConnectorAwsProperties, ConnectorAzureCPProperties. Details: " + ", ".join(error_messages))
        else:
            return instance

    def to_json(self) -> str:
        """Returns the JSON representation of the actual instance"""
        if self.actual_instance is None:
            return "null"

        if hasattr(self.actual_instance, "to_json") and callable(self.actual_instance.to_json):
            return self.actual_instance.to_json()
        else:
            return json.dumps(self.actual_instance)

    def to_dict(self) -> Optional[Union[Dict[str, Any], ConnectorAwsProperties, ConnectorAzureCPProperties]]:
        """Returns the dict representation of the actual instance"""
        if self.actual_instance is None:
            return None

        if hasattr(self.actual_instance, "to_dict") and callable(self.actual_instance.to_dict):
            return self.actual_instance.to_dict()
        else:
            # primitive type
            return self.actual_instance

    def to_str(self) -> str:
        """Returns the string representation of the actual instance"""
        return pprint.pformat(self.model_dump())


