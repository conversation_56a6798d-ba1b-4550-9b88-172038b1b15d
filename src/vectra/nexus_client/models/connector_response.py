# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from vectra.nexus_client.models.connector_error import ConnectorError
from vectra.nexus_client.models.connector_size import ConnectorSize
from vectra.nexus_client.models.connector_state import ConnectorState
from vectra.nexus_client.models.connector_type import ConnectorType
from vectra.nexus_client.models.operating_state import OperatingState
from typing import Optional, Set
from typing_extensions import Self

class ConnectorResponse(BaseModel):
    """
    Connector Response
    """ # noqa: E501
    name: StrictStr = Field(description="Connector Name")
    connector_type: ConnectorType = Field(description="Connector Type")
    connector_id: StrictStr = Field(description="Connector ID")
    connector_state: ConnectorState = Field(description="Connector State")
    operating_state: OperatingState = Field(description="Connector State")
    internal_brain_id: StrictStr = Field(description="Brain ID of the tenant")
    setup_records: Dict[str, Any] = Field(description="Setup records and their state associated with the connector")
    created_at: StrictStr = Field(description="The date the connector was first created")
    updated_at: StrictStr = Field(description="The date the connector was last modified")
    properties: Dict[str, Any] = Field(description="The properties associated with the connector")
    error: Optional[ConnectorError]
    first_log_received: Optional[StrictStr] = None
    last_log_received: Optional[StrictStr]
    size: Optional[ConnectorSize] = Field(default=None, description="Connector Size")
    last_sequence_id: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["name", "connector_type", "connector_id", "connector_state", "operating_state", "internal_brain_id", "setup_records", "created_at", "updated_at", "properties", "error", "first_log_received", "last_log_received", "size", "last_sequence_id"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ConnectorResponse from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of error
        if self.error:
            _dict['error'] = self.error.to_dict()
        # set to None if error (nullable) is None
        # and model_fields_set contains the field
        if self.error is None and "error" in self.model_fields_set:
            _dict['error'] = None

        # set to None if first_log_received (nullable) is None
        # and model_fields_set contains the field
        if self.first_log_received is None and "first_log_received" in self.model_fields_set:
            _dict['first_log_received'] = None

        # set to None if last_log_received (nullable) is None
        # and model_fields_set contains the field
        if self.last_log_received is None and "last_log_received" in self.model_fields_set:
            _dict['last_log_received'] = None

        # set to None if last_sequence_id (nullable) is None
        # and model_fields_set contains the field
        if self.last_sequence_id is None and "last_sequence_id" in self.model_fields_set:
            _dict['last_sequence_id'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ConnectorResponse from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "name": obj.get("name"),
            "connector_type": obj.get("connector_type"),
            "connector_id": obj.get("connector_id"),
            "connector_state": obj.get("connector_state"),
            "operating_state": obj.get("operating_state"),
            "internal_brain_id": obj.get("internal_brain_id"),
            "setup_records": obj.get("setup_records"),
            "created_at": obj.get("created_at"),
            "updated_at": obj.get("updated_at"),
            "properties": obj.get("properties"),
            "error": ConnectorError.from_dict(obj["error"]) if obj.get("error") is not None else None,
            "first_log_received": obj.get("first_log_received"),
            "last_log_received": obj.get("last_log_received"),
            "size": obj.get("size"),
            "last_sequence_id": obj.get("last_sequence_id")
        })
        return _obj


