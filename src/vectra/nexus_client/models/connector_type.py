# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import json
from enum import Enum
from typing_extensions import Self


class ConnectorType(str, Enum):
    """
    Connector Type model
    """

    """
    allowed enum values
    """
    AZURE_MINUS_CP = 'azure-cp'
    AWS = 'aws'
    CROWDSTRIKE = 'crowdstrike'
    DEFENDER = 'defender'
    SENTINELONE = 'sentinelone'

    @classmethod
    def from_json(cls, json_str: str) -> Self:
        """Create an instance of ConnectorType from a JSON string"""
        return cls(json.loads(json_str))


