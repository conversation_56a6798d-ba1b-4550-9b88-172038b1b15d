# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from vectra.nexus_client.models.connector_response import ConnectorResponse
from typing import Optional, Set
from typing_extensions import Self

class ConnectorListResponse(BaseModel):
    """
    Connectors response constructed from boto3 dynamodb query response
    """ # noqa: E501
    connectors: List[ConnectorResponse] = Field(description="A list of connectors")
    next_link: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["connectors", "next_link"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ConnectorListResponse from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in connectors (list)
        _items = []
        if self.connectors:
            for _item_connectors in self.connectors:
                if _item_connectors:
                    _items.append(_item_connectors.to_dict())
            _dict['connectors'] = _items
        # set to None if next_link (nullable) is None
        # and model_fields_set contains the field
        if self.next_link is None and "next_link" in self.model_fields_set:
            _dict['next_link'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ConnectorListResponse from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "connectors": [ConnectorResponse.from_dict(_item) for _item in obj["connectors"]] if obj.get("connectors") is not None else None,
            "next_link": obj.get("next_link")
        })
        return _obj


