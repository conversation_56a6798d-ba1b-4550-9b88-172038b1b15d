# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from vectra.nexus_client.models.properties import Properties
from vectra.nexus_client.models.secret import Secret
from typing import Optional, Set
from typing_extensions import Self

class ConnectorSecretCreate(BaseModel):
    """
    Request body for connectors that consent using an api secret Note:     properties has a default=None as it may not always be required
    """ # noqa: E501
    connector_type: StrictStr
    secret: Secret
    properties: Optional[Properties] = None
    __properties: ClassVar[List[str]] = ["connector_type", "secret", "properties"]

    @field_validator('connector_type')
    def connector_type_validate_enum(cls, value):
        """Validates the enum"""
        if value not in set(['crowdstrike', 'sentinelone']):
            raise ValueError("must be one of enum values ('crowdstrike', 'sentinelone')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ConnectorSecretCreate from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of secret
        if self.secret:
            _dict['secret'] = self.secret.to_dict()
        # override the default output from pydantic by calling `to_dict()` of properties
        if self.properties:
            _dict['properties'] = self.properties.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ConnectorSecretCreate from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "connector_type": obj.get("connector_type"),
            "secret": Secret.from_dict(obj["secret"]) if obj.get("secret") is not None else None,
            "properties": Properties.from_dict(obj["properties"]) if obj.get("properties") is not None else None
        })
        return _obj


