# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.api.connectors_api import ConnectorsApi


class TestConnectorsApi(unittest.TestCase):
    """ConnectorsApi unit test stubs"""

    def setUp(self) -> None:
        self.api = ConnectorsApi()

    def tearDown(self) -> None:
        pass

    def test_create_connector_v1(self) -> None:
        """Test case for create_connector_v1

        Create Connector
        """
        pass

    def test_delete_connector_v1(self) -> None:
        """Test case for delete_connector_v1

        Delete Connector
        """
        pass

    def test_get_connector_v1(self) -> None:
        """Test case for get_connector_v1

        Get Connector
        """
        pass

    def test_get_connectors_v1(self) -> None:
        """Test case for get_connectors_v1

        Get Connectors
        """
        pass

    def test_patch_connector_v1(self) -> None:
        """Test case for patch_connector_v1

        Patch Connector
        """
        pass

    def test_update_connector_state_v1(self) -> None:
        """Test case for update_connector_state_v1

        Update Connector State
        """
        pass


if __name__ == '__main__':
    unittest.main()
