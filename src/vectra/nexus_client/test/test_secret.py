# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.secret import Secret

class TestSecret(unittest.TestCase):
    """Secret unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> Secret:
        """Test Secret
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `Secret`
        """
        model = Secret()
        if include_optional:
            return Secret(
                secret_name = '',
                client_id = '298342j382u34n83u2',
                client_secret = 'abcD1234EFghIjkLmNoPqRstUvWxYz567890',
                api_token = 'token-secret'
            )
        else:
            return Secret(
                client_id = '298342j382u34n83u2',
                client_secret = 'abcD1234EFghIjkLmNoPqRstUvWxYz567890',
                api_token = 'token-secret',
        )
        """

    def testSecret(self):
        """Test Secret"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
