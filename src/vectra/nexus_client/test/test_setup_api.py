# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.api.setup_api import SetupApi


class TestSetupApi(unittest.TestCase):
    """SetupApi unit test stubs"""

    def setUp(self) -> None:
        self.api = SetupApi()

    def tearDown(self) -> None:
        pass

    def test_setup_consent_v1(self) -> None:
        """Test case for setup_consent_v1

        Setup Consent
        """
        pass

    def test_setup_properties_v1(self) -> None:
        """Test case for setup_properties_v1

        Setup Properties
        """
        pass

    def test_setup_secrets_v1(self) -> None:
        """Test case for setup_secrets_v1

        Setup Secrets
        """
        pass

    def test_update_secret_v1(self) -> None:
        """Test case for update_secret_v1

        Update Secret
        """
        pass


if __name__ == '__main__':
    unittest.main()
