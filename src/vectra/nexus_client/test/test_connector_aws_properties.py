# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_aws_properties import ConnectorAwsProperties

class TestConnectorAwsProperties(unittest.TestCase):
    """ConnectorAwsProperties unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ConnectorAwsProperties:
        """Test ConnectorAwsProperties
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ConnectorAwsProperties`
        """
        model = ConnectorAwsProperties()
        if include_optional:
            return ConnectorAwsProperties(
                connector_type = 'aws',
                s3_bucket_name = 'mock-bucket',
                iam_role_arn = 'arn:aws:iam::123456789009:role/mock-bucket',
                sns_topic_arn = 'arn:aws:sns:us-west-2:123456789009:mock-bucket'
            )
        else:
            return ConnectorAwsProperties(
                connector_type = 'aws',
                s3_bucket_name = 'mock-bucket',
                iam_role_arn = 'arn:aws:iam::123456789009:role/mock-bucket',
                sns_topic_arn = 'arn:aws:sns:us-west-2:123456789009:mock-bucket',
        )
        """

    def testConnectorAwsProperties(self):
        """Test ConnectorAwsProperties"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
