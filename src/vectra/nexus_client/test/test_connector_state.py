# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_state import ConnectorState

class TestConnectorState(unittest.TestCase):
    """ConnectorState unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testConnectorState(self):
        """Test ConnectorState"""
        # inst = ConnectorState()

if __name__ == '__main__':
    unittest.main()
