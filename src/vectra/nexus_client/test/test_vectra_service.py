# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.vectra_service import VectraService

class TestVectraService(unittest.TestCase):
    """VectraService unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testVectraService(self):
        """Test VectraService"""
        # inst = VectraService()

if __name__ == '__main__':
    unittest.main()
