# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_secret_create import ConnectorSecretCreate

class TestConnectorSecretCreate(unittest.TestCase):
    """ConnectorSecretCreate unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ConnectorSecretCreate:
        """Test ConnectorSecretCreate
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ConnectorSecretCreate`
        """
        model = ConnectorSecretCreate()
        if include_optional:
            return ConnectorSecretCreate(
                connector_type = 'crowdstrike',
                secret = None,
                properties = None
            )
        else:
            return ConnectorSecretCreate(
                connector_type = 'crowdstrike',
                secret = None,
        )
        """

    def testConnectorSecretCreate(self):
        """Test ConnectorSecretCreate"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
