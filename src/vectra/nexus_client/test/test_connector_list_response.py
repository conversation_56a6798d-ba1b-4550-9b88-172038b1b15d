# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_list_response import ConnectorListResponse

class TestConnectorListResponse(unittest.TestCase):
    """ConnectorListResponse unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ConnectorListResponse:
        """Test ConnectorListResponse
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ConnectorListResponse`
        """
        model = ConnectorListResponse()
        if include_optional:
            return ConnectorListResponse(
                connectors = [
                    vectra.nexus_client.models.connector_response.ConnectorResponse(
                        name = 'aws_connector', 
                        connector_type = null, 
                        connector_id = '76d6d891-7e5d-47c8-a078-666044c0bbdb', 
                        connector_state = null, 
                        operating_state = null, 
                        internal_brain_id = 'saas99d4e37f493a4d37b83b09029f5c4dd7', 
                        setup_records = {"setup_records":{"consent":"completed","user_input":"awaiting"}}, 
                        created_at = '2024-02-26T10:37:11.750Z', 
                        updated_at = '2024-02-26T10:37:11.750Z', 
                        properties = {"iam_role_arn":"arn:aws:iam::1234567891:role/foo"}, 
                        error = vectra.nexus_client.models.connector_error.ConnectorError(
                            error_code = '', 
                            error_message = '', ), 
                        first_log_received = '', 
                        last_log_received = '', 
                        size = null, 
                        last_sequence_id = '', )
                    ],
                next_link = ''
            )
        else:
            return ConnectorListResponse(
                connectors = [
                    vectra.nexus_client.models.connector_response.ConnectorResponse(
                        name = 'aws_connector', 
                        connector_type = null, 
                        connector_id = '76d6d891-7e5d-47c8-a078-666044c0bbdb', 
                        connector_state = null, 
                        operating_state = null, 
                        internal_brain_id = 'saas99d4e37f493a4d37b83b09029f5c4dd7', 
                        setup_records = {"setup_records":{"consent":"completed","user_input":"awaiting"}}, 
                        created_at = '2024-02-26T10:37:11.750Z', 
                        updated_at = '2024-02-26T10:37:11.750Z', 
                        properties = {"iam_role_arn":"arn:aws:iam::1234567891:role/foo"}, 
                        error = vectra.nexus_client.models.connector_error.ConnectorError(
                            error_code = '', 
                            error_message = '', ), 
                        first_log_received = '', 
                        last_log_received = '', 
                        size = null, 
                        last_sequence_id = '', )
                    ],
        )
        """

    def testConnectorListResponse(self):
        """Test ConnectorListResponse"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
