# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.microsoft_consent_grant import MicrosoftConsentGrant

class TestMicrosoftConsentGrant(unittest.TestCase):
    """MicrosoftConsentGrant unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> MicrosoftConsentGrant:
        """Test MicrosoftConsentGrant
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `MicrosoftConsentGrant`
        """
        model = MicrosoftConsentGrant()
        if include_optional:
            return MicrosoftConsentGrant(
                connector_type = 'defender',
                ms_tenant_id = 'dda75ba7-c192-4bf3-8139-2d4b1hqbc6cc'
            )
        else:
            return MicrosoftConsentGrant(
                connector_type = 'defender',
                ms_tenant_id = 'dda75ba7-c192-4bf3-8139-2d4b1hqbc6cc',
        )
        """

    def testMicrosoftConsentGrant(self):
        """Test MicrosoftConsentGrant"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
