# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.consent_grant_properties import ConsentGrantProperties

class TestConsentGrantProperties(unittest.TestCase):
    """ConsentGrantProperties unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ConsentGrantProperties:
        """Test ConsentGrantProperties
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ConsentGrantProperties`
        """
        model = ConsentGrantProperties()
        if include_optional:
            return ConsentGrantProperties(
                connector_type = 'defender',
                ms_tenant_id = 'dda75ba7-c192-4bf3-8139-2d4b1hqbc6cc'
            )
        else:
            return ConsentGrantProperties(
                connector_type = 'defender',
                ms_tenant_id = 'dda75ba7-c192-4bf3-8139-2d4b1hqbc6cc',
        )
        """

    def testConsentGrantProperties(self):
        """Test ConsentGrantProperties"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
