# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_size import ConnectorSize

class TestConnectorSize(unittest.TestCase):
    """ConnectorSize unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testConnectorSize(self):
        """Test ConnectorSize"""
        # inst = ConnectorSize()

if __name__ == '__main__':
    unittest.main()
