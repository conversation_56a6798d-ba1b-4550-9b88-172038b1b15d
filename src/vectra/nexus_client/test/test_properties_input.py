# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.properties_input import PropertiesInput

class TestPropertiesInput(unittest.TestCase):
    """PropertiesInput unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> PropertiesInput:
        """Test PropertiesInput
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `PropertiesInput`
        """
        model = PropertiesInput()
        if include_optional:
            return PropertiesInput(
                connector_type = 'aws',
                resource_group_id = '/subscriptions/2a43c28d-hd6-afa6-2f9741cadf44/resourceGroups/rg-vectra-cdr',
                s3_bucket_name = 'mock-bucket',
                iam_role_arn = 'arn:aws:iam::123456789009:role/mock-bucket',
                sns_topic_arn = 'arn:aws:sns:us-west-2:123456789009:mock-bucket'
            )
        else:
            return PropertiesInput(
                connector_type = 'aws',
                resource_group_id = '/subscriptions/2a43c28d-hd6-afa6-2f9741cadf44/resourceGroups/rg-vectra-cdr',
                s3_bucket_name = 'mock-bucket',
                iam_role_arn = 'arn:aws:iam::123456789009:role/mock-bucket',
                sns_topic_arn = 'arn:aws:sns:us-west-2:123456789009:mock-bucket',
        )
        """

    def testPropertiesInput(self):
        """Test PropertiesInput"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
