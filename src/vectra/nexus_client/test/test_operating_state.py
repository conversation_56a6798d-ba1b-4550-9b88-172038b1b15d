# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.operating_state import OperatingState

class TestOperatingState(unittest.TestCase):
    """OperatingState unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testOperatingState(self):
        """Test OperatingState"""
        # inst = OperatingState()

if __name__ == '__main__':
    unittest.main()
