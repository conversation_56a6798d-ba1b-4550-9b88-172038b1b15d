# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.properties_update import PropertiesUpdate

class TestPropertiesUpdate(unittest.TestCase):
    """PropertiesUpdate unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> PropertiesUpdate:
        """Test PropertiesUpdate
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `PropertiesUpdate`
        """
        model = PropertiesUpdate()
        if include_optional:
            return PropertiesUpdate(
                crowdstrike_url = 'https://api.crowdstrike.com',
                sentinelone_url = 'https://usea1-partners.sentinelone.net'
            )
        else:
            return PropertiesUpdate(
                crowdstrike_url = 'https://api.crowdstrike.com',
                sentinelone_url = 'https://usea1-partners.sentinelone.net',
        )
        """

    def testPropertiesUpdate(self):
        """Test PropertiesUpdate"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
