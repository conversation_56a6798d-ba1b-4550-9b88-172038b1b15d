# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.sentinel_one_secret import SentinelOneSecret

class TestSentinelOneSecret(unittest.TestCase):
    """SentinelOneSecret unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SentinelOneSecret:
        """Test SentinelOneSecret
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SentinelOneSecret`
        """
        model = SentinelOneSecret()
        if include_optional:
            return SentinelOneSecret(
                secret_name = '',
                api_token = 'token-secret'
            )
        else:
            return SentinelOneSecret(
                api_token = 'token-secret',
        )
        """

    def testSentinelOneSecret(self):
        """Test SentinelOneSecret"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
