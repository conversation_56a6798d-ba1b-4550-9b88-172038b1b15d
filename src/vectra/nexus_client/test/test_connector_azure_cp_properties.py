# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_azure_cp_properties import ConnectorAzureCPProperties

class TestConnectorAzureCPProperties(unittest.TestCase):
    """ConnectorAzureCPProperties unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ConnectorAzureCPProperties:
        """Test ConnectorAzureCPProperties
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ConnectorAzureCPProperties`
        """
        model = ConnectorAzureCPProperties()
        if include_optional:
            return ConnectorAzureCPProperties(
                connector_type = 'azure-cp',
                resource_group_id = '/subscriptions/2a43c28d-hd6-afa6-2f9741cadf44/resourceGroups/rg-vectra-cdr'
            )
        else:
            return ConnectorAzureCPProperties(
                connector_type = 'azure-cp',
                resource_group_id = '/subscriptions/2a43c28d-hd6-afa6-2f9741cadf44/resourceGroups/rg-vectra-cdr',
        )
        """

    def testConnectorAzureCPProperties(self):
        """Test ConnectorAzureCPProperties"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
