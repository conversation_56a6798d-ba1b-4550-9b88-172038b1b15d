# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_secret_update import ConnectorSecretUpdate

class TestConnectorSecretUpdate(unittest.TestCase):
    """ConnectorSecretUpdate unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ConnectorSecretUpdate:
        """Test ConnectorSecretUpdate
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ConnectorSecretUpdate`
        """
        model = ConnectorSecretUpdate()
        if include_optional:
            return ConnectorSecretUpdate(
                connector_type = 'crowdstrike',
                secret = None,
                properties = None
            )
        else:
            return ConnectorSecretUpdate(
                connector_type = 'crowdstrike',
        )
        """

    def testConnectorSecretUpdate(self):
        """Test ConnectorSecretUpdate"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
