# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.consent_secret import ConsentSecret

class TestConsentSecret(unittest.TestCase):
    """ConsentSecret unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ConsentSecret:
        """Test ConsentSecret
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ConsentSecret`
        """
        model = ConsentSecret()
        if include_optional:
            return ConsentSecret(
                secret_name = '',
                client_id = '298342j382u34n83u2',
                client_secret = 'abcD1234EFghIjkLmNoPqRstUvWxYz567890'
            )
        else:
            return ConsentSecret(
                client_id = '298342j382u34n83u2',
                client_secret = 'abcD1234EFghIjkLmNoPqRstUvWxYz567890',
        )
        """

    def testConsentSecret(self):
        """Test ConsentSecret"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
