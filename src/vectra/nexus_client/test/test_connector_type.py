# coding: utf-8

"""
    Nexus

    Connector Management Service

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from vectra.nexus_client.models.connector_type import ConnectorType

class TestConnectorType(unittest.TestCase):
    """ConnectorType unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testConnectorType(self):
        """Test ConnectorType"""
        # inst = ConnectorType()

if __name__ == '__main__':
    unittest.main()
