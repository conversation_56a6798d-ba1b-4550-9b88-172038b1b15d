#!/usr/bin/env python3
"""
NLB Connection Timeout Test Script

This script tests for Network Load Balancer (NLB) connection timeouts by making repeated 
API requests to a Vectra Nexus API endpoint with deliberate long pauses between requests. 
It helps identify if and when the NLB disconnects idle connections.

Features:
- Runs multiple concurrent threads to test connection behavior
- Configurable sleep time between requests (default: 360 seconds / 6 minutes)
- Logs connection-related activities and errors
- Specifically identifies RemoteDisconnected errors that may indicate NLB timeouts

Usage:
    python connection_test_script.py [sleep_seconds]

Arguments:
    sleep_seconds - Optional: Time in seconds to sleep between requests (default: 360)

Example:
    python connection_test_script.py 300    # Test with 5-minute sleep intervals
"""
import concurrent.futures
import logging
import sys
import time
from datetime import datetime, timezone
from http.client import RemoteDisconnected

from urllib3.exceptions import ProtocolError

from vectra.nexus_client import ApiClient, Configuration, ConnectorsApi
from vectra.nexus_client.models.connector_update import ConnectorUpdate
from vectra.nexus_client.rest import ApiException

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

logging.getLogger("urllib3.connectionpool").setLevel(logging.INFO)


class ConnectionFilter(logging.Filter):
    def filter(self, record):
        msg = record.getMessage()
        return any(
            text in msg
            for text in [
                "Starting new HTTP connection",
                "connection established",
                "Keep-Alive",
                "Resetting dropped connection",
                "Connection pool closed",
                "Releasing connection",
            ]
        )


connection_logger = logging.getLogger("urllib3.connectionpool")
connection_logger.addFilter(ConnectionFilter())

NEXUS_URL = "https://nexus-uw2.ingestion.dev.vectra-svc.ai"
CONNECTOR_ID = "550e8400-e29b-41d4-a716-446655440009"

SLEEP = 360  # 6 minutes to test the NLB timeout


VECTRA_SERVICE = "vui"
BRAIN_ID = "test_azure_2"


def patch(thread_id):
    config = Configuration(host=NEXUS_URL)
    config.verify_ssl = False
    api_client = ApiClient(configuration=config)
    client = ConnectorsApi(api_client=api_client)

    request_count = 0
    while True:
        try:
            current_time = datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")
            update = ConnectorUpdate(last_log_received=current_time)
            print(f"Thread {thread_id}: Making request #{request_count}")
            _ = client.patch_connector_v1(
                connector_id=CONNECTOR_ID,
                connector_update=update,
                x_vectra_service=VECTRA_SERVICE,
                x_internal_brain_id=BRAIN_ID,
            )
            print(f"Thread {thread_id}: Request #{request_count} - Success")
            print(f"Thread {thread_id}: Sleeping for {SLEEP} seconds...")
            time.sleep(SLEEP)
            request_count += 1

        except (ProtocolError, ConnectionError) as e:
            if isinstance(e, ProtocolError) and isinstance(e.args[1], RemoteDisconnected):
                print(f"Thread {thread_id}: ✓ FOUND IT! RemoteDisconnected error: {e}")
            else:
                print(f"Thread {thread_id}: Connection error: {e}")
            time.sleep(5)
            request_count += 1

        except ApiException as e:
            print(f"Thread {thread_id}: API Exception: {e}")
            time.sleep(5)
            request_count += 1

        except Exception as e:
            print(f"Thread {thread_id}: Unexpected error: {e}")
            time.sleep(5)
            request_count += 1


if __name__ == "__main__":
    if len(sys.argv) > 1:
        try:
            SLEEP = int(sys.argv[1])
            print(f"Using sleep interval of {SLEEP} seconds")
        except ValueError:
            pass

    print(f"Starting NLB connection test with {SLEEP} second sleep interval")
    print(f"Nexus URL: {NEXUS_URL}")
    print(f"Connector ID: {CONNECTOR_ID}")
    print("Running 5 concurrent threads...")
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(patch, i) for i in range(5)]
        try:
            concurrent.futures.wait(futures)
        except KeyboardInterrupt:
            print("\nTest interrupted by user. Final results:")
